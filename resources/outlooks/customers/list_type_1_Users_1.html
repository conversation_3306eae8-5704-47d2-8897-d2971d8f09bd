<h1>{$title}</h1>
{if $subtitle}<h2>{$subtitle|escape}</h2>{/if}

<table border="0" cellpadding="0" cellspacing="0">
  {if $action eq 'filter'}
    {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=customers&amp;customers=filter&amp;{if $relation}relation={$relation}&amp;{/if}{if $event}event={$event}&amp;{/if}{if $mynzoom_settings_table}mynzoom_settings_table={$mynzoom_settings_table}&amp;{/if}{if $smarty.get.autocomplete_filter}autocomplete_filter=session&amp;{/if}{if $smarty.request.uniqid}uniqid={$smarty.request.uniqid}&amp;{/if}{if $session_param}session_param={$session_param}&amp;{/if}page={/capture}

  {else}
    <tr>
      <td class="pagemenu">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$controller}={$action}{if $type && is_numeric($type)}&amp;type={$type}{/if}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_stats=1
        }
      </td>
    </tr>
  {/if}
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="customers" action="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}" method="post" enctype="multipart/form-data">
      {if $action eq 'filter' && $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <style type="text/css">

      </style>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <th class="t_caption t_border t_checkall">
            {if $action eq 'filter'}
              {if !$autocomplete_params || $autocomplete_params.select_multiple}
                {include file="`$theme->templatesDir`_select_items.html"
                  pages=$pagination.pages
                  total=$pagination.total
                  session_param=$session_param|default:$pagination.session_param
                }
              {else}
                {assign var='hide_selection_stats' value=true}
              {/if}
            {else}
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
            {/if}
          </th>
          <th class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></th>
          <th class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name|default:#customers_name#|escape}</div></th>
          <th class="t_caption t_border {$sort.city.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.city.link} onclick="{$sort.city.link}"{/if}>{$basic_vars_labels.city|default:#customers_city#|escape}</div></th>
          <th class="t_caption t_border {$sort.skype.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.skype.link} onclick="{$sort.skype.link}"{/if}>{$basic_vars_labels.skype|default:#customers_skype#|escape}</div></th>
          <th class="t_caption t_border {$sort.department.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.department.link}">{$basic_vars_labels.department|default:#customers_department#|escape}</div></th>
          <th class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{$basic_vars_labels.type|default:#customers_type#|escape}</div></th>
          <th class="t_caption t_border {$sort.email.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.email.link} onclick="{$sort.email.link}"{/if}>{$basic_vars_labels.email|default:#customers_email#|escape}</div></th>
          <th class="t_caption t_border {$sort.is_company.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.is_company.link}">{#customers_company_person#|escape}</div></th>
          <th class="t_caption t_border {$sort.group.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.group.link}">{#customers_group#|escape}</div></th>
          <th class="t_caption t_border {$sort.a__position_name.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__position_name.link} onclick="{$sort.a__position_name.link}"{/if}>{$add_vars_labels.1.position_name}</div></th>
          <th class="t_caption t_border {$sort.a__working_to.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__working_to.link} onclick="{$sort.a__working_to.link}"{/if}>{$add_vars_labels.1.working_to}</div></th>
          <th class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{$basic_vars_labels.added|default:#added#|escape}</div></th>
          <th class="t_caption t_border {$sort.a__link_contact_persons_name.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__link_contact_persons_name.link} onclick="{$sort.a__link_contact_persons_name.link}"{/if}>{$add_vars_labels.1.link_contact_persons_name}</div></th>
          <th class="t_caption t_border {$sort.gsm.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.gsm.link} onclick="{$sort.gsm.link}"{/if}>{$basic_vars_labels.gsm|default:#customers_gsm#|escape}</div></th>
          <th class="t_caption t_border {$sort.num.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.num.link} onclick="{$sort.num.link}"{/if}>{$basic_vars_labels.num|default:#customers_num#|escape}</div></th>
          <th class="t_caption t_border {$sort.main_trademark.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.main_trademark.link} onclick="{$sort.main_trademark.link}"{/if}>{$basic_vars_labels.main_trademark|default:#customers_main_trademark#|escape}</div></th>
          <th class="t_caption t_border {$sort.tags.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.tags.link} onclick="{$sort.tags.link}"{/if}>{$basic_vars_labels.tags|default:#customers_tags#|escape}</div></th>
          <th class="t_caption t_border {$sort.phone.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.phone.link} onclick="{$sort.phone.link}"{/if}>{$basic_vars_labels.phone|default:#customers_phone#|escape}</div></th>
          <th class="t_caption t_border {$sort.web.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.web.link} onclick="{$sort.web.link}"{/if}>{$basic_vars_labels.web|default:#customers_web#|escape}</div></th>
          <th class="t_caption t_border {$sort.a__abilitys.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__abilitys.link} onclick="{$sort.a__abilitys.link}"{/if}>{$add_vars_labels.1.abilitys}</div></th>
          <th class="t_caption t_border {$sort.a__ticket_email_from.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__ticket_email_from.link} onclick="{$sort.a__ticket_email_from.link}"{/if}>{$add_vars_labels.1.ticket_email_from}</div></th>
          <th class="t_caption t_border {$sort.othercontact.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.othercontact.link} onclick="{$sort.othercontact.link}"{/if}>{$basic_vars_labels.othercontact|default:#customers_othercontact#|escape}</div></th>
          <th class="t_caption t_border {$sort.ucn.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.ucn.link} onclick="{$sort.ucn.link}"{/if}>{$basic_vars_labels.ucn|default:#customers_ucn#|escape}</div></th>
          <th class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name_code|default:#customers_name_code#|escape}</div></th>
          <th class="t_caption t_border {$sort.comments.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.comments.link} onclick="{$sort.comments.link}"{/if}>{$basic_vars_labels.comments|default:#customers_comments#|escape}</div></th>
          <th class="t_caption t_border {$sort.a__working_from.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__working_from.link} onclick="{$sort.a__working_from.link}"{/if}>{$add_vars_labels.1.working_from}</div></th>
          <th class="t_caption t_border {$sort.emails.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.emails.link} onclick="{$sort.emails.link}"{/if}>{$basic_vars_labels.emails|default:#customers_emails#|escape}</div></th>

          <th class="t_caption">&nbsp;</th>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {array assign='background_colors'}
      {foreach name='i' from=$customers item='single'}
      {strip}
      {assign var='salutation' value=''}
      {if !$single->get('is_company') && $single->get('salutation')}
        {assign var='layout_salutation' value=$single->getLayoutsDetails('salutation')}
        {if $layout_salutation.view}
          {capture assign='salutation'}salutation_{$single->get('salutation')}{/capture}
          {capture assign='salutation'}{$smarty.config.$salutation|escape} {/capture}
        {/if}
      {/if}
      {capture assign='info'}
        <strong>{$basic_vars_labels.name|default:#customers_name#|escape}:</strong> {$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}<br />
        <strong>{$basic_vars_labels.type|default:#customers_type#|escape}:</strong> {$single->get('type_name')|escape} ({if $single->get('is_company')}{#customers_company#|escape}{else}{#customers_person#|escape}{/if})<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />
        {if $single->isDeleted()}<strong>{#deleted#|escape}:</strong> {$single->get('deleted')|date_format:#date_mid#|escape}{if $single->get('deleted_by_name')} {#by#|escape} {$single->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$single->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {/strip}
      {include file="`$theme->templatesDir`row_link_action.html" object=$single assign='row_link'}
      {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
      <div id="rf{$single->get('id')}" style="display: none">{$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}</div>
      {if $single->modelName != 'Event' && !$single->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$single->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">
            {counter name='item_counter' print=true}
          </td>
          <td colspan="26" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td>
            {include file=`$theme->templatesDir`single_actions_list.html object=$single disabled='edit,delete,view'}
          </td>
        </tr>
      {else}
        <tr {if !$background_style}class="{cycle values='t_odd,t_even'}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('archived_by')} attention{/if}{if $single->get('deleted_by')} t_deleted{/if}{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} {if $single->modelName eq 'Contract'}strike{else}t_strike{/if}{/if}{if $single->get('severity')} {$single->get('severity')}{/if}"{else}class="t_row{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} t_strike{/if}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('deleted_by')} t_deleted{/if}"{if $single->isDefined('active') && $single->get('active') || !$single->isDefined('active')} {$background_style}{/if}{/if}>
          <td class="t_border">
            {if $action eq 'filter'}
              {if $autocomplete_params && !$autocomplete_params.select_multiple}
                <input type="checkbox" name='items[]' value="{$single->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
              {else}
                <input type="checkbox"
                       onclick="setCheckAllBox(params = {ldelim}
                                                the_element: this,
                                                module: '{$module}',
                                                controller: '{$controller}',
                                                action: '{$action}',
                                                button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                               {rdelim});"
                       name='items[]'
                       value="{$single->get('id')}{if $module eq 'customers' && $relation}_{if $single->get('is_company')}company{else}person{/if}{/if}"
                       title="{#check_to_include#|escape}" />
              {/if}
            {else}
              <input onclick="sendIds(params = {ldelim}
                                              the_element: this,
                                              module: '{$module}',
                                              controller: '{$controller}',
                                              action: '{$action}',
                                              session_param: '{$session_param|default:$pagination.session_param}',
                                              total: {$pagination.total}
                                             {rdelim});"
                     type="checkbox"
                     name='items[]'
                     value="{$single->get('id')}"
                     title="{#check_to_include#|escape}"
                     {if @in_array($single->get('id'), $selected_items.ids) ||
                         (@$selected_items.select_all eq 1 && @!in_array($single->get('id'), $selected_items.ignore_ids))}
                       checked="checked"
                     {/if} />
            {/if}
          </td>
          <td class="t_border hright" nowrap="nowrap">
            {if $single->get('files_count')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}&amp;{$controller}{else}&amp;{$module}{/if}=attachments&amp;attachments={$single->get('id')}{if $single->get('archived_by')}&amp;archive=1{/if}">
               <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$module}', '{$controller}', {$single->get('id')}{if $single->get('archived_by')}, '', 1{/if})"
                     onmouseout="mclosetime()" />
              </a>
            {/if}
            {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$single->get('id')}">{$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}</a></td>
          <td class="t_border {$sort.city.isSorted} {$row_link_class}"{$row_link}>{$single->get('city')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.skype.isSorted} {$row_link_class}"{$row_link}>
          {if is_array($single->get('skype'))}
            {foreach from=$single->get('skype') item='v' name='vi'}
              {if $v}{$v|escape}{if !$smarty.foreach.vi.last}<br />{/if}{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          {else}
            &nbsp;
          {/if}
          </td>
          <td class="t_border {$sort.department.isSorted} {$row_link_class}"{$row_link}>{$single->get('department_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.type.isSorted} {$row_link_class}"{$row_link}>{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.email.isSorted}{if !$single->get('email')} {$row_link_class}{/if}"{if !$single->get('email')} {$row_link}{/if}>
            {if is_array($single->get('email'))}
              {foreach from=$single->get('email') item='email' name='cdi'}
                <a href="mailto:{$email}" target="_self">{$email|escape|default:"&nbsp;"}</a>{if !$smarty.foreach.cdi.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="t_border {$sort.is_company.isSorted} {$row_link_class}"{$row_link}>{if $single->get('is_company')}{#customers_company#|escape}{else}{#customers_person#|escape}{/if}</td>
          <td class="t_border {$sort.group.isSorted} {$row_link_class}"{$row_link}>{$single->get('group_name')|escape|default:"&nbsp;"}</td>
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', 'position_name')}position_name{/if}{/capture}
          {capture assign='var_value'}{$single->getVarValue('position_name', 0)}{/capture}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.position_name}{/capture}
          {capture assign='var_row_link'}{if !$asterisk_contact && !preg_match(str_replace(' ', '', '#(< a\s|onclick=)#'), $var_value)} {$row_link}{/if}{/capture}
          {capture assign='content'}{if $asterisk_contact}{if $var_value}{include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1' number=$var_value}{/if}{else}{$var_value}{/if}{/capture}
          {capture assign='content'}{if $content !== ''}{$content}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          {assign var='long_content' value=false}
          {capture assign='var_type'}autocompleter{/capture}
          {if $var_type eq 'textarea' &&  $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('add_var_position_name', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('add_var_position_name', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="add_var_position_name_part_{$single_id}"><span{$var_row_link}>{$content|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="add_var_position_name_full_{$single_id}" style="display: none;"><span{$var_row_link}>{$content|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|nl2br|url2href}{/capture}
          {/if}
          <td class="t_border {if is_numeric($var_value)} hright{/if} {$sort.a__position_name.isSorted}{if $var_row_link} {$row_link_class}{/if}" {if !$long_content}{$var_row_link}{/if}>{$content}</td>
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', 'working_to')}working_to{/if}{/capture}
          {capture assign='var_value'}{$single->getVarValue('working_to', 0)}{/capture}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.working_to}{/capture}
          {capture assign='var_row_link'}{if !$asterisk_contact && !preg_match(str_replace(' ', '', '#(< a\s|onclick=)#'), $var_value)} {$row_link}{/if}{/capture}
          {capture assign='content'}{if $asterisk_contact}{if $var_value}{include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1' number=$var_value}{/if}{else}{$var_value}{/if}{/capture}
          {capture assign='content'}{if $content !== ''}{$content}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          {assign var='long_content' value=false}
          {capture assign='var_type'}time{/capture}
          {if $var_type eq 'textarea' &&  $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('add_var_working_to', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('add_var_working_to', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="add_var_working_to_part_{$single_id}"><span{$var_row_link}>{$content|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="add_var_working_to_full_{$single_id}" style="display: none;"><span{$var_row_link}>{$content|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|nl2br|url2href}{/capture}
          {/if}
          <td class="t_border {if is_numeric($var_value)} hright{/if} {$sort.a__working_to.isSorted}{if $var_row_link} {$row_link_class}{/if}" {if !$long_content}{$var_row_link}{/if}>{$content}</td>
          <td class="t_border {$sort.added.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>{$single->get('added')|date_format:#date_short#|escape}</td>
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', 'link_contact_persons_name')}link_contact_persons_name{/if}{/capture}
          {capture assign='var_value'}{$single->getVarValue('link_contact_persons_name', 0)}{/capture}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.link_contact_persons_name}{/capture}
          {capture assign='var_row_link'}{if !$asterisk_contact && !preg_match(str_replace(' ', '', '#(< a\s|onclick=)#'), $var_value)} {$row_link}{/if}{/capture}
          {capture assign='content'}{if $asterisk_contact}{if $var_value}{include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1' number=$var_value}{/if}{else}{$var_value}{/if}{/capture}
          {capture assign='content'}{if $content !== ''}{$content}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          {assign var='long_content' value=false}
          {capture assign='var_type'}autocompleter{/capture}
          {if $var_type eq 'textarea' &&  $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('add_var_link_contact_persons_name', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('add_var_link_contact_persons_name', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="add_var_link_contact_persons_name_part_{$single_id}"><span{$var_row_link}>{$content|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="add_var_link_contact_persons_name_full_{$single_id}" style="display: none;"><span{$var_row_link}>{$content|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|nl2br|url2href}{/capture}
          {/if}
          <td class="t_border {if is_numeric($var_value)} hright{/if} {$sort.a__link_contact_persons_name.isSorted}{if $var_row_link} {$row_link_class}{/if}" {if !$long_content}{$var_row_link}{/if}>{$content}</td>
          <td class="t_border {$sort.gsm.isSorted}{if !$use_asterisk} {$row_link_class}{/if}"{if !$use_asterisk} {$row_link}{/if}>
            {foreach from=$single->get('gsm') item='gsm' name='cdi'}
              {if $use_asterisk && $gsm}
                {include file=`$theme->templatesDir`_asterisk_contact.html contact_type='gsm' number=$gsm label=$smarty.config.customers_gsm}
              {else}
                {$gsm|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last}<br />{/if}
              {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td class="t_border {$sort.num.isSorted} {$row_link_class}"{$row_link}>{$single->get('num')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.main_trademark.isSorted} {$row_link_class}"{$row_link}>{$single->get('main_trademark_name')|escape|default:"&nbsp;"}</td>
          {strip}
            {if preg_match('#^Finance_.*$#i', $single->modelName)}
              {assign var='_module' value='finance'}
              {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
            {else}
              {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
              {capture assign='_controller'}{/capture}
            {/if}
          {/strip}
          <td class="t_border {$sort.tags.isSorted}" {if $single->getModelTags() && $single->get('available_tags_count') gt 0 && $single->checkPermissions('tags_view') && $single->checkPermissions('tags_edit')} onclick="changeTags({$single->get('id')}, '{$_module}', '{$_controller}'{if $redirect_to_url && $update_target}, '{$redirect_to_url}' + ($$('#{$update_target} .page_menu_current_page').length ? $$('#{$update_target} .page_menu_current_page')[0].innerHTML : 1){/if})" style="cursor: pointer;" title="{#tags_change#|escape}"{/if}>
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="t_border {$sort.phone.isSorted}{if !$use_asterisk} {$row_link_class}{/if}"{if !$use_asterisk} {$row_link}{/if}>
            {foreach from=$single->get('phone') item='phone' name='cdi'}
              {if $use_asterisk && $phone}
                {include file=`$theme->templatesDir`_asterisk_contact.html contact_type='phone' number=$phone label=$smarty.config.customers_phone}
              {else}
                {$phone|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last}<br />{/if}
              {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td class="t_border {$sort.web.isSorted}{if !$single->get('web')} {$row_link_class}{/if}"{if !$single->get('web')} {$row_link}{/if}>
            {if is_array($single->get('web'))}
              {foreach from=$single->get('web') item='web' name='cdi'}
                <a href="http://{$web}" target="_blank">{$web|escape|default:"&nbsp;"}</a>{if !$smarty.foreach.cdi.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', 'abilitys')}abilitys{/if}{/capture}
          {capture assign='var_value'}{$single->getVarValue('abilitys', 0)}{/capture}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.abilitys}{/capture}
          {capture assign='var_row_link'}{if !$asterisk_contact && !preg_match(str_replace(' ', '', '#(< a\s|onclick=)#'), $var_value)} {$row_link}{/if}{/capture}
          {capture assign='content'}{if $asterisk_contact}{if $var_value}{include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1' number=$var_value}{/if}{else}{$var_value}{/if}{/capture}
          {capture assign='content'}{if $content !== ''}{$content}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          {assign var='long_content' value=false}
          {capture assign='var_type'}textarea{/capture}
          {if $var_type eq 'textarea' &&  $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('add_var_abilitys', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('add_var_abilitys', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="add_var_abilitys_part_{$single_id}"><span{$var_row_link}>{$content|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="add_var_abilitys_full_{$single_id}" style="display: none;"><span{$var_row_link}>{$content|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|nl2br|url2href}{/capture}
          {/if}
          <td class="t_border {if is_numeric($var_value)} hright{/if} {$sort.a__abilitys.isSorted}{if $var_row_link} {$row_link_class}{/if}" {if !$long_content}{$var_row_link}{/if}>{$content}</td>
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', 'ticket_email_from')}ticket_email_from{/if}{/capture}
          {capture assign='var_value'}{$single->getVarValue('ticket_email_from', 0)}{/capture}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.ticket_email_from}{/capture}
          {capture assign='var_row_link'}{if !$asterisk_contact && !preg_match(str_replace(' ', '', '#(< a\s|onclick=)#'), $var_value)} {$row_link}{/if}{/capture}
          {capture assign='content'}{if $asterisk_contact}{if $var_value}{include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1' number=$var_value}{/if}{else}{$var_value}{/if}{/capture}
          {capture assign='content'}{if $content !== ''}{$content}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          {assign var='long_content' value=false}
          {capture assign='var_type'}text{/capture}
          {if $var_type eq 'textarea' &&  $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('add_var_ticket_email_from', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('add_var_ticket_email_from', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="add_var_ticket_email_from_part_{$single_id}"><span{$var_row_link}>{$content|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="add_var_ticket_email_from_full_{$single_id}" style="display: none;"><span{$var_row_link}>{$content|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|nl2br|url2href}{/capture}
          {/if}
          <td class="t_border {if is_numeric($var_value)} hright{/if} {$sort.a__ticket_email_from.isSorted}{if $var_row_link} {$row_link_class}{/if}" {if !$long_content}{$var_row_link}{/if}>{$content}</td>
          <td class="t_border {$sort.othercontact.isSorted} {$row_link_class}"{$row_link}>
          {if is_array($single->get('othercontact'))}
            {foreach from=$single->get('othercontact') item='v' name='vi'}
              {if $v}{$v|escape}{if !$smarty.foreach.vi.last}<br />{/if}{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          {else}
            &nbsp;
          {/if}
          </td>
          <td class="t_border {$sort.ucn.isSorted} {$row_link_class}"{$row_link}>{$single->get('ucn')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module ne $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$action_param}=view&amp;view={$single->get('id')}"{if $link_target} target="{$link_target}"{/if}>&#91;{$single->get('code')|escape|default:"&nbsp;"}&#93; {$single->get('name')|escape|default:"&nbsp;"}{if $single->get('lastname')} {$single->get('lastname')}{/if}</a></td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td class="t_border {$sort.comments.isSorted}">
            <div class="t_occupy_cell has_inline_add hright comments comments_{$single->modelName|lower}_{$single->get('id')}{if $single->get('comments') gt 0 && $single->checkPermissions('comments')} pointer" onmouseenter="showCommunicationsInfo(this, 'comments', '{$_module}', '{$_controller}', {$single->get('id')}, '{$single->get('archived_by')}')" onmouseleave="mclosetime()" onclick="openHref('{$smarty.server.SCRIPT_NAME}?{$module_param}={$_module}{if $_controller ne $_module}&amp;controller={$_controller}{/if}&amp;{$_controller}=communications&amp;communications={$single->get('id')}&amp;communication_type=comments{if $single->get('archived_by')}&amp;archive=1{/if}', '_self', event){/if}">
              <span class="comments_total">{if $single->get('comments')}{$single->get('comments')}{/if}</span>
            </div>
            {if $single->checkPermissions('comments_add')}
            <div class="inline_add" onmouseover="Event.stop(event)">
              <a href="#" onclick="return loadInlineAddCommunication(event, 'comment', '{$_module}', '{$_controller}', {$single->get('id')});" title="{#add_comment#|escape}"></a>
            </div>
            {/if}
          </td>
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', 'working_from')}working_from{/if}{/capture}
          {capture assign='var_value'}{$single->getVarValue('working_from', 0)}{/capture}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.working_from}{/capture}
          {capture assign='var_row_link'}{if !$asterisk_contact && !preg_match(str_replace(' ', '', '#(< a\s|onclick=)#'), $var_value)} {$row_link}{/if}{/capture}
          {capture assign='content'}{if $asterisk_contact}{if $var_value}{include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1' number=$var_value}{/if}{else}{$var_value}{/if}{/capture}
          {capture assign='content'}{if $content !== ''}{$content}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          {assign var='long_content' value=false}
          {capture assign='var_type'}time{/capture}
          {if $var_type eq 'textarea' &&  $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('add_var_working_from', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('add_var_working_from', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="add_var_working_from_part_{$single_id}"><span{$var_row_link}>{$content|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="add_var_working_from_full_{$single_id}" style="display: none;"><span{$var_row_link}>{$content|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|nl2br|url2href}{/capture}
          {/if}
          <td class="t_border {if is_numeric($var_value)} hright{/if} {$sort.a__working_from.isSorted}{if $var_row_link} {$row_link_class}{/if}" {if !$long_content}{$var_row_link}{/if}>{$content}</td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td class="t_border {$sort.emails.isSorted}">
            <div class="t_occupy_cell has_inline_add hright emails emails_{$single->modelName|lower}_{$single->get('id')}{if $single->get('emails') gt 0 && $single->checkPermissions('emails')} pointer" onmouseenter="showCommunicationsInfo(this, 'emails', '{$_module}', '{$_controller}', {$single->get('id')}, '{$single->get('archived_by')}')" onmouseleave="mclosetime()" onclick="openHref('{$smarty.server.SCRIPT_NAME}?{$module_param}={$_module}{if $_controller ne $_module}&amp;controller={$_controller}{/if}&amp;{$_controller}=communications&amp;communications={$single->get('id')}&amp;communication_type=emails{if $single->get('archived_by')}&amp;archive=1{/if}', '_self', event){/if}">
              <span class="emails_total">{if $single->get('emails')}{$single->get('emails')}{/if}</span>
            </div>
            {if $single->checkPermissions('emails_add')}
            <div class="inline_add" onmouseover="Event.stop(event)">
              <a href="#" onclick="return loadInlineAddCommunication(event, 'email', '{$_module}', '{$_controller}', {$single->get('id')});" title="{#add_email#|escape}"></a>
            </div>
            {/if}
          </td>

          <td class="hcenter" nowrap="nowrap">
            {if $action eq 'filter'}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single exclude="edit, view, delete"}
            {else}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single}
            {/if}
          </td>
        </tr>
      {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="29">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="29"></td>
        </tr>
      </table>
      {if $action eq 'filter'}
        <br />
        {strip}
          {if $smarty.request.autocomplete_filter}
  {if $autocomplete_params.select_multiple}
    <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: false{rdelim});">{#select#|escape}</button>
  {/if}
  <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: true{rdelim});">{#select#|escape} &amp; {#close#|escape}</button>
{else}
  <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim} return confirmAction('link', function(el) {ldelim}{if !$relation}updateReferers(el.form, 0){else}updateCustomersRelations(el.form, '{$relation}', 0){/if};{rdelim}, this, '{#confirm_link_customers#|escape:'quotes'|escape}'); {rdelim}else{ldelim}alert('{#alert_link_customers#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape}</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim} return confirmAction('link', function(el) {ldelim}{if !$relation}updateReferers(el.form, 1){else}updateCustomersRelations(el.form, '{$relation}', 1){/if};{rdelim}, this, '{#confirm_link_customers#|escape:'quotes'|escape}'); {rdelim}else{ldelim}alert('{#alert_link_customers#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape} &amp; {#close#|escape}</button>
{/if}

        {/strip}
      {else}
        {if ('')}
          {include file="`$theme->templatesDir`_severity_legend.html" prefix=''}
        {/if}
        <br />
        {include file=`$theme->templatesDir`multiple_actions_list.html exclude='' include='multiprint,tags' session_param=$session_param|default:$pagination.session_param}
      {/if}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
  {if $background_colors}
    {include file="`$theme->templatesDir`_invoices_reasons_legend.html"}
  {/if}
</table>
