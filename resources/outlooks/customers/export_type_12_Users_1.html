    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#customers_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.eik|default:#customers_eik#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.city|default:#customers_city#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.address|default:#customers_address#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.phone|default:#customers_phone#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.gsm|default:#customers_gsm#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.email|default:#customers_email#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.12.type_delivery}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#customers_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.type|default:#customers_type#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.admit_VAT_credit|default:#customers_admit_VAT_credit#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.skype|default:#customers_skype#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.registration_address|default:#customers_registration_address#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.notes|default:#customers_notes#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name_code|default:#customers_name_code#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.main_trademark|default:#customers_main_trademark#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.mol|default:#customers_mol#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.modified|default:#customers_modified#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.modified_by|default:#customers_modified_by#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.code|default:#customers_code#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.12.acc_code}</th>
          <th nowrap="nowrap">{$basic_vars_labels.company_name|default:#customers_company_name#|escape}</th>

        </tr>
      {foreach name='i' from=$customers item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="25-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('eik')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('city')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('address')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('phone') item='phone' name='cdi'}
              {$phone|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('gsm') item='gsm' name='cdi'}
              {$gsm|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>
          <td{if !$single->get('email')} {/if}>
            {if is_array($single->get('email'))}
              {foreach from=$single->get('email') item='email' name='cdi'}
                {$email|escape|default:"&nbsp;"}{if !$smarty.foreach.cdi.last}, {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          {assign var='var_values' value=$single->getSelectedAddVarCheckboxLabels('type_delivery')}
          {capture assign='var_back_label'}{$add_vars_back_labels.12.type_delivery}{/capture}
          <td style="mso-number-format: \@;">
            {foreach from=$var_values item='checkbox_value' name='cb_val'}
              {if trim($checkbox_value) !== ''}
                {$checkbox_value|nl2br|url2href|default:"&nbsp;"}
              {else}
                  &#x2713;
              {/if}
              {if !$smarty.foreach.cb_val.last}, {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
            {if $var_values}{include file="_back_label.html" back_label=$var_back_label}{/if}
          </td>
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td nowrap="nowrap">
            {if $single->get('admit_VAT_credit')}{#yes#}{else}{#no#}{/if}
          </td>
          <td style="mso-number-format: \@;">
          {if is_array($single->get('skype'))}
            {foreach from=$single->get('skype') item='v' name='vi'}
              {if $v}{$v|escape}{if !$smarty.foreach.vi.last}, {/if}{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          {else}
            &nbsp;
          {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('registration_address')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('notes')|escape|nl2br|url2href|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">&#91;{$single->get('code')|escape|default:"&nbsp;"}&#93; {$single->get('name')|escape|default:"&nbsp;"}{if $single->get('lastname')} {$single->get('lastname')}{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('main_trademark_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('mol')|escape|default:"&nbsp;"}</td>
          <td nowrap="nowrap">{$single->get('modified')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('modified_by_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('code')|escape|default:"&nbsp;"}</td>
          {capture assign='var_value'}{$single->getVarValue('acc_code')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.12.acc_code}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
          <td style="mso-number-format: \@;">{$single->get('company_name')|escape|default:"&nbsp;"}</td>
          

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="25">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
