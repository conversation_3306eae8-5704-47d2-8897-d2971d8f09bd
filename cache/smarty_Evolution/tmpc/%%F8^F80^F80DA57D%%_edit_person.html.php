<?php /* Smarty version 2.6.33, created on 2025-06-25 10:05:48
         compiled from /var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_edit_person.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_edit_person.html', 30, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_edit_person.html', 90, false),array('modifier', 'indent', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_edit_person.html', 166, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_edit_person.html', 278, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_edit_person.html', 278, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_edit_person.html', 57, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_edit_person.html', 90, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_edit_person.html', 90, false),)), $this); ?>
      <?php if (empty ( $this->_tpl_vars['available_actions'] )): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layouts_index.html', 'smarty_include_vars' => array('display' => 'abs_div')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
      <?php $this->assign('super_layout_offset', '0'); ?>
      <?php $_from = $this->_tpl_vars['customer']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>

      <?php if ($this->_tpl_vars['layout']['place'] > $this->_tpl_vars['super_layout_offset']): ?>
        <?php if ($this->_tpl_vars['layout']['place'] <= @PH_CUSTOMERS_LAYOUTS_MAIN_TO): ?>
          <?php $this->assign('super_layout_offset', @PH_CUSTOMERS_LAYOUTS_MAIN_TO); ?>
          <?php $this->assign('super_layout', 'main_data'); ?>
          <?php $this->assign('super_layout_class', 'customers_main_data'); ?>
        <?php elseif ($this->_tpl_vars['layout']['place'] <= @PH_CUSTOMERS_LAYOUTS_ADDRESS_TO): ?>
          <?php $this->assign('super_layout_offset', @PH_CUSTOMERS_LAYOUTS_ADDRESS_TO); ?>
          <?php $this->assign('super_layout', 'contact_data'); ?>
          <?php $this->assign('super_layout_class', 'customers_contact_data'); ?>
        <?php elseif ($this->_tpl_vars['layout']['place'] <= @PH_CUSTOMERS_LAYOUTS_REG_TO): ?>
          <?php $this->assign('super_layout_offset', @PH_CUSTOMERS_LAYOUTS_REG_TO); ?>
          <?php $this->assign('super_layout', 'personal_data'); ?>
          <?php $this->assign('super_layout_class', 'customers_personal_data'); ?>
        <?php else: ?>
          <?php $this->assign('super_layout', ''); ?>
          <?php $this->assign('super_layout_class', ''); ?>
        <?php endif; ?>
        <?php $this->assign('super_layout_cookie', ''); ?>
        <?php if ($this->_tpl_vars['super_layout'] && $this->_tpl_vars['customer']->get('super_layouts') && in_array ( $this->_tpl_vars['super_layout'] , $this->_tpl_vars['customer']->get('super_layouts') )): ?>
        <tr>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
            </div>
            <div class="layout_switch" onclick="toggleViewLayouts(this)" id="customers_<?php echo $this->_tpl_vars['super_layout']; ?>
_switch">
              <?php ob_start(); ?>customers_<?php echo $this->_tpl_vars['super_layout']; ?>
_box<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('super_layout_cookie', ob_get_contents());ob_end_clean(); ?>
              <?php ob_start(); ?>customers_<?php if ($this->_tpl_vars['super_layout'] == 'contact_data'): ?>address_data<?php else: ?><?php echo $this->_tpl_vars['super_layout']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('super_layout_name', ob_get_contents());ob_end_clean(); ?>
              <a name="customer_<?php echo $this->_tpl_vars['super_layout']; ?>
_index"></a><div class="switch_<?php if ($_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['super_layout_name']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
            </div>
          </td>
        </tr>
        <?php endif; ?>
      <?php endif; ?>

        <?php if ($this->_tpl_vars['layout']['system'] || $this->_tpl_vars['layout']['view'] && array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view'] && $this->_tpl_vars['layout']['visible']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?><?php if (! $this->_tpl_vars['layout']['view'] || ! $this->_tpl_vars['layout']['visible'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
            </div>
            <div class="layout_switch" <?php if ($this->_tpl_vars['layout']['system']): ?>onclick="toggleViewLayouts(this)" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch"<?php else: ?>onclick="toggleLayouts(this)" id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_switch"<?php endif; ?>>
              <a name="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
            </div>
          </td>
        </tr>
        <?php endif; ?>

        <?php if ($this->_tpl_vars['lkey'] == 'salutation'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_salutation"><label for="salutation"<?php if ($this->_tpl_vars['messages']->getErrors('salutation')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('name' => 'salutation','options' => $this->_tpl_vars['salutations'],'value' => $this->_tpl_vars['customer']->get('salutation'),'width' => 80,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php else: ?>
              <?php $_from = $this->_tpl_vars['salutations']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['salutation']):
?>
              <?php if ($this->_tpl_vars['salutation']['option_value'] == $this->_tpl_vars['customer']->get('salutation')): ?>
                <input type="hidden" name="salutation" id="salutation" value="<?php echo $this->_tpl_vars['salutation']['option_value']; ?>
" />
                <?php echo ((is_array($_tmp=$this->_tpl_vars['salutation']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td colspan="3" class="nopadding">
            <table border="0" cellspacing="0" cellpadding="0" class="t_layout_table">
              <tr>
                <td class="labelbox" style="width: 180px!important"><a name="error_name"><label for="name"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'name'), $this);?>
</label></a></td>
                <td class="required" style="width: 10px!important"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                <?php if ($this->_tpl_vars['layout']['edit']): ?>
                  <input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                <?php else: ?>
                  <input type="hidden" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('info', ob_get_contents()); ob_end_clean();
 ?>
                  <span <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
><?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>
</span>
                <?php endif; ?>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_lastname"><label for="lastname"<?php if ($this->_tpl_vars['messages']->getErrors('lastname')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'lastname'), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                <?php if ($this->_tpl_vars['layout']['edit']): ?>
                  <input type="text" class="txtbox" name="lastname" id="lastname" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_lastname'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                <?php else: ?>
                  <input type="hidden" name="lastname" id="lastname" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                <?php endif; ?>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'type'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_type"><label for="type"<?php if ($this->_tpl_vars['messages']->getErrors('type')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <input type="hidden" name="type" id="type" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('type'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
            <input type="hidden" name="type_name" id="type_name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'is_company'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_is_company"><label for="is_company"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['customer']->get('is_company')): ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php else: ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
            <input type="hidden" name="is_company" id="is_company" value="<?php echo $this->_tpl_vars['customer']->get('is_company'); ?>
" />
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'code'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_code"><label for="code"<?php if ($this->_tpl_vars['messages']->getErrors('code')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="code" id="code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="code" id="code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'num'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_num"><label for="num"<?php if ($this->_tpl_vars['messages']->getErrors('num')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <input type="hidden" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" name="num" id="num" />
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_department"><label for="department"<?php if ($this->_tpl_vars['messages']->getErrors('department')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <?php ob_start(); ?><?php echo $this->_tpl_vars['customer']->get('department'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_department', ob_get_contents());ob_end_clean(); ?>
              <select name="department" id="department" class="selbox<?php if (! $this->_tpl_vars['departments']): ?> missing_records<?php elseif (! $this->_tpl_vars['current_department']): ?> undefined<?php endif; ?>" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this); changeAssigningOptions(this, 'assigned');" onkeypress="dropdownTypingSearch(this, event);" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                <?php if ($this->_tpl_vars['departments']): ?>
                  <option value="" class="undefined"<?php if (! $this->_tpl_vars['current_department']): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                  <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['department']):
?>
                    <?php if (! $this->_tpl_vars['department']->get('deleted_by') && ( $this->_tpl_vars['department']->get('active') || $this->_tpl_vars['department']->get('id') === $this->_tpl_vars['current_department'] )): ?>
                      <option value="<?php echo $this->_tpl_vars['department']->get('id'); ?>
"<?php if ($this->_tpl_vars['department']->get('id') == $this->_tpl_vars['current_department']): ?> selected="selected"<?php endif; ?><?php if (! $this->_tpl_vars['department']->get('active')): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
" disabled="disabled"<?php endif; ?>><?php if (! $this->_tpl_vars['department']->get('active')): ?>*&nbsp;<?php endif; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['department']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['department']->get('level'), '-') : smarty_modifier_indent($_tmp, $this->_tpl_vars['department']->get('level'), '-')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                    <?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                <?php else: ?>
                  <option value="" class="missing_records"<?php if (! $this->_tpl_vars['current_department']): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_select_records'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                <?php endif; ?>
              </select>
            <?php else: ?>
              <input type="hidden" name="department" id="department" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('department'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'assigned'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_assigned"><label for="assigned"<?php if ($this->_tpl_vars['messages']->getErrors('assigned')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <?php ob_start(); ?><?php echo $this->_tpl_vars['customer']->get('assigned'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_assigned', ob_get_contents());ob_end_clean(); ?>
              <select name="assigned" id="assigned" class="selbox<?php if (! $this->_tpl_vars['department_users']): ?> missing_records<?php elseif (! $this->_tpl_vars['current_assigned']): ?> undefined<?php endif; ?>" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                <?php if ($this->_tpl_vars['department_users']): ?>
                  <option value="" class="undefined"<?php if (! $this->_tpl_vars['current_assigned']): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                <?php else: ?>
                  <option value="" class="missing_records"<?php if (! $this->_tpl_vars['current_assigned']): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_select_records'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                <?php endif; ?>
                <?php $_from = $this->_tpl_vars['department_users']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['dep_user']):
?>
                  <option value="<?php echo $this->_tpl_vars['dep_user']['id']; ?>
"<?php if ($this->_tpl_vars['dep_user']['id'] == $this->_tpl_vars['current_assigned']): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['dep_user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                <?php endforeach; endif; unset($_from); ?>
              </select>
            <?php else: ?>
              <input type="hidden" name="assigned" id="assigned" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('assigned'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('assigned_to_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'company_department'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_company_department"><label for="company_department"<?php if ($this->_tpl_vars['messages']->getErrors('company_department')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="company_department" id="company_department" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('company_department'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="company_department" id="company_department" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('company_department'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('company_department'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'position'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_position"><label for="position"<?php if ($this->_tpl_vars['messages']->getErrors('position')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="position" id="position" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('position'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="position" id="position" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('position'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('position'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'country'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_country"><label for="country"<?php if ($this->_tpl_vars['messages']->getErrors('country')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <select class="selbox<?php if (! $this->_tpl_vars['customer']->get('country')): ?> undefined<?php endif; ?>" name="country" id="country" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                <option value="" class="undefined"<?php if (! $this->_tpl_vars['customer']->get('country')): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                <?php $_from = $this->_tpl_vars['countries']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['country']):
?>
                  <option value="<?php echo $this->_tpl_vars['country']['option_value']; ?>
"<?php if ($this->_tpl_vars['customer']->get('country') == $this->_tpl_vars['country']['option_value']): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['country']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                <?php endforeach; endif; unset($_from); ?>
              </select>
            <?php else: ?>
              <input type="hidden" name="country" id="country" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('country'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('country_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'city'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_city"><label for="city"<?php if ($this->_tpl_vars['messages']->getErrors('city')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="city" id="city" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('city'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="city" id="city" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('city'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('city'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'postal_code'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_postal_code"><label for="postal_code"<?php if ($this->_tpl_vars['messages']->getErrors('postal_code')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="postal_code" id="postal_code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('postal_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="postal_code" id="postal_code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('postal_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('postal_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'address'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_address"><label for="address"<?php if ($this->_tpl_vars['messages']->getErrors('address')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="address" id="address" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (! $this->_tpl_vars['layout']['edit']): ?> style="display: none;"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('address'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
            <?php if (! $this->_tpl_vars['layout']['edit']): ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('address'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_notes"><label for="notes"<?php if ($this->_tpl_vars['messages']->getErrors('notes')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="notes" id="notes" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (! $this->_tpl_vars['layout']['edit']): ?> style="display: none;"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
            <?php if (! $this->_tpl_vars['layout']['edit']): ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'contacts'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox" colspan="3">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_contact_data.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['customer'],'predefined_contact_params' => $this->_tpl_vars['customer']->getPredefinedContactParameters())));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'ucn'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_ucn"><label for="ucn"<?php if ($this->_tpl_vars['messages']->getErrors('ucn')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="ucn" id="ucn" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('ucn'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="ucn" id="ucn" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('ucn'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('ucn'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'identity_num'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_identity_num"><label for="identity_num"<?php if ($this->_tpl_vars['messages']->getErrors('identity_num')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="identity_num" id="identity_num" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('identity_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="identity_num" id="identity_num" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('identity_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('identity_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'identity_date'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_identity_date"><label for="identity_date"<?php if ($this->_tpl_vars['messages']->getErrors('identity_date')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php ob_start(); ?><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('required', ob_get_contents());ob_end_clean(); ?>
            <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('readonly', ob_get_contents());ob_end_clean(); ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_date.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'identity_date','label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['customer']->get('identity_date'),'show_calendar_icon' => 1,'readonly' => $this->_tpl_vars['readonly'],'required' => $this->_tpl_vars['required'],'width' => 200)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'identity_by'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_identity_by"><label for="identity_by"<?php if ($this->_tpl_vars['messages']->getErrors('identity_by')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="identity_by" id="identity_by" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('identity_by'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="identity_by" id="identity_by" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('identity_by'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('identity_by'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'identity_valid'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_identity_valid"><label for="identity_valid"<?php if ($this->_tpl_vars['messages']->getErrors('identity_valid')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php ob_start(); ?><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('required', ob_get_contents());ob_end_clean(); ?>
            <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('readonly', ob_get_contents());ob_end_clean(); ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_date.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'identity_valid','label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['customer']->get('identity_valid'),'show_calendar_icon' => 1,'readonly' => $this->_tpl_vars['readonly'],'required' => $this->_tpl_vars['required'],'width' => 200)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'address_by_personal_id'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_address_by_personal_id"><label for="address_by_personal_id"<?php if ($this->_tpl_vars['messages']->getErrors('address_by_personal_id')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="address_by_personal_id" id="address_by_personal_id" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (! $this->_tpl_vars['layout']['edit']): ?> style="display: none;"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('address_by_personal_id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
            <?php if (! $this->_tpl_vars['layout']['edit']): ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('address_by_personal_id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'in_dds'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_in_dds"><label for="in_dds"<?php if ($this->_tpl_vars['messages']->getErrors('in_dds')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="in_dds" id="in_dds" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('in_dds'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/refresh.png" class="icon_button pointer vmiddle" width="14" height="14" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_refresh_person_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_refresh_person_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" onclick="return confirmAction('refresh_company_info', function() { getCompanyInfoByVat(); }, this);" />
            <?php else: ?>
              <input type="hidden" name="in_dds" id="in_dds" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('in_dds'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('in_dds'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'bank'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_bank"><label for="bank"<?php if ($this->_tpl_vars['messages']->getErrors('bank')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="bank" id="bank" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('bank'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="bank" id="bank" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('bank'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('bank'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'iban'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_iban"><label for="iban"<?php if ($this->_tpl_vars['messages']->getErrors('iban')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="iban" id="iban" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('iban'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="iban" id="iban" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('iban'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('iban'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'bic'): ?>
        <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><a name="error_bic"><label for="bic"<?php if ($this->_tpl_vars['messages']->getErrors('bic')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['layout']['edit']): ?>
              <input type="text" class="txtbox" name="bic" id="bic" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('bic'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <?php else: ?>
              <input type="hidden" name="bic" id="bic" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('bic'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('bic'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['layout']['view'] && array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
        <!-- Customer Additional Vars -->
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_manage_vars.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <?php echo ''; ?><?php if ($this->_tpl_vars['customer']->get('buttons')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['customer']->get('buttons'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_button.html", 'smarty_include_vars' => array('label' => $this->_tpl_vars['button']['label'],'standalone' => true,'name' => $this->_tpl_vars['button']['name'],'source' => $this->_tpl_vars['button']['source'],'disabled' => $this->_tpl_vars['button']['disabled'],'hidden' => $this->_tpl_vars['button']['hidden'],'width' => $this->_tpl_vars['button']['width'],'height' => $this->_tpl_vars['button']['height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo '<button type="submit" name="saveButton1" class="button">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?>

          </td>
        </tr>
      </table>