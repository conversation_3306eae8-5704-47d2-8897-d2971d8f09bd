<?php /* Smarty version 2.6.33, created on 2025-06-25 10:05:48
         compiled from input_time.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'input_time.html', 64, false),array('modifier', 'date_format', 'input_time.html', 85, false),array('modifier', 'strip_tags', 'input_time.html', 89, false),array('modifier', 'escape', 'input_time.html', 89, false),array('function', 'help', 'input_time.html', 66, false),)), $this); ?>
<?php if ($this->_tpl_vars['index']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['name_index']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>
<?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['standalone']): ?><?php echo ''; ?><?php if (preg_match ( '#^(\d+%|)$#' , $this->_tpl_vars['width'] )): ?><?php echo '100%'; ?><?php elseif (is_numeric ( $this->_tpl_vars['width'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['width']; ?><?php echo 'px'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['height'] && ! preg_match ( '#%$#' , $this->_tpl_vars['height'] )): ?><?php echo $this->_tpl_vars['height']; ?>
px<?php elseif ($this->_tpl_vars['height']): ?><?php echo $this->_tpl_vars['height']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
<tr<?php if ($this->_tpl_vars['hidden']): ?> style="display: none"<?php endif; ?>>
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
        <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
  </td>

    <td<?php if ($this->_tpl_vars['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap">
<?php endif; ?>

        <input
      type="text"
      name="<?php echo $this->_tpl_vars['name']; ?>
_formatted<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_formatted<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      class="timebox txtbox<?php if ($this->_tpl_vars['readonly']): ?> readonly<?php endif; ?><?php if (! $this->_tpl_vars['width']): ?> short<?php endif; ?><?php if ($this->_tpl_vars['custom_class']): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>"
      style="<?php if ($this->_tpl_vars['hidden']): ?>display: none;<?php elseif ($this->_tpl_vars['width']): ?>width: <?php echo $this->_tpl_vars['width']; ?>
!important;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?>height: <?php echo $this->_tpl_vars['height']; ?>
;<?php endif; ?>"
      autocomplete="off"
      <?php if ($this->_tpl_vars['value'] && preg_match ( '#\d\d?:\d\d?#' , $this->_tpl_vars['value'] )): ?>
        value="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['value'])) ? $this->_run_mod_handler('date_format', true, $_tmp, '%H:%M') : smarty_modifier_date_format($_tmp, '%H:%M')))) ? $this->_run_mod_handler('default', true, $_tmp, '  :  ') : smarty_modifier_default($_tmp, '  :  ')); ?>
"
      <?php else: ?>
        value="  :  "
      <?php endif; ?>
      title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      onfocus="highlight(this); datetimePositionMouseCursor(this);"
      onblur="unhighlight(this); validateTime(this, -1); formatDate(this);<?php if (! empty ( $this->_tpl_vars['js_methods']['onblur'] )): ?><?php echo $this->_tpl_vars['js_methods']['onblur']; ?>
<?php endif; ?>"
      <?php if ($this->_tpl_vars['readonly']): ?> 
        readonly="readonly"
      <?php else: ?>
        onkeydown="return isAllowedDateKey(event);<?php if (! empty ( $this->_tpl_vars['js_methods']['onkeydown'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeydown']; ?>
<?php endif; ?>"
        onkeyup="return changeKey(this, event, filterDate);<?php if (! empty ( $this->_tpl_vars['js_methods']['onkeyup'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeyup']; ?>
<?php endif; ?>"
      <?php endif; ?>
      <?php if ($this->_tpl_vars['disabled']): ?> disabled="disabled"<?php endif; ?> />
      
    <input
      type="hidden"
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      class="timebox txtbox hidden<?php if ($this->_tpl_vars['custom_class']): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>"
      <?php if ($this->_tpl_vars['onchange'] || ! empty ( $this->_tpl_vars['js_methods']['onchange'] )): ?>onchange="<?php if ($this->_tpl_vars['onchange']): ?><?php echo $this->_tpl_vars['onchange']; ?>
<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onchange'] )): ?><?php echo $this->_tpl_vars['js_methods']['onchange']; ?>
<?php endif; ?>"<?php endif; ?>
      <?php if ($this->_tpl_vars['value'] && preg_match ( '#\d\d?:\d\d?#' , $this->_tpl_vars['value'] )): ?>
        value="<?php echo ((is_array($_tmp=$this->_tpl_vars['value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      <?php else: ?>
        value=""
      <?php endif; ?>
    />

        <?php if (! $this->_tpl_vars['back_label'] && $this->_tpl_vars['var']['back_label']): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! $this->_tpl_vars['back_label_style'] && $this->_tpl_vars['var']['back_label_style']): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => $this->_tpl_vars['custom_id'],'name' => $this->_tpl_vars['name'],'back_label' => $this->_tpl_vars['back_label'],'back_label_style' => $this->_tpl_vars['back_label_style'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>