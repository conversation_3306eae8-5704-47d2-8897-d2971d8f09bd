<?php /* Smarty version 2.6.33, created on 2025-06-25 10:05:48
         compiled from input_table.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', 'input_table.html', 14, false),)), $this); ?>
<tr<?php if ($this->_tpl_vars['hidden_all']): ?> style="display: none;"<?php endif; ?>><td colspan="3"><?php if (trim ( $this->_tpl_vars['var']['label'] )): ?><div class="t_caption2_title"><?php echo $this->_tpl_vars['var']['label']; ?>
</div><?php endif; ?>
<?php if (trim ( $this->_tpl_vars['var']['javascript'] )): ?>
<script type="text/javascript">
    <?php echo $this->_tpl_vars['var']['javascript']; ?>

</script>
<?php endif; ?>
    <div class="nz-view-wrapper">
<table<?php if ($this->_tpl_vars['hidden_all']): ?> style="display: none;"<?php endif; ?> class="t_grouping_table<?php if ($this->_tpl_vars['var']['borderless']): ?> t_borderless<?php endif; ?><?php if ($this->_tpl_vars['var']['custom_class']): ?> <?php echo $this->_tpl_vars['var']['custom_class']; ?>
<?php endif; ?>"<?php if ($this->_tpl_vars['var']['t_width']): ?> width="<?php echo $this->_tpl_vars['var']['t_width']; ?>
"<?php endif; ?> id="var_table_<?php echo $this->_tpl_vars['var']['table']; ?>
">
  <tr>
    <?php $_from = $this->_tpl_vars['var']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['label']):
?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]): ?><?php echo $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]; ?>
<?php else: ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
      <th<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?> style="display: none;"<?php endif; ?>>
        <div<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['key']]): ?> style="width: <?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['key']]; ?>
px;"<?php endif; ?>>
          <a name="error_<?php echo $this->_tpl_vars['var']['names'][$this->_tpl_vars['key']]; ?>
"></a><label for="<?php echo $this->_tpl_vars['var']['names'][$this->_tpl_vars['key']]; ?>
"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['var']['names'][$this->_tpl_vars['key']])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['info']), $this);?>
<?php if ($this->_tpl_vars['var']['required'][$this->_tpl_vars['key']]): ?> <?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></label>
        </div>
      </th>
    <?php endforeach; endif; unset($_from); ?>
  </tr>
  <tr>
    <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
?>
    <?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'formula'): ?>
      <?php $this->assign('formula_value', $this->_tpl_vars['var']['formula'][$this->_tpl_vars['key']]['value']); ?>
    <?php else: ?>
      <?php $this->assign('formula_value', $this->_tpl_vars['var']['index'][$this->_tpl_vars['key']]['formula']); ?>
    <?php endif; ?>
    <td<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?> style="display: none;"<?php endif; ?>>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']]).".html", 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']],'value_id' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['value_id'],'var_id' => $this->_tpl_vars['var']['ids'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'description' => $this->_tpl_vars['var']['descriptions'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label_style'],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'calculate' => $this->_tpl_vars['var']['calculate'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'autocomplete' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['name']],'origin' => 'table','height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'options' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options'],'optgroups' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups'],'on_change' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['on_change'],'onchange' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['onchange'],'sequences' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['sequences'],'really_required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'view_mode' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['view_mode'],'thumb_width' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width'],'thumb_height' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height'],'width' => $this->_tpl_vars['var']['width'][$this->_tpl_vars['key']],'formulas' => $this->_tpl_vars['formulas'],'formula_value' => $this->_tpl_vars['formula_value'],'source' => $this->_tpl_vars['var']['formula'][$this->_tpl_vars['key']]['source'],'indexes' => $this->_tpl_vars['indexes'],'date_value' => $this->_tpl_vars['var']['index'][$this->_tpl_vars['key']]['date'],'options_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'deleteid' => $this->_tpl_vars['var']['deleteids'][$this->_tpl_vars['key']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['show_placeholder'],'text_align' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align'],'custom_class' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
    <?php endforeach; endif; unset($_from); ?>
  </tr>
</table>
    </div>
</td></tr>