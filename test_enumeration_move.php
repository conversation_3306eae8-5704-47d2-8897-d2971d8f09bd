<?php

// Test script to verify that enumeration column has been moved from Excel adapter to ModelTableProvider

require_once '_libs/inc/common/registry.class.php';
require_once '_libs/tests/Nzoom/TestHelpers/GlobalMocks.php';
require_once '_libs/Nzoom/Export/Provider/ExportTableProviderInterface.php';
require_once '_libs/Nzoom/Export/Provider/ModelTableProvider.php';
require_once '_libs/Nzoom/Export/Entity/ExportTable.php';
require_once '_libs/Nzoom/Export/Entity/ExportTableCollection.php';
require_once '_libs/Nzoom/Export/Entity/ExportHeader.php';
require_once '_libs/Nzoom/Export/Entity/ExportColumn.php';
require_once '_libs/Nzoom/Export/Entity/ExportRecord.php';
require_once '_libs/Nzoom/Export/Entity/ExportValue.php';



echo "Testing enumeration column move from Excel adapter to ModelTableProvider...\n\n";

// Create registry
$registry = new Registry();

// Create ModelTableProvider
$provider = new \Nzoom\Export\Provider\ModelTableProvider($registry, 'full_num', 'Full Num');

// Create mock model with proper grouping data
$varsForTemplate = [
    'test_table' => [
        'type' => 'grouping',
        'names' => ['item_name', 'quantity', 'price'],
        'labels' => ['Item Name', 'Quantity', 'Price'],
        'hidden' => [],
        'types' => ['text', 'text', 'text'],
        'values' => [
            ['Item 1', '10', '100.00'],
            ['Item 2', '5', '50.00'],
            ['Item 3', '2', '25.00']
        ]
    ]
];

$model = new Model(
    ['id' => 1, 'full_num' => 'DOC-001'], // data
    [], // exportValues
    [], // exportTypes
    $varsForTemplate // varsForTemplate
);

// Test with first record
echo "=== Testing Record 1 ===\n";
$tableCollection1 = $provider->getTablesForRecord($model);
echo "Number of tables found: " . $tableCollection1->count() . "\n";

foreach ($tableCollection1->getTables() as $table) {
    echo "\nTable: " . $table->getTableName() . "\n";
    echo "Table Type: " . $table->getTableType() . "\n";

    $header = $table->getHeader();
    $columns = $header->getColumns();

    echo "Columns (" . count($columns) . "):\n";
    foreach ($columns as $index => $column) {
        echo "  " . ($index + 1) . ". " . $column->getVarName() . " (" . $column->getLabel() . ") - " . $column->getType() . "\n";
    }

    echo "Records (" . $table->count() . "):\n";
    foreach ($table->getRecords() as $recordIndex => $record) {
        echo "  Record " . ($recordIndex + 1) . ":\n";
        $values = $record->getValues();
        foreach ($values as $valueIndex => $exportValue) {
            $column = $columns[$valueIndex] ?? null;
            $columnName = $column ? $column->getVarName() : "Column $valueIndex";
            echo "    $columnName: " . $exportValue->getValue() . "\n";
        }
    }
}

// Test with second record to verify enumeration restarts
echo "\n=== Testing Record 2 (should restart enumeration) ===\n";
$varsForTemplate2 = [
    'test_table' => [
        'type' => 'grouping',
        'names' => ['item_name', 'quantity', 'price'],
        'labels' => ['Item Name', 'Quantity', 'Price'],
        'hidden' => [],
        'types' => ['text', 'text', 'text'],
        'values' => [
            ['Item A', '20', '200.00'],
            ['Item B', '15', '150.00']
        ]
    ]
];

$model2 = new Model(
    ['id' => 2, 'full_num' => 'DOC-002'], // data
    [], // exportValues
    [], // exportTypes
    $varsForTemplate2 // varsForTemplate
);

$tableCollection2 = $provider->getTablesForRecord($model2);
echo "Number of tables found: " . $tableCollection2->count() . "\n";

foreach ($tableCollection2->getTables() as $table) {
    echo "\nTable: " . $table->getTableName() . "\n";
    echo "Records (" . $table->count() . "):\n";
    foreach ($table->getRecords() as $recordIndex => $record) {
        echo "  Record " . ($recordIndex + 1) . ":\n";
        $values = $record->getValues();
        $header = $table->getHeader();
        $columns = $header->getColumns();
        foreach ($values as $valueIndex => $exportValue) {
            $column = $columns[$valueIndex] ?? null;
            $columnName = $column ? $column->getVarName() : "Column $valueIndex";
            echo "    $columnName: " . $exportValue->getValue() . "\n";
        }
    }
}

echo "\nTest completed!\n";
echo "\nExpected results:\n";
echo "- Each table should have reference column as first column (full_num)\n";
echo "- Each table should have enumeration column as second column (#)\n";
echo "- Record 1: Enumeration values should be 1, 2, 3 for the three records\n";
echo "- Record 2: Enumeration values should restart at 1, 2 for the two records\n";
echo "- Data columns should follow after enumeration column\n";
