<?php

namespace Tests\Nzoom\Export\Provider;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Provider\ModelTableProvider;
use Nzoom\Export\Entity\ExportValue;

/**
 * Test that ModelTableProvider gracefully handles unsupported field types
 */
class ModelTableProviderUnsupportedFieldTest extends ExportTestCase
{
    private ModelTableProvider $provider;
    private RegistryMock $registry;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../../TestHelpers/GlobalMocks.php';

        $this->registry = new RegistryMock();
        $this->provider = new ModelTableProvider($this->registry, 'test_ref', 'Test Ref');
    }

    public function testUnsupportedFieldTypeInHeaderCreation(): void
    {
        // Create template variables with grouping data
        $varsForTemplate = [
            'test_table' => [
                'type' => 'grouping',
                'names' => ['col1', 'col2', 'col3'],
                'labels' => ['Column 1', 'Column 2', 'Column 3'],
                'hidden' => [],
                'types' => ['text', 'unsupported_field_type', 'date'],
                'values' => [
                    ['value1', 'value2', '2024-01-01']
                ]
            ]
        ];

        $model = new \Model(
            ['id' => 123, 'test_ref' => 'REF123'],
            [],
            [],
            $varsForTemplate
        );

        // Get tables - should not throw exception and should skip unsupported column
        $tableCollection = $this->provider->getTablesForRecord($model);

        $this->assertNotNull($tableCollection);
        $this->assertTrue($tableCollection->hasTables());

        $tables = $tableCollection->getTables();
        $this->assertCount(1, $tables);

        $table = reset($tables);
        $header = $table->getHeader();

        // Should have reference column + enumeration column + 2 supported columns (col1 and col3)
        // col2 with unsupported type should be skipped
        $this->assertCount(4, $header->getColumns()); // reference + enumeration + col1 + col3

        // Check that the expected columns are present in the header
        $this->assertTrue($header->hasColumn('test_ref'), 'test_ref column should be present in header');
        $this->assertTrue($header->hasColumn('#'), 'enumeration column should be present in header');
        $this->assertTrue($header->hasColumn('col1'), 'col1 column should be present in header');
        $this->assertTrue($header->hasColumn('col3'), 'col3 column should be present in header');
        $this->assertFalse($header->hasColumn('col2'), 'col2 column should be skipped due to unsupported type');
    }

    public function testUnsupportedFieldTypeInRecordCreation(): void
    {
        // Create template variables with GT2 grouping data
        $varsForTemplate = [
            'gt2_table' => [
                'type' => 'gt2',
                'vars' => [
                    'col1' => ['type' => 'text', 'label' => 'Column 1', 'position' => 1],
                    'col2' => ['type' => 'unsupported_field_type', 'label' => 'Column 2', 'position' => 2],
                    'col3' => ['type' => 'date', 'label' => 'Column 3', 'position' => 3]
                ],
                'values' => [
                    'row1' => ['col1' => 'value1', 'col2' => 'value2', 'col3' => '2024-01-01']
                ]
            ]
        ];

        $model = new \Model(
            ['id' => 123, 'test_ref' => 'REF123'],
            [],
            [],
            $varsForTemplate
        );

        // Get tables - should not throw exception and should skip unsupported column
        $tableCollection = $this->provider->getTablesForRecord($model);

        $this->assertNotNull($tableCollection);
        $this->assertTrue($tableCollection->hasTables());

        $tables = $tableCollection->getTables();
        $this->assertCount(1, $tables);

        $table = reset($tables);
        $records = $table->getRecords();
        $this->assertCount(1, $records);

        $record = reset($records);
        $values = $record->getValues();

        // Should have reference column + enumeration column + 2 supported columns (col1 and col3)
        // col2 with unsupported type should be skipped
        $this->assertCount(4, $values); // reference + enumeration + col1 + col3

        // Check that the expected columns are present
        $this->assertTrue($record->hasValue('test_ref'), 'test_ref column should be present');
        $this->assertTrue($record->hasValue('#'), 'enumeration column should be present');
        $this->assertTrue($record->hasValue('col1'), 'col1 column should be present');
        $this->assertTrue($record->hasValue('col3'), 'col3 column should be present');
        $this->assertFalse($record->hasValue('col2'), 'col2 column should be skipped due to unsupported type');

        // Verify the values
        $this->assertEquals('REF123', $record->getValueByColumnName('test_ref')->getValue());
        $this->assertEquals('value1', $record->getValueByColumnName('col1')->getValue());

        // Date value should be converted to DateTime object
        $dateValue = $record->getValueByColumnName('col3')->getValue();
        $this->assertInstanceOf(\DateTime::class, $dateValue);
        $this->assertEquals('2024-01-01', $dateValue->format('Y-m-d'));
    }

    public function testConvertFieldTypeToValueTypeThrowsException(): void
    {
        // Use reflection to test the private method directly
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('convertFieldTypeToValueType');
        $method->setAccessible(true);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported field type: unsupported_type');

        $method->invoke($this->provider, 'unsupported_type');
    }

    public function testSupportedFieldTypesWork(): void
    {
        // Use reflection to test the private method directly
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('convertFieldTypeToValueType');
        $method->setAccessible(true);

        $supportedTypes = [
            'text' => ExportValue::TYPE_STRING,
            'textarea' => ExportValue::TYPE_STRING,
            'dropdown' => ExportValue::TYPE_STRING,
            'radio' => ExportValue::TYPE_STRING,
            'checkbox_group' => ExportValue::TYPE_STRING,
            'config' => ExportValue::TYPE_STRING,
            'button' => ExportValue::TYPE_STRING,
            'autocompleter' => ExportValue::TYPE_STRING,
            'time' => ExportValue::TYPE_STRING,
            'date' => ExportValue::TYPE_DATE,
            'datetime' => ExportValue::TYPE_DATETIME,
        ];

        foreach ($supportedTypes as $fieldType => $expectedExportType) {
            $result = $method->invoke($this->provider, $fieldType);
            $this->assertEquals($expectedExportType, $result, "Field type '$fieldType' should map to '$expectedExportType'");
        }
    }
}
