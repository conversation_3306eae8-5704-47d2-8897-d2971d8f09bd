<?php

namespace Tests\Nzoom\Export;

use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\ExportService;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Factory\ExportFormatFactory;
use Nzoom\Export\Adapter\ExportFormatAdapterInterface;
use Nzoom\I18n\I18n;



/**
 * Mock Logger for testing
 */
class MockLogger
{
    public $errors = [];

    public function error($message)
    {
        $this->errors[] = $message;
    }
}

/**
 * Mock Session for ExportService testing
 */
class MockExportSession
{
    public function get($key)
    {
        return null;
    }
}

/**
 * Mock User for ExportService testing
 */
class MockExportUser
{
    private $id;

    public function __construct($id = 123)
    {
        $this->id = $id;
    }

    public function get($key)
    {
        if ($key === 'id') {
            return $this->id;
        }
        return null;
    }
}

/**
 * Mock Export Adapter for testing
 */
class MockExportAdapter implements ExportFormatAdapterInterface
{
    private $exportCalled = false;
    private $lastExportData = null;
    private $lastOptions = null;
    private $configuration = [];

    public function export($output, string $type, ExportData $data, array $options = []): void
    {
        $this->exportCalled = true;
        $this->lastExportData = $data;
        $this->lastOptions = $options;

        // Write some test data to the output
        if (is_resource($output)) {
            fwrite($output, "Mock export data for type: {$type}");
        }
    }

    public static function getSupportedExtensions(): array
    {
        return ['mock'];
    }

    public function getMimeType(): string
    {
        return 'application/octet-stream';
    }

    public function getDefaultExtension(): string
    {
        return 'mock';
    }

    public static function supportsFormat(string $format): bool
    {
        return $format === 'mock';
    }

    public function getFormatOptions(): array
    {
        return ['test_option' => 'Test Option'];
    }

    public function setConfiguration(array $config): self
    {
        $this->configuration = $config;
        return $this;
    }

    public function getFormatName(): string
    {
        return 'mock';
    }

    // Test helper methods
    public function wasExportCalled(): bool
    {
        return $this->exportCalled;
    }

    public function getLastExportData(): ?ExportData
    {
        return $this->lastExportData;
    }

    public function getLastOptions(): ?array
    {
        return $this->lastOptions;
    }

    public function getConfiguration(): array
    {
        return $this->configuration;
    }
}

/**
 * Mock Format Factory for testing
 */
class MockFormatFactory extends ExportFormatFactory
{
    private $mockAdapter;

    public function __construct($mockAdapter = null)
    {
        $this->mockAdapter = $mockAdapter ?: new MockExportAdapter();
    }

    public function createAdapter(string $format, array $options = []): ExportFormatAdapterInterface
    {
        if (!$this->isFormatSupported($format)) {
            throw new \InvalidArgumentException("Unsupported format: {$format}");
        }
        return $this->mockAdapter;
    }

    public function getSupportedFormats(): array
    {
        return ['mock', 'csv', 'xlsx'];
    }

    public function isFormatSupported(string $format): bool
    {
        return MockExportAdapter::supportsFormat($format) || in_array($format, ['csv', 'xlsx']);
    }

    public function getMockAdapter(): MockExportAdapter
    {
        return $this->mockAdapter;
    }
}

/**
 * Test case for ExportService
 */
class ExportServiceTest extends ExportTestCase
{
    private ExportService $service;
    private RegistryMock $registry;
    private $translator;
    private MockLogger $logger;
    private MockFormatFactory $formatFactory;
    private MockExportAdapter $mockAdapter;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../TestHelpers/GlobalMocks.php';

        // Create mock dependencies
        $this->translator = $this->createMock(I18n::class);
        $this->translator->method('translate')->willReturnCallback(function($key, $params = []) {
            return $key; // Simple pass-through for testing
        });

        $this->logger = new MockLogger();
        $this->mockAdapter = new MockExportAdapter();
        $this->formatFactory = new MockFormatFactory($this->mockAdapter);

        // Create registry mock
        $this->registry = new RegistryMock();
        $this->registry->set('translater', $this->translator);
        $this->registry->set('logger', $this->logger);

        // Add session mock for ExportActionFactory
        $sessionMock = new MockExportSession();
        $this->registry->set('session', $sessionMock);

        // Add currentUser mock for DataFactory
        $userMock = new MockExportUser(123);
        $this->registry->set('currentUser', $userMock);

        // Create service instance
        $this->service = new ExportService($this->registry, 'test_module', 'test_controller', 'mock');
    }

    public function testConstructor(): void
    {
        $service = new ExportService($this->registry, 'users', 'list', 'csv');
        $this->assertInstanceOf(ExportService::class, $service);
    }

    public function testSetModelName(): void
    {
        $result = $this->service->setModelName('TestModel');
        $this->assertSame($this->service, $result); // Test fluent interface
    }

    public function testSetModelFactoryName(): void
    {
        $result = $this->service->setModelFactoryName('TestFactory');
        $this->assertSame($this->service, $result); // Test fluent interface
    }

    public function testGetFormatFactory(): void
    {
        $factory = $this->service->getFormatFactory();
        $this->assertInstanceOf(ExportFormatFactory::class, $factory);
        
        // Should return the same instance on subsequent calls
        $factory2 = $this->service->getFormatFactory();
        $this->assertSame($factory, $factory2);
    }

    public function testSetFormatFactory(): void
    {
        $result = $this->service->setFormatFactory($this->formatFactory);
        $this->assertSame($this->service, $result); // Test fluent interface
        
        $retrievedFactory = $this->service->getFormatFactory();
        $this->assertSame($this->formatFactory, $retrievedFactory);
    }

    public function testGetAdapter(): void
    {
        $this->service->setFormatFactory($this->formatFactory);
        
        $adapter = $this->service->getAdapter();
        $this->assertInstanceOf(ExportFormatAdapterInterface::class, $adapter);
        $this->assertSame($this->mockAdapter, $adapter);
        
        // Should return the same instance on subsequent calls
        $adapter2 = $this->service->getAdapter();
        $this->assertSame($adapter, $adapter2);
    }

    public function testGetSupportedFormats(): void
    {
        $this->service->setFormatFactory($this->formatFactory);
        
        $formats = $this->service->getSupportedFormats();
        $this->assertIsArray($formats);
        $this->assertContains('mock', $formats);
        $this->assertContains('csv', $formats);
        $this->assertContains('xlsx', $formats);
    }

    public function testIsFormatSupported(): void
    {
        $this->service->setFormatFactory($this->formatFactory);
        
        $this->assertTrue($this->service->isFormatSupported('mock'));
        $this->assertTrue($this->service->isFormatSupported('csv'));
        $this->assertFalse($this->service->isFormatSupported('unsupported'));
    }

    public function testCreateExportData(): void
    {
        $outlook = new \Outlook();
        $filters = ['where' => ['active = 1']];

        $exportData = $this->service->createExportData($outlook, $filters, 'TestFactory');

        $this->assertInstanceOf(ExportData::class, $exportData);
    }

    public function testCreateExportDataWithTables(): void
    {
        $outlook = new \Outlook();
        $filters = ['where' => ['active = 1']];

        $exportData = $this->service->createExportDataWithTables($outlook, $filters, 'Documents');

        $this->assertInstanceOf(ExportData::class, $exportData);
    }

    public function testGetReferenceColumnForModel(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getReferenceColumnForModel');
        $method->setAccessible(true);

        // Test known model classes (singular form)
        $result = $method->invoke($this->service, 'Document');
        $this->assertEquals(['name' => 'full_num', 'label' => 'Document Number'], $result);

        $result = $method->invoke($this->service, 'Customer');
        $this->assertEquals(['name' => 'code', 'label' => 'Customer Code'], $result);
    }

    public function testGetReferenceColumnForModelThrowsExceptionForUnknownModel(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getReferenceColumnForModel');
        $method->setAccessible(true);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No reference column mapping found for model class: UnknownModel');

        $method->invoke($this->service, 'UnknownModel');
    }

    public function testCreateGeneratorFileStreamer(): void
    {
        $generatorFunction = function() {
            yield 'test data';
        };

        // Don't pass totalSize to avoid the addHeader bug in GeneratorFileStreamer
        $streamer = $this->service->createGeneratorFileStreamer(
            $generatorFunction,
            'test.txt',
            'text/plain'
        );

        $this->assertInstanceOf(\Nzoom\Export\Streamer\GeneratorFileStreamer::class, $streamer);
    }

    public function testCreateExportAction(): void
    {
        $this->service->setModelName('TestModel');
        $this->service->setModelFactoryName('TestFactory');
        
        $result = $this->service->createExportAction('test_module', [1, 2], [3, 4]);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('options', $result);
        $this->assertArrayHasKey('ajax_no', $result);
    }

    private function createTestExportData(): ExportData
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        
        $record1 = new ExportRecord();
        $record1->addValue('id', 1, ExportValue::TYPE_INTEGER);
        $record1->addValue('name', 'Test User 1', ExportValue::TYPE_STRING);
        
        $record2 = new ExportRecord();
        $record2->addValue('id', 2, ExportValue::TYPE_INTEGER);
        $record2->addValue('name', 'Test User 2', ExportValue::TYPE_STRING);
        
        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record1);
        $exportData->addRecord($record2);

        return $exportData;
    }

    public function testExportSuccess(): void
    {
        $this->service->setFormatFactory($this->formatFactory);
        $exportData = $this->createTestExportData();

        // Capture output to prevent actual streaming
        ob_start();

        try {
            // Mock the exit behavior by catching the exception that would be thrown
            // when the streamer tries to exit
            $this->service->export('test.mock', $exportData, ['test_option' => 'value']);
        } catch (\Exception $e) {
            // Expected - the streamer will try to exit
        }

        ob_end_clean();

        // Verify the adapter was called
        $this->assertTrue($this->mockAdapter->wasExportCalled());
        $this->assertSame($exportData, $this->mockAdapter->getLastExportData());
        $this->assertEquals(['test_option' => 'value'], $this->mockAdapter->getLastOptions());
    }

    public function testExportWithInvalidFormat(): void
    {
        // Create a service with an unsupported format
        $service = new ExportService($this->registry, 'test', 'test', 'unsupported');
        $service->setFormatFactory($this->formatFactory);

        $exportData = $this->createTestExportData();

        // Should handle the error gracefully
        $service->export('test.unsupported', $exportData);

        // Check that error was logged
        $this->assertNotEmpty($this->logger->errors);
        $this->assertStringContainsString('Unsupported export format', $this->logger->errors[0]);

        // Check that ajax_result was set
        $ajaxResult = $this->registry->get('ajax_result');
        $this->assertNotNull($ajaxResult);
        $this->assertStringContainsString('error', $ajaxResult);
    }

    public function testGetExportFilenameWithString(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getExportFilename');
        $method->setAccessible(true);

        $filename = $method->invoke($this->service, 'custom_name', 'csv');
        $this->assertEquals('custom_name.csv', $filename);
    }

    public function testGetExportFilenameWithArray(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getExportFilename');
        $method->setAccessible(true);

        $filename = $method->invoke($this->service, ['array_name', 'ignored'], 'xlsx');
        $this->assertEquals('array_name.xlsx', $filename);
    }

    public function testGetExportFilenameWithNull(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getExportFilename');
        $method->setAccessible(true);

        $filename = $method->invoke($this->service, null, 'pdf');

        // Should generate a filename with module_controller_export pattern
        $this->assertStringContainsString('test_module_test_controller_export_', $filename);
        $this->assertStringEndsWith('.pdf', $filename);
    }

    public function testGetExportFilenameWithExistingExtension(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getExportFilename');
        $method->setAccessible(true);

        $filename = $method->invoke($this->service, 'already_has.csv', 'csv');
        $this->assertEquals('already_has.csv', $filename);
    }

    public function testGetExportFilenameWithEmptyString(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getExportFilename');
        $method->setAccessible(true);

        $filename = $method->invoke($this->service, '', 'json');

        // Should generate a filename when empty string is provided
        $this->assertStringContainsString('test_module_test_controller_export_', $filename);
        $this->assertStringEndsWith('.json', $filename);
    }

    public function testCreateTempStream(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('createTempStream');
        $method->setAccessible(true);

        $stream = $method->invoke($this->service);

        $this->assertIsResource($stream);
        $this->assertEquals('stream', get_resource_type($stream));

        // Clean up
        fclose($stream);
    }

    public function testStreamToBrowserWithValidStream(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('streamToBrowser');
        $method->setAccessible(true);

        $stream = fopen('php://temp', 'w+');
        fwrite($stream, 'test data');
        rewind($stream);

        // Capture output to prevent actual streaming
        ob_start();

        try {
            $method->invoke($this->service, $stream, 'test.txt', 'text/plain');
        } catch (\Exception $e) {
            // Expected - the streamer will try to exit
        }

        ob_end_clean();

        // Stream should be closed by the method
        $this->assertFalse(is_resource($stream));
    }

    public function testStreamToBrowserWithInvalidStream(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('streamToBrowser');
        $method->setAccessible(true);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid stream provided for streaming');

        $method->invoke($this->service, 'not a stream', 'test.txt', 'text/plain');
    }

    public function testHandleExportErrorWithLogger(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('handleExportError');
        $method->setAccessible(true);

        // Capture output to prevent header issues
        ob_start();

        try {
            $method->invoke($this->service, 'Test error message', 500);
        } catch (\Exception $e) {
            // Expected - method may exit
        }

        ob_end_clean();

        // Check that error was logged
        $this->assertContains('Export error: Test error message', $this->logger->errors);

        // Check that ajax_result was set
        $ajaxResult = $this->registry->get('ajax_result');
        $this->assertNotNull($ajaxResult);

        $decoded = json_decode($ajaxResult, true);
        $this->assertEquals('Test error message', $decoded['error']);
        $this->assertEquals('error', $decoded['status']);
        $this->assertArrayHasKey('timestamp', $decoded);
    }

    public function testHandleExportErrorWithoutLogger(): void
    {
        // Remove logger from registry
        $this->registry->remove('logger');

        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('handleExportError');
        $method->setAccessible(true);

        // Capture output to prevent header issues
        ob_start();

        try {
            // Should not throw an exception even without logger
            $method->invoke($this->service, 'Test error without logger');
        } catch (\Exception $e) {
            // Expected - method may exit
        }

        ob_end_clean();

        // Check that ajax_result was still set
        $ajaxResult = $this->registry->get('ajax_result');
        $this->assertNotNull($ajaxResult);

        $decoded = json_decode($ajaxResult, true);
        $this->assertEquals('Test error without logger', $decoded['error']);
    }

    public function testHandleExportErrorWithAjaxRequest(): void
    {
        // Mock AJAX request
        $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';

        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('handleExportError');
        $method->setAccessible(true);

        // Capture output to test AJAX response
        ob_start();

        try {
            $method->invoke($this->service, 'AJAX error test', 400);
        } catch (\Exception $e) {
            // Expected - method will exit for AJAX requests
        }

        $output = ob_get_clean();

        // Check that ajax_result was set (method may not output directly due to headers)
        $ajaxResult = $this->registry->get('ajax_result');
        $this->assertNotNull($ajaxResult);
        $this->assertJson($ajaxResult);

        $decoded = json_decode($ajaxResult, true);
        $this->assertEquals('AJAX error test', $decoded['error']);

        // Clean up
        unset($_SERVER['HTTP_X_REQUESTED_WITH']);
    }

    public function testComplexExportScenario(): void
    {
        // Test a complete export workflow
        $this->service->setModelName('ComplexModel');
        $this->service->setModelFactoryName('ComplexFactory');
        $this->service->setFormatFactory($this->formatFactory);

        $exportData = $this->createTestExportData();
        $options = [
            'max_column_width' => 40.0,
            'max_row_height' => 80.0,
            'chunk_size' => 1000
        ];

        // Capture output to prevent actual streaming
        ob_start();

        try {
            $this->service->export('complex_export.mock', $exportData, $options);
        } catch (\Exception $e) {
            // Expected - the streamer will try to exit
        }

        ob_end_clean();

        // Verify the complete workflow
        $this->assertTrue($this->mockAdapter->wasExportCalled());
        $this->assertSame($exportData, $this->mockAdapter->getLastExportData());
        $this->assertEquals($options, $this->mockAdapter->getLastOptions());

        // Verify format factory integration
        $this->assertSame($this->formatFactory, $this->service->getFormatFactory());
        $this->assertSame($this->mockAdapter, $this->service->getAdapter());
    }

    public function testLazyInitialization(): void
    {
        // Test that factory and adapter are created lazily
        $service = new ExportService($this->registry, 'lazy', 'test', 'csv');

        // Factory should be created on first access
        $factory1 = $service->getFormatFactory();
        $factory2 = $service->getFormatFactory();
        $this->assertSame($factory1, $factory2);

        // Adapter should be created on first access
        $adapter1 = $service->getAdapter();
        $adapter2 = $service->getAdapter();
        $this->assertSame($adapter1, $adapter2);
    }
}
