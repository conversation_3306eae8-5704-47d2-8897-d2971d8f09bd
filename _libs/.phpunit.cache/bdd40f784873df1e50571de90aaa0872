a:6:{s:9:"classesIn";a:1:{s:26:"Nzoom\Export\ExportService";a:6:{s:4:"name";s:13:"ExportService";s:14:"namespacedName";s:26:"Nzoom\Export\ExportService";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:13;s:7:"endLine";i:388;s:7:"methods";a:18:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:81:"__construct(Registry $registry, string $module, string $controller, string $type)";s:10:"visibility";s:6:"public";s:9:"startLine";i:77;s:7:"endLine";i:84;s:3:"ccn";i:1;}s:12:"setModelName";a:6:{s:10:"methodName";s:12:"setModelName";s:9:"signature";s:31:"setModelName(string $modelName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:92;s:7:"endLine";i:96;s:3:"ccn";i:1;}s:19:"setModelFactoryName";a:6:{s:10:"methodName";s:19:"setModelFactoryName";s:9:"signature";s:45:"setModelFactoryName(string $modelFactoryName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:104;s:7:"endLine";i:108;s:3:"ccn";i:1;}s:18:"createExportAction";a:6:{s:10:"methodName";s:18:"createExportAction";s:9:"signature";s:83:"createExportAction(string $module_check, array $types, array $typeSections): ?array";s:10:"visibility";s:6:"public";s:9:"startLine";i:118;s:7:"endLine";i:132;s:3:"ccn";i:1;}s:16:"createExportData";a:6:{s:10:"methodName";s:16:"createExportData";s:9:"signature";s:113:"createExportData(Outlook $outlook, array $filters, string $modelClass, $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:135;s:7:"endLine";i:139;s:3:"ccn";i:1;}s:26:"createExportDataWithTables";a:6:{s:10:"methodName";s:26:"createExportDataWithTables";s:9:"signature";s:125:"createExportDataWithTables(Outlook $outlook, array $filters, string $factoryClass, $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:141;s:7:"endLine";i:150;s:3:"ccn";i:1;}s:27:"createGeneratorFileStreamer";a:6:{s:10:"methodName";s:27:"createGeneratorFileStreamer";s:9:"signature";s:154:"createGeneratorFileStreamer(callable $generatorFunction, string $filename, string $mimeType, ?int $totalSize): Nzoom\Export\Streamer\GeneratorFileStreamer";s:10:"visibility";s:6:"public";s:9:"startLine";i:161;s:7:"endLine";i:164;s:3:"ccn";i:1;}s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:84:"export(string $filename, Nzoom\Export\Entity\ExportData $data, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:175;s:7:"endLine";i:192;s:3:"ccn";i:3;}s:16:"createTempStream";a:6:{s:10:"methodName";s:16:"createTempStream";s:9:"signature";s:18:"createTempStream()";s:10:"visibility";s:7:"private";s:9:"startLine";i:200;s:7:"endLine";i:209;s:3:"ccn";i:2;}s:15:"streamToBrowser";a:6:{s:10:"methodName";s:15:"streamToBrowser";s:9:"signature";s:74:"streamToBrowser($stream, string $downloadFilename, string $mimeType): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:220;s:7:"endLine";i:243;s:3:"ccn";i:3;}s:16:"getFormatFactory";a:6:{s:10:"methodName";s:16:"getFormatFactory";s:9:"signature";s:60:"getFormatFactory(): Nzoom\Export\Factory\ExportFormatFactory";s:10:"visibility";s:6:"public";s:9:"startLine";i:251;s:7:"endLine";i:258;s:3:"ccn";i:2;}s:16:"setFormatFactory";a:6:{s:10:"methodName";s:16:"setFormatFactory";s:9:"signature";s:79:"setFormatFactory(Nzoom\Export\Factory\ExportFormatFactory $formatFactory): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:266;s:7:"endLine";i:270;s:3:"ccn";i:1;}s:10:"getAdapter";a:6:{s:10:"methodName";s:10:"getAdapter";s:9:"signature";s:63:"getAdapter(): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:278;s:7:"endLine";i:285;s:3:"ccn";i:2;}s:19:"getSupportedFormats";a:6:{s:10:"methodName";s:19:"getSupportedFormats";s:9:"signature";s:28:"getSupportedFormats(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:292;s:7:"endLine";i:295;s:3:"ccn";i:1;}s:17:"isFormatSupported";a:6:{s:10:"methodName";s:17:"isFormatSupported";s:9:"signature";s:39:"isFormatSupported(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:303;s:7:"endLine";i:306;s:3:"ccn";i:1;}s:26:"getReferenceColumnForModel";a:6:{s:10:"methodName";s:26:"getReferenceColumnForModel";s:9:"signature";s:53:"getReferenceColumnForModel(string $modelClass): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:314;s:7:"endLine";i:323;s:3:"ccn";i:2;}s:17:"getExportFilename";a:6:{s:10:"methodName";s:17:"getExportFilename";s:9:"signature";s:45:"getExportFilename($prefix, string $extension)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:332;s:7:"endLine";i:352;s:3:"ccn";i:4;}s:17:"handleExportError";a:6:{s:10:"methodName";s:17:"handleExportError";s:9:"signature";s:40:"handleExportError($message, $statusCode)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:362;s:7:"endLine";i:387;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:389;s:18:"commentLinesOfCode";i:162;s:21:"nonCommentLinesOfCode";i:227;}s:15:"ignoredLinesFor";a:1:{i:0;i:13;}s:17:"executableLinesIn";a:82:{i:79;i:12;i:80;i:13;i:81;i:14;i:82;i:15;i:83;i:16;i:94;i:17;i:95;i:18;i:106;i:19;i:107;i:20;i:121;i:21;i:122;i:21;i:123;i:21;i:124;i:21;i:125;i:21;i:126;i:21;i:127;i:21;i:128;i:21;i:131;i:22;i:137;i:23;i:138;i:24;i:143;i:25;i:146;i:26;i:147;i:27;i:149;i:28;i:163;i:29;i:175;i:30;i:180;i:31;i:182;i:32;i:183;i:33;i:186;i:34;i:187;i:35;i:188;i:36;i:189;i:37;i:190;i:38;i:202;i:39;i:204;i:40;i:205;i:41;i:208;i:42;i:222;i:43;i:223;i:44;i:228;i:45;i:229;i:45;i:230;i:45;i:231;i:45;i:232;i:45;i:235;i:46;i:239;i:47;i:240;i:48;i:253;i:49;i:254;i:50;i:257;i:51;i:268;i:52;i:269;i:53;i:280;i:54;i:281;i:55;i:284;i:56;i:294;i:57;i:305;i:58;i:317;i:59;i:318;i:60;i:322;i:61;i:335;i:62;i:338;i:63;i:339;i:64;i:342;i:65;i:343;i:66;i:347;i:67;i:348;i:68;i:351;i:69;i:365;i:70;i:366;i:71;i:370;i:72;i:371;i:72;i:372;i:72;i:373;i:72;i:374;i:72;i:377;i:73;i:378;i:74;i:380;i:75;i:381;i:76;i:382;i:77;i:383;i:78;}}