a:6:{s:9:"classesIn";a:1:{s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";a:6:{s:4:"name";s:24:"ExcelExportFormatAdapter";s:14:"namespacedName";s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:27;s:7:"endLine";i:1528;s:7:"methods";a:48:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:61;s:7:"endLine";i:106;s:3:"ccn";i:5;}s:20:"extractSizingOptions";a:6:{s:10:"methodName";s:20:"extractSizingOptions";s:9:"signature";s:42:"extractSizingOptions(array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:114;s:7:"endLine";i:141;s:3:"ccn";i:11;}s:17:"createSpreadsheet";a:6:{s:10:"methodName";s:17:"createSpreadsheet";s:9:"signature";s:99:"createSpreadsheet(Nzoom\Export\Entity\ExportData $exportData): PhpOffice\PhpSpreadsheet\Spreadsheet";s:10:"visibility";s:7:"private";s:9:"startLine";i:149;s:7:"endLine";i:178;s:3:"ccn";i:1;}s:20:"setSpreadsheetLocale";a:6:{s:10:"methodName";s:20:"setSpreadsheetLocale";s:9:"signature";s:28:"setSpreadsheetLocale(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:185;s:7:"endLine";i:201;s:3:"ccn";i:4;}s:20:"getApplicationLocale";a:6:{s:10:"methodName";s:20:"getApplicationLocale";s:9:"signature";s:31:"getApplicationLocale(): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:208;s:7:"endLine";i:250;s:3:"ccn";i:4;}s:21:"setDocumentProperties";a:6:{s:10:"methodName";s:21:"setDocumentProperties";s:9:"signature";s:96:"setDocumentProperties(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:259;s:7:"endLine";i:271;s:3:"ccn";i:2;}s:17:"processExportData";a:6:{s:10:"methodName";s:17:"processExportData";s:9:"signature";s:120:"processExportData(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:280;s:7:"endLine";i:310;s:3:"ccn";i:2;}s:10:"addHeaders";a:6:{s:10:"methodName";s:10:"addHeaders";s:9:"signature";s:85:"addHeaders(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:319;s:7:"endLine";i:325;s:3:"ccn";i:2;}s:25:"addHeadersWithEnumeration";a:6:{s:10:"methodName";s:25:"addHeadersWithEnumeration";s:9:"signature";s:100:"addHeadersWithEnumeration(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:334;s:7:"endLine";i:344;s:3:"ccn";i:2;}s:14:"addNamedRanges";a:6:{s:10:"methodName";s:14:"addNamedRanges";s:9:"signature";s:141:"addNamedRanges(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportHeader $header, bool $includeEnumeration): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:354;s:7:"endLine";i:387;s:3:"ccn";i:5;}s:24:"shouldIncludeEnumeration";a:6:{s:10:"methodName";s:24:"shouldIncludeEnumeration";s:9:"signature";s:32:"shouldIncludeEnumeration(): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:394;s:7:"endLine";i:398;s:3:"ccn";i:2;}s:14:"styleHeaderRow";a:6:{s:10:"methodName";s:14:"styleHeaderRow";s:9:"signature";s:91:"styleHeaderRow(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, int $headerCount): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:407;s:7:"endLine";i:414;s:3:"ccn";i:1;}s:24:"processExportDataRecords";a:6:{s:10:"methodName";s:24:"processExportDataRecords";s:9:"signature";s:127:"processExportDataRecords(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:423;s:7:"endLine";i:462;s:3:"ccn";i:8;}s:19:"processExportRecord";a:6:{s:10:"methodName";s:19:"processExportRecord";s:9:"signature";s:130:"processExportRecord(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportRecord $record, int $row): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:472;s:7:"endLine";i:475;s:3:"ccn";i:1;}s:22:"processMainSheetRecord";a:6:{s:10:"methodName";s:22:"processMainSheetRecord";s:9:"signature";s:133:"processMainSheetRecord(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportRecord $record, int $row): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:485;s:7:"endLine";i:521;s:3:"ccn";i:6;}s:26:"setCellValueWithFormatting";a:6:{s:10:"methodName";s:26:"setCellValueWithFormatting";s:9:"signature";s:152:"setCellValueWithFormatting(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, Nzoom\Export\Entity\ExportValue $exportValue): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:533;s:7:"endLine";i:583;s:3:"ccn";i:17;}s:16:"setCellDateValue";a:6:{s:10:"methodName";s:16:"setCellDateValue";s:9:"signature";s:118:"setCellDateValue(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, $value, string $type): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:594;s:7:"endLine";i:614;s:3:"ccn";i:5;}s:29:"getExcelFormatFromExportValue";a:6:{s:10:"methodName";s:29:"getExcelFormatFromExportValue";s:9:"signature";s:83:"getExcelFormatFromExportValue(Nzoom\Export\Entity\ExportValue $exportValue): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:622;s:7:"endLine";i:665;s:3:"ccn";i:15;}s:25:"convertCustomNumberFormat";a:6:{s:10:"methodName";s:25:"convertCustomNumberFormat";s:9:"signature";s:49:"convertCustomNumberFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:673;s:7:"endLine";i:688;s:3:"ccn";i:3;}s:23:"convertCustomDateFormat";a:6:{s:10:"methodName";s:23:"convertCustomDateFormat";s:9:"signature";s:47:"convertCustomDateFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:696;s:7:"endLine";i:714;s:3:"ccn";i:1;}s:27:"handleExportRecordCellError";a:6:{s:10:"methodName";s:27:"handleExportRecordCellError";s:9:"signature";s:186:"handleExportRecordCellError(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $colLetter, int $row, Exception $e, int $colIndex, Nzoom\Export\Entity\ExportRecord $record): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:727;s:7:"endLine";i:736;s:3:"ccn";i:2;}s:25:"finalizeExportDataColumns";a:6:{s:10:"methodName";s:25:"finalizeExportDataColumns";s:9:"signature";s:100:"finalizeExportDataColumns(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:745;s:7:"endLine";i:754;s:3:"ccn";i:2;}s:24:"finalizeMainSheetColumns";a:6:{s:10:"methodName";s:24:"finalizeMainSheetColumns";s:9:"signature";s:99:"finalizeMainSheetColumns(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:763;s:7:"endLine";i:788;s:3:"ccn";i:3;}s:42:"finalizeMainSheetColumnsWithoutEnumeration";a:6:{s:10:"methodName";s:42:"finalizeMainSheetColumnsWithoutEnumeration";s:9:"signature";s:117:"finalizeMainSheetColumnsWithoutEnumeration(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:797;s:7:"endLine";i:807;s:3:"ccn";i:2;}s:27:"applyColumnWidthConstraints";a:6:{s:10:"methodName";s:27:"applyColumnWidthConstraints";s:9:"signature";s:86:"applyColumnWidthConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:815;s:7:"endLine";i:841;s:3:"ccn";i:5;}s:25:"applyRowHeightConstraints";a:6:{s:10:"methodName";s:25:"applyRowHeightConstraints";s:9:"signature";s:84:"applyRowHeightConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:852;s:7:"endLine";i:901;s:3:"ccn";i:8;}s:22:"applyVerticalAlignment";a:6:{s:10:"methodName";s:22:"applyVerticalAlignment";s:9:"signature";s:81:"applyVerticalAlignment(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:909;s:7:"endLine";i:919;s:3:"ccn";i:3;}s:19:"processExportTables";a:6:{s:10:"methodName";s:19:"processExportTables";s:9:"signature";s:120:"processExportTables(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:928;s:7:"endLine";i:944;s:3:"ccn";i:4;}s:19:"collectTablesByType";a:6:{s:10:"methodName";s:19:"collectTablesByType";s:9:"signature";s:70:"collectTablesByType(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:952;s:7:"endLine";i:983;s:3:"ccn";i:5;}s:20:"createTableWorksheet";a:6:{s:10:"methodName";s:20:"createTableWorksheet";s:9:"signature";s:169:"createTableWorksheet(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $tableType, array $tablesWithRecordNumbers): ?PhpOffice\PhpSpreadsheet\Worksheet\Worksheet";s:10:"visibility";s:7:"private";s:9:"startLine";i:993;s:7:"endLine";i:1014;s:3:"ccn";i:4;}s:21:"sanitizeWorksheetName";a:6:{s:10:"methodName";s:21:"sanitizeWorksheetName";s:9:"signature";s:43:"sanitizeWorksheetName(string $name): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:1022;s:7:"endLine";i:1027;s:3:"ccn";i:1;}s:22:"populateTableWorksheet";a:6:{s:10:"methodName";s:22:"populateTableWorksheet";s:9:"signature";s:117:"populateTableWorksheet(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, array $tablesWithRecordNumbers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1036;s:7:"endLine";i:1069;s:3:"ccn";i:4;}s:23:"addTableDataToWorksheet";a:6:{s:10:"methodName";s:23:"addTableDataToWorksheet";s:9:"signature";s:166:"addTableDataToWorksheet(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportTable $table, int $startRow, int $startingEnumeration): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:1080;s:7:"endLine";i:1092;s:3:"ccn";i:2;}s:30:"addTableHeadersWithEnumeration";a:6:{s:10:"methodName";s:30:"addTableHeadersWithEnumeration";s:9:"signature";s:109:"addTableHeadersWithEnumeration(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1103;s:7:"endLine";i:1111;s:3:"ccn";i:2;}s:19:"addTableNamedRanges";a:6:{s:10:"methodName";s:19:"addTableNamedRanges";s:9:"signature";s:124:"addTableNamedRanges(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1122;s:7:"endLine";i:1146;s:3:"ccn";i:4;}s:18:"processTableRecord";a:6:{s:10:"methodName";s:18:"processTableRecord";s:9:"signature";s:159:"processTableRecord(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, Nzoom\Export\Entity\ExportRecord $record, int $row, int $tableRowEnumeration): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1159;s:7:"endLine";i:1174;s:3:"ccn";i:3;}s:20:"finalizeTableColumns";a:6:{s:10:"methodName";s:20:"finalizeTableColumns";s:9:"signature";s:99:"finalizeTableColumns(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $worksheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1185;s:7:"endLine";i:1203;s:3:"ccn";i:3;}s:12:"createWriter";a:6:{s:10:"methodName";s:12:"createWriter";s:9:"signature";s:123:"createWriter(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $extension): PhpOffice\PhpSpreadsheet\Writer\IWriter";s:10:"visibility";s:7:"private";s:9:"startLine";i:1215;s:7:"endLine";i:1227;s:3:"ccn";i:4;}s:22:"handleSpreadsheetError";a:6:{s:10:"methodName";s:22:"handleSpreadsheetError";s:9:"signature";s:92:"handleSpreadsheetError(PhpOffice\PhpSpreadsheet\Writer\Exception $e, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1236;s:7:"endLine";i:1258;s:3:"ccn";i:4;}s:18:"getExcelFormatting";a:6:{s:10:"methodName";s:18:"getExcelFormatting";s:9:"signature";s:43:"getExcelFormatting(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:1266;s:7:"endLine";i:1361;s:3:"ccn";i:83;}s:23:"optimizeMemoryForExport";a:6:{s:10:"methodName";s:23:"optimizeMemoryForExport";s:9:"signature";s:31:"optimizeMemoryForExport(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:1368;s:7:"endLine";i:1383;s:3:"ccn";i:3;}s:14:"convertToBytes";a:6:{s:10:"methodName";s:14:"convertToBytes";s:9:"signature";s:40:"convertToBytes(string $memoryLimit): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:1391;s:7:"endLine";i:1409;s:3:"ccn";i:4;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:1414;s:7:"endLine";i:1417;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1422;s:7:"endLine";i:1434;s:3:"ccn";i:4;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1439;s:7:"endLine";i:1442;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:1447;s:7:"endLine";i:1450;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:1455;s:7:"endLine";i:1458;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:1463;s:7:"endLine";i:1527;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:1529;s:18:"commentLinesOfCode";i:454;s:21:"nonCommentLinesOfCode";i:1075;}s:15:"ignoredLinesFor";a:1:{i:0;i:27;}s:17:"executableLinesIn";a:610:{i:61;i:5;i:65;i:6;i:66;i:7;i:70;i:8;i:73;i:9;i:74;i:10;i:75;i:11;i:79;i:12;i:82;i:13;i:85;i:14;i:88;i:15;i:91;i:16;i:94;i:17;i:95;i:18;i:97;i:19;i:99;i:20;i:100;i:21;i:104;i:22;i:117;i:23;i:119;i:24;i:120;i:25;i:123;i:26;i:124;i:27;i:127;i:28;i:128;i:29;i:132;i:30;i:134;i:31;i:135;i:32;i:136;i:33;i:137;i:34;i:152;i:35;i:155;i:36;i:158;i:37;i:159;i:38;i:162;i:39;i:165;i:40;i:168;i:41;i:169;i:42;i:172;i:43;i:175;i:44;i:177;i:45;i:189;i:46;i:191;i:47;i:193;i:48;i:195;i:49;i:197;i:50;i:198;i:51;i:211;i:52;i:212;i:53;i:215;i:54;i:216;i:54;i:217;i:54;i:218;i:54;i:219;i:54;i:220;i:54;i:221;i:54;i:222;i:54;i:223;i:54;i:224;i:54;i:225;i:54;i:226;i:54;i:227;i:54;i:228;i:54;i:229;i:54;i:230;i:54;i:231;i:54;i:232;i:54;i:233;i:54;i:235;i:55;i:239;i:56;i:240;i:57;i:244;i:58;i:245;i:59;i:249;i:60;i:261;i:61;i:262;i:62;i:263;i:63;i:266;i:64;i:267;i:64;i:268;i:64;i:269;i:64;i:270;i:64;i:283;i:65;i:284;i:66;i:287;i:67;i:289;i:68;i:291;i:69;i:293;i:70;i:296;i:71;i:298;i:72;i:302;i:73;i:305;i:74;i:308;i:75;i:321;i:76;i:322;i:77;i:323;i:78;i:337;i:79;i:340;i:80;i:341;i:81;i:342;i:82;i:356;i:83;i:358;i:84;i:359;i:85;i:361;i:86;i:364;i:87;i:366;i:88;i:371;i:89;i:372;i:89;i:373;i:89;i:374;i:89;i:375;i:89;i:376;i:89;i:377;i:89;i:378;i:90;i:380;i:91;i:381;i:92;i:382;i:92;i:383;i:92;i:397;i:93;i:409;i:94;i:410;i:95;i:411;i:96;i:412;i:96;i:413;i:96;i:425;i:97;i:426;i:98;i:427;i:99;i:430;i:100;i:432;i:101;i:434;i:102;i:435;i:103;i:438;i:104;i:439;i:105;i:442;i:106;i:443;i:107;i:447;i:108;i:448;i:109;i:454;i:110;i:455;i:111;i:459;i:112;i:460;i:113;i:474;i:114;i:487;i:115;i:488;i:116;i:490;i:117;i:492;i:118;i:493;i:119;i:496;i:120;i:497;i:121;i:498;i:122;i:502;i:123;i:503;i:124;i:504;i:125;i:509;i:126;i:510;i:127;i:511;i:128;i:515;i:129;i:516;i:130;i:517;i:131;i:535;i:132;i:536;i:133;i:539;i:134;i:540;i:135;i:541;i:136;i:546;i:137;i:547;i:138;i:548;i:139;i:549;i:140;i:551;i:141;i:552;i:142;i:553;i:143;i:554;i:144;i:555;i:145;i:557;i:146;i:558;i:147;i:559;i:148;i:561;i:149;i:562;i:150;i:563;i:151;i:564;i:152;i:565;i:153;i:566;i:154;i:570;i:155;i:571;i:156;i:573;i:157;i:575;i:158;i:579;i:159;i:580;i:160;i:581;i:161;i:597;i:162;i:599;i:163;i:600;i:164;i:601;i:165;i:603;i:166;i:604;i:167;i:605;i:168;i:608;i:169;i:610;i:170;i:612;i:171;i:624;i:172;i:625;i:173;i:628;i:174;i:629;i:175;i:630;i:176;i:632;i:177;i:633;i:178;i:634;i:179;i:636;i:180;i:638;i:181;i:639;i:182;i:641;i:183;i:643;i:184;i:644;i:185;i:646;i:186;i:647;i:187;i:649;i:188;i:651;i:189;i:652;i:190;i:654;i:191;i:655;i:192;i:657;i:193;i:659;i:194;i:660;i:195;i:663;i:196;i:676;i:197;i:678;i:198;i:679;i:199;i:680;i:200;i:682;i:201;i:687;i:202;i:699;i:203;i:700;i:203;i:701;i:203;i:702;i:203;i:703;i:203;i:704;i:203;i:705;i:203;i:706;i:203;i:707;i:203;i:708;i:203;i:709;i:203;i:710;i:203;i:713;i:204;i:730;i:205;i:731;i:206;i:732;i:206;i:735;i:207;i:747;i:208;i:749;i:209;i:750;i:210;i:752;i:211;i:766;i:212;i:767;i:213;i:768;i:214;i:769;i:215;i:770;i:215;i:771;i:215;i:775;i:216;i:776;i:217;i:777;i:218;i:781;i:219;i:784;i:220;i:787;i:221;i:799;i:222;i:800;i:223;i:801;i:224;i:804;i:225;i:805;i:226;i:806;i:227;i:818;i:228;i:821;i:229;i:822;i:230;i:823;i:231;i:826;i:232;i:827;i:233;i:829;i:234;i:830;i:235;i:833;i:236;i:834;i:237;i:835;i:238;i:836;i:239;i:837;i:240;i:854;i:241;i:855;i:242;i:856;i:243;i:859;i:244;i:860;i:245;i:863;i:246;i:865;i:247;i:866;i:248;i:867;i:249;i:870;i:250;i:871;i:251;i:872;i:252;i:875;i:253;i:876;i:254;i:880;i:255;i:881;i:256;i:882;i:257;i:884;i:258;i:885;i:259;i:886;i:260;i:887;i:261;i:892;i:262;i:894;i:263;i:896;i:264;i:898;i:265;i:911;i:266;i:912;i:267;i:914;i:268;i:916;i:269;i:917;i:270;i:930;i:271;i:932;i:272;i:933;i:273;i:934;i:274;i:938;i:275;i:940;i:276;i:941;i:277;i:954;i:278;i:955;i:279;i:957;i:280;i:958;i:281;i:959;i:282;i:960;i:283;i:963;i:284;i:964;i:285;i:966;i:286;i:967;i:287;i:968;i:288;i:969;i:289;i:972;i:290;i:973;i:290;i:974;i:290;i:975;i:290;i:976;i:290;i:979;i:291;i:982;i:292;i:995;i:293;i:996;i:294;i:1000;i:295;i:1001;i:296;i:1002;i:297;i:1005;i:298;i:1006;i:299;i:1007;i:300;i:1008;i:301;i:1009;i:302;i:1010;i:303;i:1012;i:304;i:1025;i:305;i:1026;i:306;i:1038;i:307;i:1039;i:308;i:1043;i:309;i:1044;i:310;i:1045;i:311;i:1046;i:312;i:1050;i:313;i:1051;i:314;i:1052;i:315;i:1054;i:316;i:1055;i:317;i:1057;i:318;i:1058;i:319;i:1060;i:320;i:1061;i:321;i:1064;i:322;i:1065;i:323;i:1068;i:324;i:1082;i:325;i:1083;i:326;i:1085;i:327;i:1086;i:328;i:1087;i:329;i:1088;i:330;i:1091;i:331;i:1105;i:332;i:1107;i:333;i:1108;i:334;i:1109;i:335;i:1124;i:336;i:1126;i:337;i:1127;i:338;i:1128;i:339;i:1131;i:340;i:1132;i:340;i:1133;i:340;i:1134;i:340;i:1135;i:340;i:1136;i:340;i:1137;i:340;i:1138;i:341;i:1139;i:342;i:1140;i:343;i:1141;i:343;i:1142;i:343;i:1161;i:344;i:1163;i:345;i:1164;i:346;i:1165;i:347;i:1166;i:348;i:1169;i:349;i:1170;i:350;i:1171;i:351;i:1187;i:352;i:1188;i:353;i:1189;i:354;i:1190;i:355;i:1191;i:355;i:1192;i:355;i:1195;i:356;i:1196;i:357;i:1197;i:358;i:1200;i:359;i:1201;i:360;i:1202;i:361;i:1217;i:362;i:1220;i:363;i:1221;i:364;i:1222;i:365;i:1223;i:366;i:1225;i:367;i:1239;i:368;i:1240;i:369;i:1244;i:370;i:1245;i:371;i:1248;i:372;i:1250;i:373;i:1251;i:373;i:1252;i:373;i:1253;i:373;i:1254;i:374;i:1269;i:375;i:1270;i:376;i:1271;i:377;i:1272;i:378;i:1273;i:379;i:1274;i:380;i:1275;i:381;i:1276;i:382;i:1277;i:383;i:1278;i:384;i:1279;i:385;i:1280;i:386;i:1281;i:387;i:1282;i:388;i:1283;i:389;i:1284;i:390;i:1285;i:391;i:1286;i:392;i:1287;i:393;i:1288;i:394;i:1289;i:395;i:1290;i:396;i:1291;i:397;i:1292;i:398;i:1293;i:399;i:1294;i:400;i:1295;i:401;i:1296;i:402;i:1297;i:403;i:1298;i:404;i:1299;i:405;i:1300;i:406;i:1301;i:407;i:1302;i:408;i:1303;i:409;i:1304;i:410;i:1305;i:411;i:1306;i:412;i:1307;i:413;i:1308;i:414;i:1309;i:415;i:1310;i:416;i:1311;i:417;i:1312;i:418;i:1313;i:419;i:1314;i:420;i:1315;i:421;i:1316;i:422;i:1317;i:423;i:1318;i:424;i:1319;i:425;i:1320;i:426;i:1321;i:427;i:1322;i:428;i:1323;i:429;i:1324;i:430;i:1325;i:431;i:1326;i:432;i:1327;i:433;i:1328;i:434;i:1329;i:435;i:1330;i:436;i:1331;i:437;i:1332;i:438;i:1333;i:439;i:1334;i:440;i:1336;i:441;i:1337;i:442;i:1338;i:443;i:1339;i:444;i:1340;i:445;i:1341;i:446;i:1342;i:447;i:1343;i:448;i:1345;i:449;i:1346;i:450;i:1347;i:451;i:1348;i:452;i:1349;i:453;i:1350;i:454;i:1351;i:455;i:1352;i:456;i:1353;i:457;i:1354;i:458;i:1355;i:459;i:1356;i:460;i:1357;i:461;i:1360;i:462;i:1371;i:463;i:1372;i:464;i:1376;i:465;i:1377;i:466;i:1380;i:467;i:1381;i:468;i:1393;i:469;i:1394;i:470;i:1395;i:471;i:1398;i:472;i:1399;i:473;i:1401;i:474;i:1402;i:475;i:1404;i:476;i:1405;i:477;i:1408;i:478;i:1416;i:479;i:1424;i:480;i:1426;i:481;i:1427;i:482;i:1428;i:483;i:1429;i:484;i:1430;i:485;i:1432;i:486;i:1441;i:487;i:1449;i:488;i:1457;i:489;i:1465;i:490;i:1466;i:490;i:1467;i:490;i:1468;i:490;i:1469;i:490;i:1470;i:490;i:1471;i:490;i:1472;i:490;i:1473;i:490;i:1474;i:490;i:1475;i:490;i:1476;i:490;i:1477;i:490;i:1478;i:490;i:1479;i:490;i:1480;i:490;i:1481;i:490;i:1482;i:490;i:1483;i:490;i:1484;i:490;i:1485;i:490;i:1486;i:490;i:1487;i:490;i:1488;i:490;i:1489;i:490;i:1490;i:490;i:1491;i:490;i:1492;i:490;i:1493;i:490;i:1494;i:490;i:1495;i:490;i:1496;i:490;i:1497;i:490;i:1498;i:490;i:1499;i:490;i:1500;i:490;i:1501;i:490;i:1502;i:490;i:1503;i:490;i:1504;i:490;i:1505;i:490;i:1506;i:490;i:1507;i:490;i:1508;i:490;i:1509;i:490;i:1510;i:490;i:1511;i:490;i:1512;i:490;i:1513;i:490;i:1514;i:490;i:1515;i:490;i:1516;i:490;i:1517;i:490;i:1518;i:490;i:1519;i:490;i:1520;i:490;i:1521;i:490;i:1522;i:490;i:1523;i:490;i:1524;i:490;i:1525;i:490;i:1526;i:490;}}