a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:905;s:7:"methods";a:25:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:106:"__construct(Registry $registry, string $referenceColumnName, string $referenceColumnLabel, array $options)";s:10:"visibility";s:6:"public";s:9:"startLine";i:50;s:7:"endLine";i:58;s:3:"ccn";i:1;}s:18:"getReferenceColumn";a:6:{s:10:"methodName";s:18:"getReferenceColumn";s:9:"signature";s:27:"getReferenceColumn(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:65;s:7:"endLine";i:68;s:3:"ccn";i:1;}s:17:"getDefaultOptions";a:6:{s:10:"methodName";s:17:"getDefaultOptions";s:9:"signature";s:26:"getDefaultOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:75;s:7:"endLine";i:82;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:87;s:7:"endLine";i:122;s:3:"ccn";i:10;}s:25:"discoverGroupingVariables";a:6:{s:10:"methodName";s:25:"discoverGroupingVariables";s:9:"signature";s:46:"discoverGroupingVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:130;s:7:"endLine";i:171;s:3:"ccn";i:10;}s:27:"createTableFromGroupingData";a:6:{s:10:"methodName";s:27:"createTableFromGroupingData";s:9:"signature";s:129:"createTableFromGroupingData(Model $model, string $varName, array $groupingData, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:182;s:7:"endLine";i:222;s:3:"ccn";i:4;}s:22:"createTableFromGT2Data";a:6:{s:10:"methodName";s:22:"createTableFromGT2Data";s:9:"signature";s:119:"createTableFromGT2Data(Model $model, string $varName, array $gt2Data, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:233;s:7:"endLine";i:271;s:3:"ccn";i:3;}s:22:"getOrCreateTableHeader";a:6:{s:10:"methodName";s:22:"getOrCreateTableHeader";s:9:"signature";s:133:"getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden, array $types): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:283;s:7:"endLine";i:336;s:3:"ccn";i:7;}s:25:"getOrCreateGT2TableHeader";a:6:{s:10:"methodName";s:25:"getOrCreateGT2TableHeader";s:9:"signature";s:91:"getOrCreateGT2TableHeader(string $tableType, array $vars): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:345;s:7:"endLine";i:396;s:3:"ccn";i:6;}s:21:"sortGT2VarsByPosition";a:6:{s:10:"methodName";s:21:"sortGT2VarsByPosition";s:9:"signature";s:41:"sortGT2VarsByPosition(array $vars): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:404;s:7:"endLine";i:425;s:3:"ccn";i:5;}s:15:"formatTableName";a:6:{s:10:"methodName";s:15:"formatTableName";s:9:"signature";s:40:"formatTableName(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:433;s:7:"endLine";i:440;s:3:"ccn";i:1;}s:27:"convertFieldTypeToValueType";a:6:{s:10:"methodName";s:27:"convertFieldTypeToValueType";s:9:"signature";s:54:"convertFieldTypeToValueType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:448;s:7:"endLine";i:473;s:3:"ccn";i:13;}s:15:"guessColumnType";a:6:{s:10:"methodName";s:15:"guessColumnType";s:9:"signature";s:40:"guessColumnType(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:481;s:7:"endLine";i:508;s:3:"ccn";i:6;}s:29:"populateTableFromGroupingData";a:6:{s:10:"methodName";s:29:"populateTableFromGroupingData";s:9:"signature";s:184:"populateTableFromGroupingData(Nzoom\Export\Entity\ExportTable $table, array $values, array $names, array $hidden, array $types, array $groupingData, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:522;s:7:"endLine";i:534;s:3:"ccn";i:3;}s:24:"populateTableFromGT2Data";a:6:{s:10:"methodName";s:24:"populateTableFromGT2Data";s:9:"signature";s:128:"populateTableFromGT2Data(Nzoom\Export\Entity\ExportTable $table, array $values, array $vars, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:545;s:7:"endLine";i:560;s:3:"ccn";i:3;}s:26:"createRecordFromGT2RowData";a:6:{s:10:"methodName";s:26:"createRecordFromGT2RowData";s:9:"signature";s:149:"createRecordFromGT2RowData(array $rowData, array $sortedVars, array $options, Model $model, int $enumerationValue): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:572;s:7:"endLine";i:607;s:3:"ccn";i:5;}s:23:"createRecordFromRowData";a:6:{s:10:"methodName";s:23:"createRecordFromRowData";s:9:"signature";s:191:"createRecordFromRowData(array $rowData, array $names, array $hidden, array $types, array $groupingData, array $options, Model $model, int $enumerationValue): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:622;s:7:"endLine";i:663;s:3:"ccn";i:6;}s:18:"resolveOptionLabel";a:6:{s:10:"methodName";s:18:"resolveOptionLabel";s:9:"signature";s:84:"resolveOptionLabel($value, ?string $fieldType, string $varName, array $groupingData)";s:10:"visibility";s:7:"private";s:9:"startLine";i:676;s:7:"endLine";i:700;s:3:"ccn";i:8;}s:29:"resolveOptionLabelFromVarData";a:6:{s:10:"methodName";s:29:"resolveOptionLabelFromVarData";s:9:"signature";s:72:"resolveOptionLabelFromVarData($value, string $fieldType, array $varData)";s:10:"visibility";s:7:"private";s:9:"startLine";i:710;s:7:"endLine";i:734;s:3:"ccn";i:8;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:745;s:7:"endLine";i:781;s:3:"ccn";i:15;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:786;s:7:"endLine";i:793;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:798;s:7:"endLine";i:807;s:3:"ccn";i:2;}s:15:"shouldSkipTable";a:6:{s:10:"methodName";s:15:"shouldSkipTable";s:9:"signature";s:61:"shouldSkipTable(Nzoom\Export\Entity\ExportTable $table): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:815;s:7:"endLine";i:826;s:3:"ccn";i:2;}s:15:"isTableRowEmpty";a:6:{s:10:"methodName";s:15:"isTableRowEmpty";s:9:"signature";s:63:"isTableRowEmpty(Nzoom\Export\Entity\ExportRecord $record): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:835;s:7:"endLine";i:856;s:3:"ccn";i:7;}s:18:"isValueEmptyOrZero";a:6:{s:10:"methodName";s:18:"isValueEmptyOrZero";s:9:"signature";s:32:"isValueEmptyOrZero($value): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:864;s:7:"endLine";i:904;s:3:"ccn";i:8;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:906;s:18:"commentLinesOfCode";i:283;s:21:"nonCommentLinesOfCode";i:623;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:323:{i:50;i:5;i:52;i:6;i:53;i:7;i:54;i:7;i:55;i:7;i:56;i:7;i:57;i:8;i:67;i:9;i:77;i:10;i:78;i:10;i:79;i:10;i:80;i:10;i:81;i:10;i:87;i:11;i:89;i:12;i:90;i:13;i:92;i:14;i:93;i:15;i:98;i:16;i:100;i:17;i:102;i:18;i:103;i:19;i:105;i:20;i:108;i:21;i:109;i:22;i:112;i:23;i:114;i:24;i:115;i:25;i:116;i:25;i:117;i:25;i:121;i:26;i:132;i:27;i:138;i:28;i:139;i:29;i:143;i:30;i:144;i:31;i:145;i:32;i:148;i:33;i:151;i:34;i:152;i:35;i:156;i:36;i:157;i:37;i:158;i:38;i:161;i:39;i:163;i:40;i:164;i:41;i:165;i:41;i:166;i:41;i:170;i:42;i:185;i:43;i:186;i:44;i:187;i:45;i:188;i:46;i:189;i:47;i:191;i:48;i:192;i:49;i:196;i:50;i:199;i:51;i:202;i:52;i:203;i:52;i:204;i:52;i:205;i:52;i:206;i:52;i:207;i:52;i:208;i:52;i:209;i:52;i:210;i:52;i:211;i:52;i:214;i:53;i:217;i:54;i:218;i:55;i:221;i:56;i:236;i:57;i:237;i:58;i:239;i:59;i:240;i:60;i:244;i:61;i:247;i:62;i:250;i:63;i:251;i:63;i:252;i:63;i:253;i:63;i:254;i:63;i:255;i:63;i:256;i:63;i:257;i:63;i:258;i:63;i:259;i:63;i:260;i:63;i:263;i:64;i:266;i:65;i:267;i:66;i:270;i:67;i:283;i:68;i:286;i:69;i:287;i:70;i:291;i:71;i:294;i:72;i:295;i:72;i:296;i:72;i:297;i:72;i:298;i:72;i:299;i:73;i:302;i:74;i:303;i:74;i:304;i:74;i:305;i:74;i:306;i:74;i:307;i:75;i:309;i:76;i:311;i:77;i:312;i:78;i:315;i:79;i:317;i:80;i:319;i:81;i:320;i:82;i:322;i:83;i:325;i:84;i:328;i:85;i:329;i:86;i:333;i:87;i:335;i:88;i:348;i:89;i:349;i:90;i:353;i:91;i:356;i:92;i:357;i:92;i:358;i:92;i:359;i:92;i:360;i:92;i:361;i:93;i:364;i:94;i:365;i:94;i:366;i:94;i:367;i:94;i:368;i:94;i:369;i:95;i:372;i:96;i:374;i:97;i:376;i:98;i:377;i:99;i:380;i:100;i:382;i:101;i:383;i:102;i:385;i:103;i:388;i:104;i:389;i:105;i:393;i:106;i:395;i:107;i:407;i:108;i:408;i:109;i:409;i:110;i:410;i:111;i:414;i:112;i:417;i:113;i:418;i:114;i:419;i:115;i:420;i:116;i:424;i:117;i:436;i:118;i:437;i:119;i:439;i:120;i:451;i:121;i:452;i:122;i:454;i:123;i:455;i:124;i:457;i:125;i:458;i:126;i:459;i:127;i:460;i:128;i:461;i:129;i:462;i:130;i:463;i:131;i:464;i:132;i:465;i:133;i:469;i:134;i:471;i:135;i:483;i:136;i:486;i:137;i:487;i:138;i:488;i:139;i:490;i:140;i:494;i:141;i:495;i:142;i:496;i:143;i:498;i:144;i:502;i:145;i:503;i:146;i:507;i:147;i:525;i:148;i:527;i:149;i:528;i:150;i:529;i:151;i:530;i:152;i:531;i:153;i:548;i:154;i:551;i:155;i:553;i:156;i:554;i:157;i:555;i:158;i:556;i:159;i:557;i:160;i:574;i:161;i:577;i:162;i:578;i:163;i:581;i:164;i:583;i:165;i:585;i:166;i:586;i:167;i:590;i:168;i:591;i:169;i:593;i:170;i:594;i:171;i:596;i:172;i:600;i:173;i:601;i:174;i:603;i:175;i:606;i:176;i:624;i:177;i:627;i:178;i:628;i:179;i:631;i:180;i:633;i:181;i:635;i:182;i:636;i:183;i:640;i:184;i:642;i:185;i:643;i:186;i:644;i:187;i:646;i:188;i:647;i:189;i:649;i:190;i:652;i:191;i:656;i:192;i:657;i:193;i:659;i:194;i:662;i:195;i:679;i:196;i:680;i:197;i:684;i:198;i:685;i:199;i:688;i:200;i:691;i:201;i:692;i:202;i:693;i:203;i:694;i:204;i:699;i:205;i:713;i:206;i:714;i:207;i:718;i:208;i:719;i:209;i:722;i:210;i:725;i:211;i:726;i:212;i:727;i:213;i:728;i:214;i:733;i:215;i:747;i:216;i:748;i:217;i:752;i:218;i:753;i:219;i:754;i:220;i:755;i:221;i:756;i:222;i:757;i:223;i:759;i:224;i:761;i:225;i:762;i:226;i:763;i:227;i:764;i:228;i:765;i:229;i:766;i:230;i:768;i:231;i:770;i:232;i:771;i:233;i:773;i:234;i:774;i:235;i:776;i:236;i:777;i:237;i:780;i:238;i:789;i:239;i:790;i:239;i:791;i:239;i:792;i:239;i:798;i:240;i:800;i:241;i:801;i:242;i:806;i:243;i:818;i:244;i:819;i:245;i:822;i:246;i:823;i:247;i:825;i:248;i:837;i:249;i:838;i:250;i:841;i:251;i:843;i:252;i:844;i:253;i:846;i:254;i:847;i:255;i:850;i:256;i:851;i:257;i:855;i:258;i:867;i:259;i:868;i:260;i:872;i:261;i:873;i:262;i:877;i:263;i:878;i:264;i:882;i:265;i:883;i:266;i:887;i:267;i:888;i:268;i:889;i:268;i:890;i:268;i:891;i:268;i:892;i:268;i:893;i:268;i:894;i:268;i:895;i:268;i:897;i:269;i:898;i:270;i:903;i:271;}}