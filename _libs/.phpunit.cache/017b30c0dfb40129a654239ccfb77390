a:6:{s:9:"classesIn";a:1:{s:26:"Nzoom\Export\ExportService";a:6:{s:4:"name";s:13:"ExportService";s:14:"namespacedName";s:26:"Nzoom\Export\ExportService";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:13;s:7:"endLine";i:400;s:7:"methods";a:18:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:81:"__construct(Registry $registry, string $module, string $controller, string $type)";s:10:"visibility";s:6:"public";s:9:"startLine";i:81;s:7:"endLine";i:88;s:3:"ccn";i:1;}s:12:"setModelName";a:6:{s:10:"methodName";s:12:"setModelName";s:9:"signature";s:31:"setModelName(string $modelName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:96;s:7:"endLine";i:100;s:3:"ccn";i:1;}s:19:"setModelFactoryName";a:6:{s:10:"methodName";s:19:"setModelFactoryName";s:9:"signature";s:45:"setModelFactoryName(string $modelFactoryName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:108;s:7:"endLine";i:112;s:3:"ccn";i:1;}s:18:"createExportAction";a:6:{s:10:"methodName";s:18:"createExportAction";s:9:"signature";s:83:"createExportAction(string $module_check, array $types, array $typeSections): ?array";s:10:"visibility";s:6:"public";s:9:"startLine";i:122;s:7:"endLine";i:136;s:3:"ccn";i:1;}s:16:"createExportData";a:6:{s:10:"methodName";s:16:"createExportData";s:9:"signature";s:113:"createExportData(Outlook $outlook, array $filters, string $modelClass, $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:139;s:7:"endLine";i:143;s:3:"ccn";i:1;}s:26:"createExportDataWithTables";a:6:{s:10:"methodName";s:26:"createExportDataWithTables";s:9:"signature";s:125:"createExportDataWithTables(Outlook $outlook, array $filters, string $factoryClass, $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:145;s:7:"endLine";i:154;s:3:"ccn";i:1;}s:27:"createGeneratorFileStreamer";a:6:{s:10:"methodName";s:27:"createGeneratorFileStreamer";s:9:"signature";s:154:"createGeneratorFileStreamer(callable $generatorFunction, string $filename, string $mimeType, ?int $totalSize): Nzoom\Export\Streamer\GeneratorFileStreamer";s:10:"visibility";s:6:"public";s:9:"startLine";i:165;s:7:"endLine";i:168;s:3:"ccn";i:1;}s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:84:"export(string $filename, Nzoom\Export\Entity\ExportData $data, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:179;s:7:"endLine";i:196;s:3:"ccn";i:3;}s:16:"createTempStream";a:6:{s:10:"methodName";s:16:"createTempStream";s:9:"signature";s:18:"createTempStream()";s:10:"visibility";s:7:"private";s:9:"startLine";i:204;s:7:"endLine";i:213;s:3:"ccn";i:2;}s:15:"streamToBrowser";a:6:{s:10:"methodName";s:15:"streamToBrowser";s:9:"signature";s:74:"streamToBrowser($stream, string $downloadFilename, string $mimeType): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:224;s:7:"endLine";i:247;s:3:"ccn";i:3;}s:16:"getFormatFactory";a:6:{s:10:"methodName";s:16:"getFormatFactory";s:9:"signature";s:60:"getFormatFactory(): Nzoom\Export\Factory\ExportFormatFactory";s:10:"visibility";s:6:"public";s:9:"startLine";i:255;s:7:"endLine";i:262;s:3:"ccn";i:2;}s:16:"setFormatFactory";a:6:{s:10:"methodName";s:16:"setFormatFactory";s:9:"signature";s:79:"setFormatFactory(Nzoom\Export\Factory\ExportFormatFactory $formatFactory): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:270;s:7:"endLine";i:274;s:3:"ccn";i:1;}s:10:"getAdapter";a:6:{s:10:"methodName";s:10:"getAdapter";s:9:"signature";s:63:"getAdapter(): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:282;s:7:"endLine";i:289;s:3:"ccn";i:2;}s:19:"getSupportedFormats";a:6:{s:10:"methodName";s:19:"getSupportedFormats";s:9:"signature";s:28:"getSupportedFormats(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:296;s:7:"endLine";i:299;s:3:"ccn";i:1;}s:17:"isFormatSupported";a:6:{s:10:"methodName";s:17:"isFormatSupported";s:9:"signature";s:39:"isFormatSupported(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:307;s:7:"endLine";i:310;s:3:"ccn";i:1;}s:26:"getReferenceColumnForModel";a:6:{s:10:"methodName";s:26:"getReferenceColumnForModel";s:9:"signature";s:53:"getReferenceColumnForModel(string $modelClass): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:318;s:7:"endLine";i:335;s:3:"ccn";i:3;}s:17:"getExportFilename";a:6:{s:10:"methodName";s:17:"getExportFilename";s:9:"signature";s:45:"getExportFilename($prefix, string $extension)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:344;s:7:"endLine";i:364;s:3:"ccn";i:4;}s:17:"handleExportError";a:6:{s:10:"methodName";s:17:"handleExportError";s:9:"signature";s:40:"handleExportError($message, $statusCode)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:374;s:7:"endLine";i:399;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:401;s:18:"commentLinesOfCode";i:164;s:21:"nonCommentLinesOfCode";i:237;}s:15:"ignoredLinesFor";a:1:{i:0;i:13;}s:17:"executableLinesIn";a:85:{i:83;i:12;i:84;i:13;i:85;i:14;i:86;i:15;i:87;i:16;i:98;i:17;i:99;i:18;i:110;i:19;i:111;i:20;i:125;i:21;i:126;i:21;i:127;i:21;i:128;i:21;i:129;i:21;i:130;i:21;i:131;i:21;i:132;i:21;i:135;i:22;i:141;i:23;i:142;i:24;i:147;i:25;i:150;i:26;i:151;i:27;i:153;i:28;i:167;i:29;i:179;i:30;i:184;i:31;i:186;i:32;i:187;i:33;i:190;i:34;i:191;i:35;i:192;i:36;i:193;i:37;i:194;i:38;i:206;i:39;i:208;i:40;i:209;i:41;i:212;i:42;i:226;i:43;i:227;i:44;i:232;i:45;i:233;i:45;i:234;i:45;i:235;i:45;i:236;i:45;i:239;i:46;i:243;i:47;i:244;i:48;i:257;i:49;i:258;i:50;i:261;i:51;i:272;i:52;i:273;i:53;i:284;i:54;i:285;i:55;i:288;i:56;i:298;i:57;i:309;i:58;i:321;i:59;i:324;i:60;i:325;i:61;i:329;i:62;i:330;i:63;i:334;i:64;i:347;i:65;i:350;i:66;i:351;i:67;i:354;i:68;i:355;i:69;i:359;i:70;i:360;i:71;i:363;i:72;i:377;i:73;i:378;i:74;i:382;i:75;i:383;i:75;i:384;i:75;i:385;i:75;i:386;i:75;i:389;i:76;i:390;i:77;i:392;i:78;i:393;i:79;i:394;i:80;i:395;i:81;}}