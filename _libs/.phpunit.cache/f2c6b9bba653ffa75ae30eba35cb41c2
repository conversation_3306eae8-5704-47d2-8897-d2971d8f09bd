a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:869;s:7:"methods";a:25:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:106:"__construct(Registry $registry, string $referenceColumnName, string $referenceColumnLabel, array $options)";s:10:"visibility";s:6:"public";s:9:"startLine";i:48;s:7:"endLine";i:56;s:3:"ccn";i:1;}s:18:"getReferenceColumn";a:6:{s:10:"methodName";s:18:"getReferenceColumn";s:9:"signature";s:27:"getReferenceColumn(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:63;s:7:"endLine";i:66;s:3:"ccn";i:1;}s:17:"getDefaultOptions";a:6:{s:10:"methodName";s:17:"getDefaultOptions";s:9:"signature";s:26:"getDefaultOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:73;s:7:"endLine";i:80;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:85;s:7:"endLine";i:120;s:3:"ccn";i:10;}s:25:"discoverGroupingVariables";a:6:{s:10:"methodName";s:25:"discoverGroupingVariables";s:9:"signature";s:46:"discoverGroupingVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:128;s:7:"endLine";i:169;s:3:"ccn";i:10;}s:27:"createTableFromGroupingData";a:6:{s:10:"methodName";s:27:"createTableFromGroupingData";s:9:"signature";s:129:"createTableFromGroupingData(Model $model, string $varName, array $groupingData, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:180;s:7:"endLine";i:220;s:3:"ccn";i:4;}s:22:"createTableFromGT2Data";a:6:{s:10:"methodName";s:22:"createTableFromGT2Data";s:9:"signature";s:119:"createTableFromGT2Data(Model $model, string $varName, array $gt2Data, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:231;s:7:"endLine";i:269;s:3:"ccn";i:3;}s:22:"getOrCreateTableHeader";a:6:{s:10:"methodName";s:22:"getOrCreateTableHeader";s:9:"signature";s:133:"getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden, array $types): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:281;s:7:"endLine";i:326;s:3:"ccn";i:7;}s:25:"getOrCreateGT2TableHeader";a:6:{s:10:"methodName";s:25:"getOrCreateGT2TableHeader";s:9:"signature";s:91:"getOrCreateGT2TableHeader(string $tableType, array $vars): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:335;s:7:"endLine";i:378;s:3:"ccn";i:6;}s:21:"sortGT2VarsByPosition";a:6:{s:10:"methodName";s:21:"sortGT2VarsByPosition";s:9:"signature";s:41:"sortGT2VarsByPosition(array $vars): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:386;s:7:"endLine";i:407;s:3:"ccn";i:5;}s:15:"formatTableName";a:6:{s:10:"methodName";s:15:"formatTableName";s:9:"signature";s:40:"formatTableName(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:415;s:7:"endLine";i:422;s:3:"ccn";i:1;}s:27:"convertFieldTypeToValueType";a:6:{s:10:"methodName";s:27:"convertFieldTypeToValueType";s:9:"signature";s:54:"convertFieldTypeToValueType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:430;s:7:"endLine";i:455;s:3:"ccn";i:13;}s:15:"guessColumnType";a:6:{s:10:"methodName";s:15:"guessColumnType";s:9:"signature";s:40:"guessColumnType(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:463;s:7:"endLine";i:490;s:3:"ccn";i:6;}s:29:"populateTableFromGroupingData";a:6:{s:10:"methodName";s:29:"populateTableFromGroupingData";s:9:"signature";s:184:"populateTableFromGroupingData(Nzoom\Export\Entity\ExportTable $table, array $values, array $names, array $hidden, array $types, array $groupingData, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:504;s:7:"endLine";i:512;s:3:"ccn";i:3;}s:24:"populateTableFromGT2Data";a:6:{s:10:"methodName";s:24:"populateTableFromGT2Data";s:9:"signature";s:128:"populateTableFromGT2Data(Nzoom\Export\Entity\ExportTable $table, array $values, array $vars, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:523;s:7:"endLine";i:534;s:3:"ccn";i:3;}s:26:"createRecordFromGT2RowData";a:6:{s:10:"methodName";s:26:"createRecordFromGT2RowData";s:9:"signature";s:126:"createRecordFromGT2RowData(array $rowData, array $sortedVars, array $options, Model $model): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:545;s:7:"endLine";i:577;s:3:"ccn";i:5;}s:23:"createRecordFromRowData";a:6:{s:10:"methodName";s:23:"createRecordFromRowData";s:9:"signature";s:168:"createRecordFromRowData(array $rowData, array $names, array $hidden, array $types, array $groupingData, array $options, Model $model): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:591;s:7:"endLine";i:629;s:3:"ccn";i:6;}s:18:"resolveOptionLabel";a:6:{s:10:"methodName";s:18:"resolveOptionLabel";s:9:"signature";s:84:"resolveOptionLabel($value, ?string $fieldType, string $varName, array $groupingData)";s:10:"visibility";s:7:"private";s:9:"startLine";i:642;s:7:"endLine";i:666;s:3:"ccn";i:8;}s:29:"resolveOptionLabelFromVarData";a:6:{s:10:"methodName";s:29:"resolveOptionLabelFromVarData";s:9:"signature";s:72:"resolveOptionLabelFromVarData($value, string $fieldType, array $varData)";s:10:"visibility";s:7:"private";s:9:"startLine";i:676;s:7:"endLine";i:700;s:3:"ccn";i:8;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:711;s:7:"endLine";i:747;s:3:"ccn";i:15;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:752;s:7:"endLine";i:759;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:764;s:7:"endLine";i:773;s:3:"ccn";i:2;}s:15:"shouldSkipTable";a:6:{s:10:"methodName";s:15:"shouldSkipTable";s:9:"signature";s:61:"shouldSkipTable(Nzoom\Export\Entity\ExportTable $table): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:781;s:7:"endLine";i:792;s:3:"ccn";i:2;}s:15:"isTableRowEmpty";a:6:{s:10:"methodName";s:15:"isTableRowEmpty";s:9:"signature";s:63:"isTableRowEmpty(Nzoom\Export\Entity\ExportRecord $record): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:801;s:7:"endLine";i:820;s:3:"ccn";i:5;}s:18:"isValueEmptyOrZero";a:6:{s:10:"methodName";s:18:"isValueEmptyOrZero";s:9:"signature";s:32:"isValueEmptyOrZero($value): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:828;s:7:"endLine";i:868;s:3:"ccn";i:8;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:870;s:18:"commentLinesOfCode";i:276;s:21:"nonCommentLinesOfCode";i:594;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:303:{i:48;i:5;i:50;i:6;i:51;i:7;i:52;i:7;i:53;i:7;i:54;i:7;i:55;i:8;i:65;i:9;i:75;i:10;i:76;i:10;i:77;i:10;i:78;i:10;i:79;i:10;i:85;i:11;i:87;i:12;i:88;i:13;i:90;i:14;i:91;i:15;i:96;i:16;i:98;i:17;i:100;i:18;i:101;i:19;i:103;i:20;i:106;i:21;i:107;i:22;i:110;i:23;i:112;i:24;i:113;i:25;i:114;i:25;i:115;i:25;i:119;i:26;i:130;i:27;i:136;i:28;i:137;i:29;i:141;i:30;i:142;i:31;i:143;i:32;i:146;i:33;i:149;i:34;i:150;i:35;i:154;i:36;i:155;i:37;i:156;i:38;i:159;i:39;i:161;i:40;i:162;i:41;i:163;i:41;i:164;i:41;i:168;i:42;i:183;i:43;i:184;i:44;i:185;i:45;i:186;i:46;i:187;i:47;i:189;i:48;i:190;i:49;i:194;i:50;i:197;i:51;i:200;i:52;i:201;i:52;i:202;i:52;i:203;i:52;i:204;i:52;i:205;i:52;i:206;i:52;i:207;i:52;i:208;i:52;i:209;i:52;i:212;i:53;i:215;i:54;i:216;i:55;i:219;i:56;i:234;i:57;i:235;i:58;i:237;i:59;i:238;i:60;i:242;i:61;i:245;i:62;i:248;i:63;i:249;i:63;i:250;i:63;i:251;i:63;i:252;i:63;i:253;i:63;i:254;i:63;i:255;i:63;i:256;i:63;i:257;i:63;i:258;i:63;i:261;i:64;i:264;i:65;i:265;i:66;i:268;i:67;i:281;i:68;i:284;i:69;i:285;i:70;i:289;i:71;i:292;i:72;i:293;i:72;i:294;i:72;i:295;i:72;i:296;i:72;i:297;i:73;i:299;i:74;i:301;i:75;i:302;i:76;i:305;i:77;i:307;i:78;i:309;i:79;i:310;i:80;i:312;i:81;i:315;i:82;i:318;i:83;i:319;i:84;i:323;i:85;i:325;i:86;i:338;i:87;i:339;i:88;i:343;i:89;i:346;i:90;i:347;i:90;i:348;i:90;i:349;i:90;i:350;i:90;i:351;i:91;i:354;i:92;i:356;i:93;i:358;i:94;i:359;i:95;i:362;i:96;i:364;i:97;i:365;i:98;i:367;i:99;i:370;i:100;i:371;i:101;i:375;i:102;i:377;i:103;i:389;i:104;i:390;i:105;i:391;i:106;i:392;i:107;i:396;i:108;i:399;i:109;i:400;i:110;i:401;i:111;i:402;i:112;i:406;i:113;i:418;i:114;i:419;i:115;i:421;i:116;i:433;i:117;i:434;i:118;i:436;i:119;i:437;i:120;i:439;i:121;i:440;i:122;i:441;i:123;i:442;i:124;i:443;i:125;i:444;i:126;i:445;i:127;i:446;i:128;i:447;i:129;i:451;i:130;i:453;i:131;i:465;i:132;i:468;i:133;i:469;i:134;i:470;i:135;i:472;i:136;i:476;i:137;i:477;i:138;i:478;i:139;i:480;i:140;i:484;i:141;i:485;i:142;i:489;i:143;i:506;i:144;i:507;i:145;i:508;i:146;i:509;i:147;i:526;i:148;i:528;i:149;i:529;i:150;i:530;i:151;i:531;i:152;i:547;i:153;i:550;i:154;i:551;i:155;i:553;i:156;i:555;i:157;i:556;i:158;i:560;i:159;i:561;i:160;i:563;i:161;i:564;i:162;i:566;i:163;i:570;i:164;i:571;i:165;i:573;i:166;i:576;i:167;i:593;i:168;i:596;i:169;i:597;i:170;i:599;i:171;i:601;i:172;i:602;i:173;i:606;i:174;i:608;i:175;i:609;i:176;i:610;i:177;i:612;i:178;i:613;i:179;i:615;i:180;i:618;i:181;i:622;i:182;i:623;i:183;i:625;i:184;i:628;i:185;i:645;i:186;i:646;i:187;i:650;i:188;i:651;i:189;i:654;i:190;i:657;i:191;i:658;i:192;i:659;i:193;i:660;i:194;i:665;i:195;i:679;i:196;i:680;i:197;i:684;i:198;i:685;i:199;i:688;i:200;i:691;i:201;i:692;i:202;i:693;i:203;i:694;i:204;i:699;i:205;i:713;i:206;i:714;i:207;i:718;i:208;i:719;i:209;i:720;i:210;i:721;i:211;i:722;i:212;i:723;i:213;i:725;i:214;i:727;i:215;i:728;i:216;i:729;i:217;i:730;i:218;i:731;i:219;i:732;i:220;i:734;i:221;i:736;i:222;i:737;i:223;i:739;i:224;i:740;i:225;i:742;i:226;i:743;i:227;i:746;i:228;i:755;i:229;i:756;i:229;i:757;i:229;i:758;i:229;i:764;i:230;i:766;i:231;i:767;i:232;i:772;i:233;i:784;i:234;i:785;i:235;i:788;i:236;i:789;i:237;i:791;i:238;i:803;i:239;i:804;i:240;i:807;i:241;i:810;i:242;i:811;i:243;i:814;i:244;i:815;i:245;i:819;i:246;i:831;i:247;i:832;i:248;i:836;i:249;i:837;i:250;i:841;i:251;i:842;i:252;i:846;i:253;i:847;i:254;i:851;i:255;i:852;i:256;i:853;i:256;i:854;i:256;i:855;i:256;i:856;i:256;i:857;i:256;i:858;i:256;i:859;i:256;i:861;i:257;i:862;i:258;i:867;i:259;}}