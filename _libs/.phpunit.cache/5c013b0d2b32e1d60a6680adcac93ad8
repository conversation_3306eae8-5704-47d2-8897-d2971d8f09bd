a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:761;s:7:"methods";a:23:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:106:"__construct(Registry $registry, string $referenceColumnName, string $referenceColumnLabel, array $options)";s:10:"visibility";s:6:"public";s:9:"startLine";i:48;s:7:"endLine";i:56;s:3:"ccn";i:1;}s:18:"getReferenceColumn";a:6:{s:10:"methodName";s:18:"getReferenceColumn";s:9:"signature";s:27:"getReferenceColumn(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:63;s:7:"endLine";i:66;s:3:"ccn";i:1;}s:17:"getDefaultOptions";a:6:{s:10:"methodName";s:17:"getDefaultOptions";s:9:"signature";s:26:"getDefaultOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:73;s:7:"endLine";i:80;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:85;s:7:"endLine";i:120;s:3:"ccn";i:10;}s:25:"discoverGroupingVariables";a:6:{s:10:"methodName";s:25:"discoverGroupingVariables";s:9:"signature";s:46:"discoverGroupingVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:128;s:7:"endLine";i:169;s:3:"ccn";i:10;}s:27:"createTableFromGroupingData";a:6:{s:10:"methodName";s:27:"createTableFromGroupingData";s:9:"signature";s:129:"createTableFromGroupingData(Model $model, string $varName, array $groupingData, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:180;s:7:"endLine";i:221;s:3:"ccn";i:4;}s:22:"createTableFromGT2Data";a:6:{s:10:"methodName";s:22:"createTableFromGT2Data";s:9:"signature";s:119:"createTableFromGT2Data(Model $model, string $varName, array $gt2Data, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:232;s:7:"endLine";i:270;s:3:"ccn";i:3;}s:22:"getOrCreateTableHeader";a:6:{s:10:"methodName";s:22:"getOrCreateTableHeader";s:9:"signature";s:133:"getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden, array $types): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:282;s:7:"endLine";i:322;s:3:"ccn";i:6;}s:25:"getOrCreateGT2TableHeader";a:6:{s:10:"methodName";s:25:"getOrCreateGT2TableHeader";s:9:"signature";s:91:"getOrCreateGT2TableHeader(string $tableType, array $vars): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:331;s:7:"endLine";i:369;s:3:"ccn";i:5;}s:21:"sortGT2VarsByPosition";a:6:{s:10:"methodName";s:21:"sortGT2VarsByPosition";s:9:"signature";s:41:"sortGT2VarsByPosition(array $vars): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:377;s:7:"endLine";i:398;s:3:"ccn";i:5;}s:15:"formatTableName";a:6:{s:10:"methodName";s:15:"formatTableName";s:9:"signature";s:40:"formatTableName(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:406;s:7:"endLine";i:413;s:3:"ccn";i:1;}s:27:"convertFieldTypeToValueType";a:6:{s:10:"methodName";s:27:"convertFieldTypeToValueType";s:9:"signature";s:54:"convertFieldTypeToValueType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:421;s:7:"endLine";i:446;s:3:"ccn";i:13;}s:15:"guessColumnType";a:6:{s:10:"methodName";s:15:"guessColumnType";s:9:"signature";s:40:"guessColumnType(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:454;s:7:"endLine";i:481;s:3:"ccn";i:6;}s:29:"populateTableFromGroupingData";a:6:{s:10:"methodName";s:29:"populateTableFromGroupingData";s:9:"signature";s:163:"populateTableFromGroupingData(Nzoom\Export\Entity\ExportTable $table, array $values, array $names, array $hidden, array $types, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:494;s:7:"endLine";i:502;s:3:"ccn";i:3;}s:24:"populateTableFromGT2Data";a:6:{s:10:"methodName";s:24:"populateTableFromGT2Data";s:9:"signature";s:128:"populateTableFromGT2Data(Nzoom\Export\Entity\ExportTable $table, array $values, array $vars, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:513;s:7:"endLine";i:524;s:3:"ccn";i:3;}s:26:"createRecordFromGT2RowData";a:6:{s:10:"methodName";s:26:"createRecordFromGT2RowData";s:9:"signature";s:126:"createRecordFromGT2RowData(array $rowData, array $sortedVars, array $options, Model $model): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:535;s:7:"endLine";i:558;s:3:"ccn";i:4;}s:23:"createRecordFromRowData";a:6:{s:10:"methodName";s:23:"createRecordFromRowData";s:9:"signature";s:147:"createRecordFromRowData(array $rowData, array $names, array $hidden, array $types, array $options, Model $model): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:571;s:7:"endLine";i:599;s:3:"ccn";i:5;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:612;s:7:"endLine";i:648;s:3:"ccn";i:15;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:653;s:7:"endLine";i:660;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:665;s:7:"endLine";i:674;s:3:"ccn";i:2;}s:15:"shouldSkipTable";a:6:{s:10:"methodName";s:15:"shouldSkipTable";s:9:"signature";s:61:"shouldSkipTable(Nzoom\Export\Entity\ExportTable $table): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:682;s:7:"endLine";i:693;s:3:"ccn";i:2;}s:15:"isTableRowEmpty";a:6:{s:10:"methodName";s:15:"isTableRowEmpty";s:9:"signature";s:63:"isTableRowEmpty(Nzoom\Export\Entity\ExportRecord $record): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:701;s:7:"endLine";i:712;s:3:"ccn";i:3;}s:18:"isValueEmptyOrZero";a:6:{s:10:"methodName";s:18:"isValueEmptyOrZero";s:9:"signature";s:32:"isValueEmptyOrZero($value): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:720;s:7:"endLine";i:760;s:3:"ccn";i:8;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:762;s:18:"commentLinesOfCode";i:239;s:21:"nonCommentLinesOfCode";i:523;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:267:{i:48;i:5;i:50;i:6;i:51;i:7;i:52;i:7;i:53;i:7;i:54;i:7;i:55;i:8;i:65;i:9;i:75;i:10;i:76;i:10;i:77;i:10;i:78;i:10;i:79;i:10;i:85;i:11;i:87;i:12;i:88;i:13;i:90;i:14;i:91;i:15;i:96;i:16;i:98;i:17;i:100;i:18;i:101;i:19;i:103;i:20;i:106;i:21;i:107;i:22;i:110;i:23;i:112;i:24;i:113;i:25;i:114;i:25;i:115;i:25;i:119;i:26;i:130;i:27;i:136;i:28;i:137;i:29;i:141;i:30;i:142;i:31;i:143;i:32;i:146;i:33;i:149;i:34;i:150;i:35;i:154;i:36;i:155;i:37;i:156;i:38;i:159;i:39;i:161;i:40;i:162;i:41;i:163;i:41;i:164;i:41;i:168;i:42;i:184;i:43;i:185;i:44;i:186;i:45;i:187;i:46;i:188;i:47;i:190;i:48;i:191;i:49;i:195;i:50;i:198;i:51;i:201;i:52;i:202;i:52;i:203;i:52;i:204;i:52;i:205;i:52;i:206;i:52;i:207;i:52;i:208;i:52;i:209;i:52;i:210;i:52;i:213;i:53;i:216;i:54;i:217;i:55;i:220;i:56;i:235;i:57;i:236;i:58;i:238;i:59;i:239;i:60;i:243;i:61;i:246;i:62;i:249;i:63;i:250;i:63;i:251;i:63;i:252;i:63;i:253;i:63;i:254;i:63;i:255;i:63;i:256;i:63;i:257;i:63;i:258;i:63;i:259;i:63;i:262;i:64;i:265;i:65;i:266;i:66;i:269;i:67;i:282;i:68;i:285;i:69;i:286;i:70;i:290;i:71;i:293;i:72;i:294;i:72;i:295;i:72;i:296;i:72;i:297;i:72;i:298;i:73;i:300;i:74;i:302;i:75;i:303;i:76;i:306;i:77;i:308;i:78;i:309;i:79;i:311;i:80;i:314;i:81;i:315;i:82;i:319;i:83;i:321;i:84;i:334;i:85;i:335;i:86;i:339;i:87;i:342;i:88;i:343;i:88;i:344;i:88;i:345;i:88;i:346;i:88;i:347;i:89;i:350;i:90;i:352;i:91;i:354;i:92;i:355;i:93;i:358;i:94;i:359;i:95;i:361;i:96;i:362;i:97;i:366;i:98;i:368;i:99;i:380;i:100;i:381;i:101;i:382;i:102;i:383;i:103;i:387;i:104;i:390;i:105;i:391;i:106;i:392;i:107;i:393;i:108;i:397;i:109;i:409;i:110;i:410;i:111;i:412;i:112;i:424;i:113;i:425;i:114;i:427;i:115;i:428;i:116;i:430;i:117;i:431;i:118;i:432;i:119;i:433;i:120;i:434;i:121;i:435;i:122;i:436;i:123;i:437;i:124;i:438;i:125;i:442;i:126;i:444;i:127;i:456;i:128;i:459;i:129;i:460;i:130;i:461;i:131;i:463;i:132;i:467;i:133;i:468;i:134;i:469;i:135;i:471;i:136;i:475;i:137;i:476;i:138;i:480;i:139;i:496;i:140;i:497;i:141;i:498;i:142;i:499;i:143;i:516;i:144;i:518;i:145;i:519;i:146;i:520;i:147;i:521;i:148;i:537;i:149;i:540;i:150;i:541;i:151;i:543;i:152;i:545;i:153;i:546;i:154;i:550;i:155;i:551;i:156;i:552;i:157;i:554;i:158;i:557;i:159;i:573;i:160;i:576;i:161;i:577;i:162;i:579;i:163;i:581;i:164;i:582;i:165;i:586;i:166;i:588;i:167;i:589;i:168;i:591;i:169;i:593;i:170;i:595;i:171;i:598;i:172;i:614;i:173;i:615;i:174;i:619;i:175;i:620;i:176;i:621;i:177;i:622;i:178;i:623;i:179;i:624;i:180;i:626;i:181;i:628;i:182;i:629;i:183;i:630;i:184;i:631;i:185;i:632;i:186;i:633;i:187;i:635;i:188;i:637;i:189;i:638;i:190;i:640;i:191;i:641;i:192;i:643;i:193;i:644;i:194;i:647;i:195;i:656;i:196;i:657;i:196;i:658;i:196;i:659;i:196;i:665;i:197;i:667;i:198;i:668;i:199;i:673;i:200;i:685;i:201;i:686;i:202;i:689;i:203;i:690;i:204;i:692;i:205;i:703;i:206;i:705;i:207;i:706;i:208;i:707;i:209;i:711;i:210;i:723;i:211;i:724;i:212;i:728;i:213;i:729;i:214;i:733;i:215;i:734;i:216;i:738;i:217;i:739;i:218;i:743;i:219;i:744;i:220;i:745;i:220;i:746;i:220;i:747;i:220;i:748;i:220;i:749;i:220;i:750;i:220;i:751;i:220;i:753;i:221;i:754;i:222;i:759;i:223;}}