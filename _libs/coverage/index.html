<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item active">/var/www/Nzoom-Hella/_libs/Nzoom/Export</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="85.04" aria-valuemin="0" aria-valuemax="100" style="width: 85.04%">
           <span class="sr-only">85.04% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">85.04%</div></td>
       <td class="success small"><div align="right">2052&nbsp;/&nbsp;2413</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="70.88" aria-valuemin="0" aria-valuemax="100" style="width: 70.88%">
           <span class="sr-only">70.88% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">70.88%</div></td>
       <td class="success small"><div align="right">258&nbsp;/&nbsp;364</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="10.00" aria-valuemin="0" aria-valuemax="100" style="width: 10.00%">
           <span class="sr-only">10.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">10.00%</div></td>
       <td class="danger small"><div align="right">2&nbsp;/&nbsp;20</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-directory.svg" class="octicon" /><a href="Adapter/index.html">Adapter</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="82.20" aria-valuemin="0" aria-valuemax="100" style="width: 82.20%">
           <span class="sr-only">82.20% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">82.20%</div></td>
       <td class="success small"><div align="right">845&nbsp;/&nbsp;1028</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="59.79" aria-valuemin="0" aria-valuemax="100" style="width: 59.79%">
           <span class="sr-only">59.79% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">59.79%</div></td>
       <td class="warning small"><div align="right">58&nbsp;/&nbsp;97</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;4</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-directory.svg" class="octicon" /><a href="Entity/index.html">Entity</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="81.32" aria-valuemin="0" aria-valuemax="100" style="width: 81.32%">
           <span class="sr-only">81.32% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">81.32%</div></td>
       <td class="success small"><div align="right">344&nbsp;/&nbsp;423</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="78.87" aria-valuemin="0" aria-valuemax="100" style="width: 78.87%">
           <span class="sr-only">78.87% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">78.87%</div></td>
       <td class="success small"><div align="right">112&nbsp;/&nbsp;142</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="14.29" aria-valuemin="0" aria-valuemax="100" style="width: 14.29%">
           <span class="sr-only">14.29% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">14.29%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;7</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-directory.svg" class="octicon" /><a href="Factory/index.html">Factory</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="98.18" aria-valuemin="0" aria-valuemax="100" style="width: 98.18%">
           <span class="sr-only">98.18% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">98.18%</div></td>
       <td class="success small"><div align="right">54&nbsp;/&nbsp;55</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="88.89" aria-valuemin="0" aria-valuemax="100" style="width: 88.89%">
           <span class="sr-only">88.89% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">88.89%</div></td>
       <td class="success small"><div align="right">8&nbsp;/&nbsp;9</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-directory.svg" class="octicon" /><a href="Provider/index.html">Provider</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="92.36" aria-valuemin="0" aria-valuemax="100" style="width: 92.36%">
           <span class="sr-only">92.36% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">92.36%</div></td>
       <td class="success small"><div align="right">290&nbsp;/&nbsp;314</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="76.00" aria-valuemin="0" aria-valuemax="100" style="width: 76.00%">
           <span class="sr-only">76.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">76.00%</div></td>
       <td class="success small"><div align="right">19&nbsp;/&nbsp;25</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="_icons/file-directory.svg" class="octicon" /><a href="Streamer/index.html">Streamer</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="67.55" aria-valuemin="0" aria-valuemax="100" style="width: 67.55%">
           <span class="sr-only">67.55% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">67.55%</div></td>
       <td class="warning small"><div align="right">102&nbsp;/&nbsp;151</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="51.16" aria-valuemin="0" aria-valuemax="100" style="width: 51.16%">
           <span class="sr-only">51.16% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">51.16%</div></td>
       <td class="warning small"><div align="right">22&nbsp;/&nbsp;43</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;4</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-code.svg" class="octicon" /><a href="DataFactory.php.html">DataFactory.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="87.80" aria-valuemin="0" aria-valuemax="100" style="width: 87.80%">
           <span class="sr-only">87.80% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">87.80%</div></td>
       <td class="success small"><div align="right">144&nbsp;/&nbsp;164</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="64.71" aria-valuemin="0" aria-valuemax="100" style="width: 64.71%">
           <span class="sr-only">64.71% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">64.71%</div></td>
       <td class="warning small"><div align="right">11&nbsp;/&nbsp;17</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-code.svg" class="octicon" /><a href="ExportActionFactory.php.html">ExportActionFactory.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">197&nbsp;/&nbsp;197</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">13&nbsp;/&nbsp;13</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-code.svg" class="octicon" /><a href="ExportService.php.html">ExportService.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="93.83" aria-valuemin="0" aria-valuemax="100" style="width: 93.83%">
           <span class="sr-only">93.83% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">93.83%</div></td>
       <td class="success small"><div align="right">76&nbsp;/&nbsp;81</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83.33" aria-valuemin="0" aria-valuemax="100" style="width: 83.33%">
           <span class="sr-only">83.33% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">83.33%</div></td>
       <td class="success small"><div align="right">15&nbsp;/&nbsp;18</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 35%</span>
     <span class="warning"><strong>Medium</strong>: 35% to 70%</span>
     <span class="success"><strong>High</strong>: 70% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Wed Jun 25 7:20:38 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>
