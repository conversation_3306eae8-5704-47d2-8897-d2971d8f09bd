<?php

require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';

class Customers_Controller extends Controller {
    use \Nzoom\Mvc\ControllerTrait\GridBasedListTrait;
    use \Nzoom\Mvc\ControllerTrait\CompleteActionTrait;
    use \Nzoom\Mvc\ControllerTrait\MultiActionTrait;
    use \Nzoom\Mvc\ControllerTrait\GridExportActionTrait;

    /**
     * Model name of this controller
     */
    public $modelName = 'Customer';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Customers';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'adds', 'add', 'multiadd', 'view',
        'edit', 'export', 'transfer',
        'create', 'documents', 'tasks', 'events', 'projects',
        'attachments', 'tag',
        'relatives', 'history', 'comments', 'emails', 'minitasks', 'communications',
        'branches', 'contactpersons', 'trademarks',
        'manage_outlooks', 'print', 'printlist'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit', 'branches', 'contactpersons', 'trademarks'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit', 'relatives', 'history', 'communications'
    );

    /**
     * Action definitions for the upper right menu
     */
    public $actionDefinitionsUpRight = array(
        'manage_outlooks', 'print', 'printlist'
    );

    public $actionListPageMenu = [
        /** Actions that serve navigational purpouse and have no direct relation on the current page */
        'general' => ['list', 'adds', 'add', 'create', 'multiadd', 'transfer'],

        /** Actions that are conextualy dependeent on the page (document opened) */
        'context' => ['view', 'edit', 'tag'/*, 'setstatus'*/, 'contactpersons', 'trademarks', 'branches', 'attachments', 'communications', 'relatives', 'history' ],

        /** Actions that should be quick to access but unabtrusive to the user */
        'quick' => [/*'observer', */'print'],

        /** Actions that are not very frequent to use, can be hidden behind a menu */
        'infriquent' => [/*, 'archive',*/ 'manage_outlooks', 'printlist'],
    ];

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'multiadd',
        'view', 'edit', 'translate',
        'attachments', 'trademarks', 'history', 'communications', 'relatives'
    );

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'ajax_select',
        'ajax_email_select',
        'ajax_get_all_emails',
        'ajax_branches',
        'ajax_contact_persons',
        'ajax_fill_users_department_options',
        'ajax_fill_contact_persons_options',
        'ajax_update_customers_info_panel',
    );

    /**
     * The fields which will switch the additional variables in the search panel
     */
    public static $searchAdditionalVarsSwitch = 'c.type';

    /**
     * Actions where side panels for model can be displayed
     */
    public static $actionsSidePanel = array('view', 'edit');

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'adds':
            //takes the add action
            $action = $this->registry['request']->get('operation');
            //checks if the action is valid
            if (preg_match('#^(add|multiadd)$#', $action)) {
                $this->setAction($action);
            } else {
                $this->setAction('view');
            }
            //construct the name of the method
            $method = '_' . $action;
            //calls the method
            $this->$method();
            break;
        case 'add':
            $this->_add();
            break;
        case 'addnew':
            $this->_addNew();
            break;
        case 'addquick':
            $this->_addQuick();
            break;
        case 'multiadd':
            $this->_multiAdd();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
        case 'managevars':
            $this->_edit();
            break;
        case 'multiedit':
            $this->_multiEdit();
            break;
        case 'view':
        case 'viewvars':
            $this->_view();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'export':
        case 'printlist':
            if ($this->registry['theme']->isModern()) {
                // Using the GridExportActionTrait implementation
                $this->_export2();
            } else {
                $this->_export();
            }
            break;
        case 'audit':
            $this->_audit();
            break;
        case 'delete':
            $this->_delete();
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'communications':
            $this->_communications();
            break;
        case 'relatives':
            $this->_relatives();
            break;
        case 'history':
            $this->_history();
            break;
        case 'branches':
            $this->_branches();
            break;
        case 'contactpersons':
            $this->_contactPersons();
            break;
        case 'trademarks':
            $this->_trademarks();
            break;
        case 'filter':
            $this->_filter();
            break;
        /* case 'filter_customers':
            $this->_filterCustomers();
            break; */
        case 'calculate':
            $this->_calculate();
            break;
        case 'ajax_fill_users_department_options':
            $this->_fillUsersDepartmentOptions();
            break;
        case 'ajax_fill_contact_persons_options':
            $this->_fillContactPersonsOptions();
            break;
        case 'ajax_update_customers_info_panel':
            $this->_updateCustomersInfoPanel();
            break;
        /* case 'add_default_assigned_users':
            $this->addDefaultAssignedUsers();
            break; */
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'ajax_getfiles':
            $this->_getFiles();
            break;
        case 'create':
            $this->_create();
            break;
        case 'attachments':
            $this->_attachments();
            break;
        case 'delfile':
        case 'getfile':
        case 'viewfile':
            $this->_manageFile();
            break;
        case 'ajax_select':
            $this->_select();
            break;
        case 'ajax_email_select':
            $this->_emailSelect();
            break;
        case 'ajax_get_all_emails':
            $this->_getAllEmails();
            break;
        case 'ajax_branches':
            $this->_getCustomerBranches();
            break;
        case 'ajax_contact_persons':
            $this->_getCustomerContactPersons();
            break;
        case 'ajax_checkname':
            $this->_checkName();
            break;
        case 'search':
            $this->_search();
            break;
        case 'subpanel':
            $this->_subpanel();
            break;
        case 'ajax_get_totals':
            $this->_getTotals();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'transfer':
            $this->_transfer();
            break;
        case 'print':
            $this->_print();
            break;
        case 'multiprint':
            $this->_multiPrint();
            break;
        case 'tag':
            $this->_tag();
            break;
        case 'multitag':
            $this->_multiTag();
            break;
        case 'multiremovetag':
            $this->_multiRemoveTag();
            break;
        case 'ajax_tag':
            $this->_getTags();
            break;
        case 'ajax_sidepanel':
            $this->_sidePanel();
            break;
        case 'attach_additional_field_file':
            $this->_attachAdditionalFieldFile();
            break;
        case 'franky':
            $this->_franky();
            break;
        case 'bb':
            $this->_bb();
            break;
        case 'savegroupvar':
            $this->_saveGroupVar();
            break;
        case 'ajax_import_table':
            $this->_importTable();
            break;
        case 'ajax_import_table_configurator':
            $this->_importTableConfigurator();
            break;
        case 'button_link_prepare':
            $this->_buttonLinkPrepare();
            break;
        case 'prepare_map':
            $this->_prepareMap();
            break;
        case 'getadvancedsearchoptions':
            $this->_getAdvancedSearchOptions();
            break;
        case 'getListColumnsDefinitions':
            $this->_getListColumnsDefinitions();
            break;
        case 'listIds':
            $this->_listIds();
            break;
        case 'getListTitle':
            $this->_getListTitle();
            break;
        case 'listData':
            $this->_listData();
            break;
        case 'getListMultiActionsPanel':
            $this->_getListMultiActionsPanel('', 'tags,multiprint,export');
            break;
        case 'getListActions':
            $this->_getListActions();
            break;
        case 'saveFilter':
            $this->_saveFilter();
            break;
        case 'loadFilter':
            $this->_loadFilter();
            break;
        case 'deleteFilter':
            $this->_deleteFilter();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _listData() {
        /** @var \Request $request */
        $request = $this->registry['request'];

        $accept = $request->getHeader('Accept');
        if (!preg_match("/.*json/i", $accept)
            || false !== stripos($accept, "html")) {
            return;
        }

        // The rights are cheked based on the action name. Documents::prepareRightsFilters() is called in the model
        $this->registry->set('action', 'list', true);
        $filters = $this->prepFiltersFromRequest();
        $this->registry->set('getTags', true, true);

        $outlook = $this->getCurrentOutlook($filters);

        $shouldCheckPermissions= [];

        if (isset($outlook) && $outlook) {
            $modelFields = $outlook->get('current_custom_fields');
            $modelFieldsNames = array_column($modelFields, 'name');
            $filters['get_fields'] = $modelFieldsNames;


            if (in_array('tags', $modelFieldsNames)) {
                //set flag to get tags for current model
                $this->registry->set('getTags', true, true);
            }

            $shouldCheckAssignmentsPermissions = in_array('owner', $modelFieldsNames)
                || in_array('observer', $modelFieldsNames)
                || in_array('responsible', $modelFieldsNames)
                || in_array('decision', $modelFieldsNames);
        }

        list($records, $pagination) = $this->modelFactoryName::pagedSearch($this->registry, $filters);

        if (isset($outlook) && $outlook) {
            $additionalVars = $outlook->getModelAdditionalFields();
            $basicVars = $outlook->getModelFields();
            if ($additionalVars) {
                $outlook->clearNotPermittedVars();
            }
            foreach ($records as $record) {
                $this->prepListRecordFileuploadAttributes($record, $additionalVars);

                if (array_key_exists('relatives_children',$basicVars)) {
                    $record->set('relatives_children', $record->getFirstLevelRelatedDocuments('child'), true);
                }

                if (array_key_exists('relatives_parent',$basicVars)) {
                    $record->set('relatives_parent', $record->getFirstLevelRelatedDocuments('parent'), true);
                }

                if (in_array('tags', $modelFieldsNames)) {
                    $record->checkPermissions('tags_view');
                    $record->checkPermissions('tags_edit');
                }

                if ($shouldCheckAssignmentsPermissions) {
                    $record->checkPermissions('assign');
                }

                $record->sanitize();
                $record->properties['cached_assoc_vars'] = null;

                unset($record->properties['cached_assoc_vars']);
            }
        }

        if (in_array(true, $shouldCheckPermissions, true)) {
            foreach ($records as $record) {
                foreach ($shouldCheckPermissions as $action=>$test) {
                    $record->checkPermissions($action);
                }
            }
        }
        $data = [
            'pagination' => $pagination,
            'records' =>  $records,
        ];

        $this->actionCompleted = true;
        $this->registry->set('ajax_result', json_encode($data), true);
    }


    /**
     * listing of all models
     */
    //private function _list() {
        //function to transfer the emails and links from customers_hyperlinks table to customers
        // EXECUTE TEMPORARY METHOD
        // Customers::updateContacts($this->registry);
        // EXECUTE TEMPORARY METHOD
        // Customers::updateCustomersRelations($this->registry);
        //all the actions are within the viewer
    /*    return true;
    }*/

    /**
     * search of models
     */
    private function _search() {
        $this->redirectSearch2ListForModernTheme();
        //all the actions are within the viewer
        return true;
    }

    /**
     * Add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        //check validity of the type
        $type_id = $request->get('type');
        $type = '';
        require_once $this->modelsDir . 'customers.types.factory.php';
        if (!empty($type_id)) {
            $filters = array('where' => array('ct.id = ' . $type_id,
                                              'ct.active = 1'),
                             'sanitize' => true);
            $type = Customers_Types::searchOne($this->registry, $filters);
        } elseif ($request->get('allowed_types')) {
            $types = explode(',', $request->get('allowed_types'));
            if (count($types) == 1) {
                $filters = array('where' => array('ct.id = ' . $types[0],
                                                  'ct.active = 1'),
                                 'sanitize' => true);
                $type = Customers_Types::searchOne($this->registry, $filters);
                if ($type) {
                    $request->set('type', $types[0], 'all', true);
                }
            }
        }

        if ($type || $request->isPost()) {
            $type_permission = ($type) ? $this->checkActionPermissions($this->module . $type->get('id'), 'add') : false;

            if (!$type || !$type_permission) {
                //invalid type, redirect to list
                $type_name = $type && $type->get('name') ? $type->get('name') : $this->i18n('customer');
                $this->registry['messages']->setError($this->i18n('error_customers_add_failed', array($type_name)), '', -1);
                $this->registry['messages']->setError($this->i18n('error_invalid_type'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'list');
            }
        }

        //check if details are submitted via POST
        if ($request->isPost()) {
            // clear extra whitespace
            $request->clearWhitespace(array('name', 'lastname'));

            //build the model from the POST
            $customer = Customers::buildModel($this->registry);

            //sets the properties of the type to the current model
            $customer->set('type_name', $type->get('name'), true);

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $customer->unsetVars();
            $customer->getVars();

            $to_save = false;
            if ($customer->validate('add')) {
                //get setting whether to check for name similarity or not
                $checkname = $this->registry['config']->getParam('customers', 'checkname');
                if (is_array($checkname)) {
                    $checkname = 1;
                }

                // if name should not be checked for similarity or
                // if model is valid and levenshtein is submitted -> save model
                if (!$checkname || !$request->get('check_similar_names')) {
                    $to_save = true;
                } else {
                    //if model is valid and no similar names are found -> save model
                    $has_names = $this->_checkName(true);
                    if (!$has_names) {
                        $to_save = true;
                    } else {
                        $this->registry->set('levenshtein', $has_names);
                    }
                }

                // if model should be saved
                if ($to_save) {

                    // build a blank model, just set the type to get the additional variables
                    $old_customer = new Customer($this->registry, array('type' => $customer->get('type')));
                    // get additional vars and values:
                    // get old vars before post
                    $this->registry->set('get_old_vars', true, true);
                    $old_customer->getVars();
                    $this->registry->set('get_old_vars', false, true);

                    // check transition
                    $old_customer->new_model = $customer;
                    $trans = $this->checkTransition($old_customer);
                    unset($old_customer->new_model);

                    // escape slashes before adding
                    $customer->slashesEscape();

                    if ($trans && $customer->add()) {

                        $this->old_model = $old_customer->sanitize();

                        $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                         'model_lang' => $customer->get('model_lang'));
                        $new_customer = Customers::searchOne($this->registry, $filters);
                        $this->registry->set('get_old_vars', true, true);
                        $new_customer->getVars();

                        $audit_parent = Customers_History::saveData($this->registry,
                                                                    array('model' => $customer,
                                                                          'action_type' => 'add',
                                                                          'new_model' => $new_customer,
                                                                          'old_model' => $this->old_model));

                        if ($new_customer->get('assigned') && ($new_customer->get('assigned') != $this->registry['currentUser']->get('id'))) {
                            $new_customer->sendAssignNotification();
                        }
                        //show corresponding message
                        $this->registry['messages']->setMessage($this->i18n('message_customers_add_success', array($new_customer->getModelTypeName())), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);

                        //change returning link if exists
                        if ($this->registry['session']->isRequested('return_link')) {
                            $redirect_link = $this->registry['session']->get('return_link');
                            $redirect_link = preg_replace("#&customer=[0-9]*#", '', $redirect_link);
                            $redirect_link = $redirect_link . '&customer=' . $customer->get('id');
                            $this->registry['session']->set('return_link', $redirect_link, '', true);
                        }

                        //the model was successfully saved set action as completed
                        $this->actionCompleted = true;

                        //get the customer from previously saved object (used for automations)
                        $customer = $new_customer;
                    } else {
                        // strip slashes if adding was unsuccessful
                        $customer->slashesStrip();

                        $error = true;
                    }
                }
            } else {
                $error = true;
            }
            if (!empty($error)) {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_customers_add_failed', array($customer->getModelTypeName())), '', -2);
            }
        } else {
            //create an empty customer model
            $customer = Customers::buildModel($this->registry);

            //bb specific
            if (!$customer->get('model_id')) {
                $customer->set('model_id', time(), true);
            }

            if ($customer->get('in_dds')) {
                //load customer information from European customer Registry
                include_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
                $info = Finance_Companies::getCompanyInfoByVAT($customer->get('in_dds'));
                if (!empty($info)) {
                    $customer->set('in_dds', $info['vat'], true);
                    $customer->set('eik', $info['eik'], true);
                    $customer->set('ucn', $info['eik'], true);
                    if (!empty($info['name'])) {
                        $customer->set('name', $info['name'], true);
                        $customer->set('company_name', $info['name'], true);
                    }
                    if (!empty($info['address'])) {
                        $customer->set('registration_address', $info['address'], true);
                        $customer->set('address_by_personal_id', $info['address'], true);
                    }
                    if (!empty($info['countryCode'])) {
                        $customer->set('country', $info['countryCode'], true);
                    }
                }
            }

            //set default values from the customer type
            if ($type) {
                $customer->set('group', $type->getDefaultGroup(), true);
            }
        }

        if (!empty($customer)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('customer', $customer->sanitize());
        }

        return true;
    }

    /**
     * Change form for adding a new model when type or kind of customer is changed
     */
    private function _addNew() {
        $request = &$this->registry['request'];

        // clear extra whitespace
        $request->clearWhitespace(array('name', 'lastname'));

        $customer = Customers::buildModel($this->registry);
        if ($request->get('is_company', 'get')) {
            $customer->set('is_company', 1, true);
        } else {
            $customer->set('is_company', 0, true);
        }

        // if chaging type, delete saved BB data (bb, cstm, files - not implemented yet)
        if ($request->get('type_changed') && $bb_params = $customer->getBB(array('model_id' => $customer->get('model_id')))) {
            $bb_params = reset($bb_params);
            $bb_params = array_intersect_key($bb_params, array_flip(array('model_id', 'model_type', 'bb_num')));
            $customer->delBB($bb_params);
        }
        $customer->getVarsForTemplate(false);
        $customer->prepareBBVarsForTemplate('request');

        $vars = $customer->get('vars');
        foreach ($vars as $var_idx => $var) {
            if ($var['type'] == 'grouping') {
                foreach ($var['names'] as $gr_idx => $var_name) {
                    $current_var = $customer->get($var_name);
                    if (!empty($current_var)) {
                        if (is_array($current_var)) {
                            foreach ($current_var as $var_row_idx => $var_row_val) {
                                $vars[$var_idx]['values'][$var_row_idx][$gr_idx] = $var_row_val;
                            }
                        } else {
                            $vars[$var_idx]['values'][0][$gr_idx] = $current_var;
                        }
                    }
                }
            } elseif ($var['type'] == 'config') {
                foreach ($var['names'] as $var_name) {
                    $current_var = $customer->get($var_name);
                    if (!empty($current_var) && is_array($current_var)) {
                        $vars[$var_idx]['values'][$var_name] = reset($current_var);
                    }
                }
            } else {
                $current_var = $customer->get($var['name']);
                if (!empty($current_var)) {
                    if (is_array($current_var)) {
                        if ($var['type'] == 'checkbox_group') {
                            $vars[$var_idx]['value'] = $current_var;
                        } else {
                            $vars[$var_idx]['value'] = reset($current_var);
                        }
                    } else {
                        if ($var['type'] == 'checkbox_group') {
                            $vars[$var_idx]['value'] = array($current_var);
                        } else {
                            $vars[$var_idx]['value'] = $current_var;
                        }
                    }
                }
            }
        }
        $customer->set('vars', $vars, true);

        if (!empty($customer)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('customer', $customer->sanitize());
        }

        return true;
    }

    /**
     * communication concerning the customer (comments and emails)
     */
    private function _communications() {
        $request = &$this->registry['request'];

        //check the request for selected communication type
        $communication_type = $request->get('communication_type');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('c.id = ' . $id ));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $customer = Customers::searchOne($this->registry, $filters);
        if (!empty($customer)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            $this->registry->set('communication_type', $communication_type, true);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('customer')) {
                $this->registry->set('customer', $customer->sanitize());
            }

            require_once PH_MODULES_DIR . 'communications/viewers/communications.viewer.php';
            $this->viewer = new Communications_Viewer($this->registry, true);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Branches for the customer
     */
    private function _branches() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters['where'][] = 'c.id = ' . $id;
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $customer = Customers::searchOne($this->registry, $filters);
        if (!empty($customer)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('customer')) {
                $this->registry->set('customer', $customer->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Contact persons for the customer
     */
    private function _contactPersons() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters['where'][] = 'c.id = ' . $id;
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang']= $model_lang;
        }
        $customer = Customers::searchOne($this->registry, $filters);
        if (!empty($customer)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('customer')) {
                $this->registry->set('customer', $customer->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Trademarks for the customer
     */
    private function _trademarks() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters['where'][] = 'c.id = ' . $id;
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang']= $model_lang;
        }
        $customer = Customers::searchOne($this->registry, $filters);

        if (!empty($customer)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            //check if details are submitted via POST
            if ($request->isPost()) {
                $old_customer = clone $customer;
                $old_customer->getTrademarks();
                $old_customer->sanitize();

                if ($customer->saveTrademarks()) {
                    $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                     'model_lang' => $request->get('model_lang'));
                    $new_customer = Customers::searchOne($this->registry, $filters);
                    $new_customer->getTrademarks();
                    $new_customer->sanitize();

                    Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'trademarks', 'new_model' => $new_customer, 'old_model' => $old_customer));

                    $this->registry['messages']->setMessage($this->i18n('message_customers_trademarks_save_success', array($this->i18n('trademarks_lowercase'))), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);

                    //the model was successfully saved set action as completed
                    $this->actionCompleted = true;
                } else {
                    $this->registry['messages']->setError($this->i18n('error_customers_trademarks_save_failed', array($this->i18n('trademarks_lowercase'))), '', -1);
                }
            }

            $customer->getTrademarks();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('customer')) {
                $this->registry->set('customer', $customer->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Relatives for the models
     */
    private function _relatives() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters['where'][] = 'c.id = ' . $id;
        if ($model_lang = $request->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $customer = Customers::searchOne($this->registry, $filters);
        if (!empty($customer)) {
            if ($request->isPost()) {
                if ($customer->updateRelatives()) {
                    //show corresponding message
                    $this->registry['messages']->setMessage($this->i18n('message_customers_relate_success'), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);

                    //the model was successfully saved set action as completed
                    $this->actionCompleted = true;
                } else {
                    //some error occurred
                    //show corresponding error(s)
                    $this->registry['messages']->setError($this->i18n('error_customers_relate_failed'), '', -1);
                }
            }

            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            $customer->getParents();

            //get the relatives tree
            $this->registry->set('relatives_tree', $customer->getRelativesTree());

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('customer')) {
                $this->registry->set('customer', $customer->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * History of the customer
     */
    private function _history() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $filters = array(
            'where' => array(
                'c.id = \'' . $request->get($this->action) . '\'',
            ),
            'model_lang' => $request->get('model_lang') ?: $this->registry['lang'],
        );
        $customer = Customers::searchOne($this->registry, $filters);

        if ($request->get('source') == 'ajax') {
            if ($customer && $this->checkAccessOwnership($customer, false)) {
                if (!$this->registry->isRegistered('customer')) {
                    $this->registry->set('customer', $customer->sanitize());
                }

                require_once $this->viewersDir . 'customers.history.viewer.php';
                $viewer = new Customers_History_Viewer($this->registry);
                $viewer->prepare();
                if ($request->get('history_activity')) {
                    if ($request->get('page') <= 1) {
                        $viewer->prepareTitleBar();
                    }
                    $viewer->setFrameset('_history_activity.html');
                } else {
                    $viewer->setFrameset('_history.html');
                }
                $viewer->display();
            }
            exit;
        }

        if (!empty($customer)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('customer')) {
                $this->registry->set('customer', $customer->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add a single model (from popup window)
     */
    private function _addQuick() {
        $request = &$this->registry['request'];

        require_once $this->modelsDir . 'customers.types.factory.php';

        //check if details are submitted via POST
        if ($request->isPost()) {
            // clear extra whitespace
            $request->clearWhitespace(array('name', 'lastname'));

            //build the model from the POST
            $customer = Customers::buildModel($this->registry);

            $type_id = $customer->get('type');
            $type = '';
            if (!empty($type_id)) {
                $filters = array('where' => array('ct.id = ' . $type_id,
                                                  'ct.active = 1'),
                                 'sanitize' => true);
                $type = Customers_Types::searchOne($this->registry, $filters);
            }
            if ($type) {
                $customer->set('type_name', $type->get('name'), true);
            }

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $customer->unsetVars();
            $customer->getVars();

            $to_save = false;
            if ($customer->validate('add')) {
                //get setting whether to check for name similarity or not
                $checkname = $this->registry['config']->getParam('customers', 'checkname');
                if (is_array($checkname)) {
                    $checkname = 1;
                }

                // if name should not be checked for similarity or
                // if model is valid and levenshtein is submitted -> save model
                if (!$checkname || !$request->get('check_similar_names')) {
                    $to_save = true;
                } else {
                    //if model is valid and no similar names are found -> save model
                    $has_names = $this->_checkName(true);
                    if (!$has_names) {
                        $to_save = true;
                    } else {
                        $this->registry->set('levenshtein', $has_names);
                    }
                }

                // if model should be saved
                if ($to_save) {

                    // build a blank model, just set the type to get the additional variables
                    $old_customer = new Customer($this->registry, array('type' => $customer->get('type')));
                    // get additional vars and values:
                    // get old vars before post
                    $this->registry->set('get_old_vars', true, true);
                    $old_customer->getVars();
                    $this->registry->set('get_old_vars', false, true);

                    // check transition
                    $old_customer->new_model = $customer;
                    $trans = $this->checkTransition($old_customer);
                    unset($old_customer->new_model);

                    // escape slashes before adding
                    $customer->slashesEscape();

                    if ($customer->add()) {

                        $this->old_model = $old_customer->sanitize();

                        $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                         'model_lang' => $customer->get('model_lang'));
                        $new_customer = Customers::searchOne($this->registry, $filters);
                        $this->registry->set('get_old_vars', true, true);
                        $new_customer->getVars();

                        $audit_parent = Customers_History::saveData($this->registry,
                                                                    array('model' => $customer,
                                                                          'action_type' => 'add',
                                                                          'new_model' => $new_customer,
                                                                          'old_model' => $this->old_model));

                        if ($new_customer->get('assigned') && ($new_customer->get('assigned') != $this->registry['currentUser']->get('id'))) {
                            $new_customer->sendAssignNotification();
                        }

                        //show corresponding message
                        $this->registry['messages']->setMessage($this->i18n('message_customers_add_success', array($new_customer->getModelTypeName())), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);

                        //the model was successfully saved set action as completed
                        $this->actionCompleted = true;

                        //get the customer from previously saved object (used for automations)
                        $customer = $new_customer;

                        $this->registry['session']->remove($request['uniqid'], 'autocomplete_params');
                    } else {
                        // strip slashes if adding was unsuccessful
                        $customer->slashesStrip();

                        $error = true;
                    }
                }
            } else {
                $error = true;
            }
            if (!empty($error)) {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_customers_add_failed', array($customer->getModelTypeName())), '', -1);
            }
        } else {
            //create an empty customer model
            $customer = Customers::buildModel($this->registry);

            if (!$customer->get('model_id')) {
                $customer->set('model_id', time(), true);
            }
        }

        if (!empty($customer)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('customer', $customer->sanitize());
        }

        return true;
    }

    /**
     * Add multiple models
     */
    private function _multiAdd() {
        $request = &$this->registry['request'];

        require_once $this->modelsDir . 'customers.types.factory.php';

        //check validity of the type
        $type_id = $request->get('type');
        $type = '';
        if (!empty($type_id)) {
            $filters = array('where' => array ('ct.id = ' . $type_id,
                                               'ct.active = 1'),
                             'sanitize' => true);
            $type = Customers_Types::searchOne($this->registry, $filters);
        }

        $type_name = $type && $type->get('name') ? $type->get('name') : $this->i18n('customer');
        $type_name_plural = $type && $type->get('name_plural') ? $type->get('name_plural') : $this->i18n('customers');

        $type_permission = ($type) ? $this->checkActionPermissions($this->module . $type->get('id'), 'multiadd') : false;

        if (!$type || !$type_permission) {
            //invalid type, redirect to list
            $this->registry['messages']->setError($this->i18n('error_customers_add_failed', array($type_name)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_invalid_type'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'list');
        } else {
            //assign the type to the registry
            $this->registry['custype'] = $type;
        }

        $is_company = $request->get('is_company');
        if (($type->get('kind') == 'person' && $is_company) || ($type->get('kind') == 'company' && !$is_company)) {
            //invalid kind, redirect to list
            $this->registry['messages']->setError($this->i18n('error_customers_kind_add_failed',
                                                  array($type_name_plural, $this->i18n('customers_' . ($type->get('kind') == 'person' ? 'company' : 'person')))), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'list');
        }

        //add additional variables
        $customer = Customers::buildModelIndex($this->registry, 0);
        $customer->set('type', $type_id, true);

        $multivars = $customer->getFieldsMulti($this->action);

        //get main variables
        $vars = $this->getBasicVarsDefs($is_company, $customer);

        $vars = $this->mergeVars($customer, $vars, $multivars);
        if (count($vars) == 0) {
            //show error no variables
            $this->registry['messages']->setError($this->i18n('error_customers_no_multi_operation_variables'));
            $this->registry['messages']->insertInSession($this->registry);

            //redirect to the listing
            $this->redirect($this->module, 'list');
        }

        $this->registry->set('type_name', $type_name, true);

        $this->actionCompleted = Customers::multiAdd($this->registry, $vars, $this);

        $this->registry->remove('type_name');

        if ($request->isPost()) {
            if ($this->actionCompleted) {
                $this->registry['messages']->setMessage($this->i18n('message_customers_multiadd_success', array($type_name_plural)), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $this->registry['messages']->setError($this->i18n('error_customers_multiadd_failed', array($type_name_plural)), '', -1);
            }
        }

        return true;
    }

    /**
     * edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // make sure action in registry is 'edit'
        $this->setAction('edit');

        //check if details are submitted via POST
        if ($request->isPost()) {
            // clear extra whitespace
            $request->clearWhitespace(array('name', 'lastname'));

            // build the model from the POST
            $customer = Customers::buildModel($this->registry);

            // get old model from db
            $filters = array('where' => array('c.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $old_customer = Customers::searchOne($this->registry, $filters);

            // get additional vars and values:
            // get old vars before post
            $this->registry->set('get_old_vars', true, true);
            $old_customer->getVars();

            // get post vars
            $this->registry->set('get_old_vars', false, true);
            $customer->unsetVars();
            $customer->getVars();

            // check transition
            $old_customer->new_model = $customer;
            $trans = $this->checkTransition($old_customer);
            unset($old_customer->new_model);

            if ($trans && $customer->save()) {

                $this->old_model = $old_customer->sanitize();

                $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                 'model_lang' => $customer->get('model_lang'));
                $new_customer = Customers::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_customer->getVars();

                $audit_parent = Customers_History::saveData($this->registry,
                                                            array('model' => $new_customer,
                                                                  'action_type' => 'edit',
                                                                  'new_model' => $new_customer,
                                                                  'old_model' => $old_customer));

                if ($new_customer->get('assigned') && $old_customer->get('assigned') != $new_customer->get('assigned') && $new_customer->get('assigned') != $this->registry['currentUser']->get('id')) {
                    $new_customer->sendAssignNotification();
                }

                // if updating emails targetlists is enabled
                if ($this->registry['config']->getParam('customers', 'update_emails_targetlists')) {
                    Customers::updateEmailsTargetlists($this->registry, $new_customer, $old_customer);
                }

                //show message 'message_customers_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_customers_edit_success', array($new_customer->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;

                //get the customer from previously saved object
                $customer = $new_customer;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_customers_edit_failed', array($old_customer->getModelTypeName())), '', -2);
            }

        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('c.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);

            if ($customer) {
                //check access and ownership of the model
                $this->checkAccessOwnership($customer);
            }
        }

        if (!empty($customer)) {

            if (!$this->actionCompleted) {
                // get additional variables
                $customer->getVarsForTemplate(false);
                $mode = ($request->isPost()) ? 'request' : 'database';
                $customer->prepareBBVarsForTemplate($mode);
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('customer')) {
                $this->registry->set('customer', $customer->sanitize());
            }
        } else {
            //show error no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * multiedit models
     */
    private function _multiEdit($ids = '') {
        $request = &$this->registry['request'];

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
            $filter_ids = implode(',', $ids);
        } else {
            $filter_ids = '';
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $filters = array('where' => array('c.id IN (' . $filter_ids . ')'),
                         'model_lang' => $request->get('model_lang'),
                         'sanitize' => false);
        $customers = Customers::search($this->registry, $filters);

        //if no customers or checked deleted customers
        if (empty($customers) || count($ids) != count($customers)) {
            $this->registry['messages']->setError($this->i18n('error_no_customers_or_deleted'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, 'list');
        }

        $first_customer = $customers[0];
        $is_company = ($first_customer->get('is_company'));
        $type = $first_customer->get('type');
        foreach ($customers as $customer) {
            $type_i = $customer->get('type');
            $is_company_i = $customer->get('is_company');
            //different type or companies and persons
            if ($is_company != $is_company_i || $type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_different_types_or_companies'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'list');
                return true;
            }
        }

        $this->registry->set('type_name', $first_customer->get('type_name'));

        $custype = '';

        require_once $this->modelsDir . 'customers.types.factory.php';
        if (!empty($type)) {
            $filters = array('where' => array('ct.id = ' . $type),
                             'sanitize' => true);
            $custype = Customers_Types::searchOne($this->registry, $filters);
        }
        $this->registry['custype'] = $custype;

        //get main variables
        $vars = $this->getBasicVarsDefs($is_company, $customer);

        //add additional variables
        $multivars = $customer->getFieldsMulti($this->action);
        $vars = $this->mergeVars($customer, $vars, $multivars);

        if (count($vars) == 0) {
            //show error no variables
            $this->registry['messages']->setError($this->i18n('error_customers_no_multi_operation_variables'));
            $this->registry['messages']->insertInSession($this->registry);

            //redirect to the listing
            $this->redirect($this->module, 'list');
        }

        //check if details are submitted via checkboxes (items)
        if (!$request->get('multisave')) {
            // the models from the DB
            foreach ($ids as $i => $id) {
                $records = $customers[$i]->getFieldsMulti($this->action);
                $add_vars = array();
                foreach ($records as $var) {
                    $add_vars[PH_ADDITIONAL_VAR_PREFIX . $var['name']] = $var['value'];
                }
                //get values
                foreach ($vars as $k => $var) {
                    if (strpos($var['name'], PH_ADDITIONAL_VAR_PREFIX) === 0) {
                        if ($var['type'] == 'autocompleter') {
                            $vars[$k]['value'] = $add_vars[$var['name']];
                            // linkification
                            if (!empty($vars[$k]['autocomplete']) && !empty($vars[$k]['autocomplete']['id_var']) &&
                            !empty($vars[$k]['autocomplete']['view_mode']) && $vars[$k]['autocomplete']['view_mode'] == 'link') {
                                $vars[$k]['value_id'] =
                                    isset($add_vars[$vars[$k]['autocomplete']['id_var']]) ?
                                    $add_vars[$vars[$k]['autocomplete']['id_var']] :
                                    $customers[$i]->getVarValue(preg_replace('#^' . preg_quote(PH_ADDITIONAL_VAR_PREFIX) . '#', '', $vars[$k]['autocomplete']['id_var']));
                            }
                        } else {
                            $vars[$k]['val'] = $add_vars[$var['name']];
                        }
                        $vars[$k]['model_id'] = $id;
                    } elseif (in_array($var['name'], $customers[$i]->contactParameters)) {
                        $contacts = array();
                        $notes = $customers[$i]->get($var['name'] . '_note');
                        if ($customers[$i]->get($var['name'])) {
                            foreach ($customers[$i]->get($var['name']) as $idx => $value) {
                                $contacts[] = sprintf('%s%s', $value, (!empty($notes[$idx])) ? '|'. $notes[$idx] : '');
                            }
                        }
                        $vars[$k]['val'] = implode(', ', $contacts);
                    } else {
                        $vars[$k]['val'] = $customers[$i]->get($var['name']);
                    }
                }
                $customers[$i]->set('multivars', $vars, true);
                $customers[$i]->sanitize();
            }
            $this->registry->set('customers', $customers);
        } else {

            //save models
            foreach ($vars as $var) {
                if (!$this->registry['request']->isRequested($var['name'])) {
                    $this->registry['request']->set($var['name'], array(), 'post', true);
                }
            }

            $type_name_plural = !empty($custype) && $custype->get('name_plural') ? $custype->get('name_plural') : $this->i18n('customers');

            //save models
            $this->actionCompleted = Customers::multiEdit($this->registry, $vars, $type, $is_company, $this);
            if ($this->actionCompleted) {
                $this->registry['messages']->setMessage($this->i18n('message_customers_multiedit_success', array($type_name_plural)), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $this->registry['messages']->setError($this->i18n('error_customers_multiedit_failed', array($type_name_plural)), '', -1);
            }
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            // get the complete model from db (in the new model_lang)
            $filters = array('where' => array('c.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
            $customer->getVars();

            $old_customer = clone $customer;
            $this->old_model = $old_customer->sanitize();

            // set data from request into model
            foreach ($request->getAll() AS $k => $v) {
                $customer->set($k, $v, true);
            }

            if ($customer->save()) {
                $filters = array('where' => array('c.id = ' . $id),
                                 'model_lang' => $request->get('model_lang'));
                $new_customer = Customers::searchOne($this->registry, $filters);
                $new_customer->getVars();

                Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'translate', 'new_model' => $new_customer, 'old_model' => $old_customer));

                // if updating emails targetlists is enabled
                if ($this->registry['config']->getParam('customers', 'update_emails_targetlists')) {
                    Customers::updateEmailsTargetlists($this->registry, $new_customer, $old_customer);
                }

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_customers_translate_success', array($old_customer->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_customers_translate_failed', array($old_customer->getModelTypeName())), '', -2);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('c.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);

            if ($customer) {
                //check access and ownership of the model
                $this->checkAccessOwnership($customer);
            }
        }

        if (!empty($customer)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('customer', $customer->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        // make sure action in registry is 'view'
        $this->setAction('view');

        $filters = array('where' => array('c.id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $customer = Customers::searchOne($this->registry, $filters);

        if (!empty($customer)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            $customer->getVarsForTemplate(false);
            $customer->prepareBBVarsForTemplate();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('customer')) {
                $this->registry->set('customer', $customer->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Activates or deactivates the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Customers::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);

            foreach ($ids as $id) {
                $customer = new Customer($this->registry);
                $customer->set('id', $id, true);
                Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => $this->action));
            }

            // if updating emails targetlists is enabled
            if ($this->action == 'deactivate' && !empty($ids) &&
            $this->registry['config']->getParam('customers', 'update_emails_targetlists')) {
                Customers::deleteEmailsFromTargetlists($this->registry, $ids, 'Customer');
            }
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    private function _delete() : void
    {
        /** @var Request $request */
        $request = $this->registry['request'];
        $id = $request->getGet($this->action);
        //ids of the models to be deleted
        if ($id) {
            $ids = [$id];
        } else {
            $ids = $request->get('items');
        }

        /** @var Messages $msg */
        $msg = $this->registry['messages'];

        if (empty($ids)) {
            $msg->setError($this->i18n('error_delete_notallowed'));
            $this->MultiActionFinnish();
            return;
        }

        if (!$this->modelFactoryName::checkPermissions($this->registry, $ids, $this->action)) {
            $msg->setError($this->i18n('error_no_access_to_models'));
            $this->MultiActionFinnish();
            if ($request->isAcceptHtml()) {
                $this->redirect($this->module, $this->defaultAction);
            }
            return;
        }

        $result = Customers::delete($this->registry, $ids);

        if (!$result) {
            $msg->setError($this->i18n('error_items_not_deleted'), '', -1);
            $this->MultiActionFinnish();
            return;
        }

        foreach ($ids as $id) {
            $customer = new Customer($this->registry);
            $customer->set('id', $id, true);
            Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'delete'));
        }

        // if updating emails targetlists is enabled
        if (!empty($ids) && $this->registry['config']->getParam('customers', 'update_emails_targetlists')) {
            Customers::deleteEmailsFromTargetlists($this->registry, $ids, 'Customer');
        }

        $msg->setMessage($this->i18n('message_items_deleted'));
        $this->MultiActionFinnish();
        return;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Customers::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage($this->i18n('message_items_restored'));

            foreach ($ids as $id) {
                $customer = new Customer($this->registry);
                $customer->set('id', $id, true);
                Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'restore'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError($this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Customers::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage($this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError($this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * filter for references
     */
    private function _filter() {

        //checks if the filter is called for relating documents
        if ($this->registry['request']->get('relation')) {
            $this->registry->set('relation', $this->registry['request']->get('relation'), true);
        }
        if ($this->registry['request']->get('event')) {
            $this->registry->set('event', $this->registry['request']->get('event'), true);
        }
        if ($this->registry['request']->get('mynzoom_settings_table')) {
            $this->registry->set('mynzoom_settings_table', $this->registry['request']->get('mynzoom_settings_table'), true);
        }

        $this->viewer = $this->getViewer();

        $autocomplete_filter = $this->registry['request']->get('autocomplete_filter');
        if ($autocomplete_filter) {
            if ($autocomplete_filter != 'session') {
                $filters = $this->_select();
                $this->viewer->data['autocomplete_filters'] = $filters;
            }
        }
        $this->viewer->setFrameset('frameset_pop.html');

        return true;
    }

    /**
     * AJAX FILTER for add customers to event
     *
     * @deprecated - used for old assignments of events - with filter pop-up
     */
    /*private function _filterCustomers() {
        $request = $this->registry['request'];
        if ($request->isPost() || $request->isRequested('items')) {
            $ids = $request->get('items');

            //$fields = preg_split('#\s*,\s*#', $request->get('fields'));
            //$fields = array('id', 'name');
            $filters = array('where' => array('c.id IN (' . implode(', ', $ids) . ')',
                    "((c.subtype='normal' AND c.is_company=0) OR c.subtype='contact')"),
                             'model_lang' => $request->get('model_lang'));

            $customers = Customers::search($this->registry, $filters);

            $filter_customers = array();
            foreach ($customers as $customer) {
                $filter_customers[$customer->get('id')]['participant_id'] = $customer->get('id');
                $filter_customers[$customer->get('id')]['customer_name'] = $customer->get('name') . ' ' . $customer->get('lastname');
            }
        }

        if (!empty($filter_customers)) {
            echo json_encode($filter_customers);
        }
        exit;
    }*/

    /**
     * Fills dropdown options for assigning to department
     */
    private function _fillUsersDepartmentOptions() {
        $result = Customers::fillUsersDropdown($this->registry);

        print $result;
        return true;
    }

    /**
     * get contact persons for current branch
     */
    private function _fillContactPersonsOptions() {
        $request = &$this->registry['request'];

        $branch_id = $request->get('branch_id');
        $records = array();
        if ($branch_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters  = array('sanitize' => true,
                              'model_lang' => $request->get('model_lang'),
                              'where' => array ('c.parent_customer = ' . $branch_id,
                                                'c.subtype = \'contact\'',
                                                'c.active = 1'),
                              'sort' => array('c.is_main DESC', 'CONCAT_WS(\' \', ci18n.name, ci18n.lastname) ASC', 'c.id DESC'));
            $contact_persons = Customers_Contactpersons::search($this->registry, $filters);
            foreach ($contact_persons as $key => $contact_person) {
                $records[$key]['option_value'] = $contact_person->get('id');
                $records[$key]['label'] = ($contact_person->get('name') ? $contact_person->get('name') : '') . ($contact_person->get('lastname') ? (' ' . $contact_person->get('lastname')) : '');
            }
        }

        print json_encode($records);
    }

    /**
     * prepare information for updating customer's info panel
     */
    private function _updateCustomersInfoPanel() {
        $request = &$this->registry['request'];

        $branch_id = $request->get('branch_id');
        $records = array();
        if ($branch_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters  = array('sanitize' => true,
                              'model_lang' => $request->get('model_lang'),
                              'where' => array ('c.id = ' . $branch_id,
                                                'c.subtype = \'branch\'',
                                                'c.active = 1'));
            $branch = Customers_Branches::searchOne($this->registry, $filters);
            if ($branch) {
                // gets branch name
                $records['branch_name'] = $branch->get('name');
                $records['branch_address'] = $branch->get('address');

                // get main contact person's name
                require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                $filters  = array('sanitize' => true,
                                  'model_lang' => $request->get('model_lang'),
                                  'where' => array('c.parent_customer = ' . $branch_id,
                                                   'c.subtype = \'contact\'',
                                                   'c.active = 1',
                                                   'c.is_main = 1'));
                $contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);
                if ($contact_person) {
                    $records['contact_person_name'] = $contact_person->get('name') . ($contact_person->get('lastname') ? (' ' . $contact_person->get('lastname')) : '');
                } else {
                    $records['contact_person_name'] = '';
                }

                //prepare viewer
                $customerContactViewer = new Viewer($this->registry);
                $customerContactViewer->data['customers_info'] = $branch;
                $customerContactViewer->setFrameset('_customers_contacts_data.html');
                $records['contacts_template'] = $customerContactViewer->fetch();
            }
        }

        print json_encode($records);
    }

    /**
     * AJAX FUNCTION for adding customers or users as default assigned to a event type
     *
     * @deprecated - used for old default assignments to events in My nZoom
     */
    /* private function addDefaultAssignedUsers() {
        $request = $this->registry['request'];

        $assignments = array();

        if ($request->isPost() || $request->isRequested('items')) {
            $ids = $request->get('items');

            $filters = array('where'        => array('c.id IN (' . implode(', ', $ids) . ')',
                                                     "((c.subtype='normal' AND c.is_company=0) OR c.subtype='contact')"),
                             'model_lang'   => $request->get('model_lang')
            );

            $customers = Customers::search($this->registry, $filters);

            foreach ($customers as $customer) {
                $assignments[] = array(
                    'id'            => $customer->get('id'),
                    'name'          => $customer->get('name') . ' ' . $customer->get('lastname'),
                    'assignee_type' => 'customer'
                );
            }
        }

        echo json_encode($assignments);
        exit;
    } */

    /**
     * Redirect to specified module at ADD section and auto complete customer data
     */
    private function _create() {
        $request = &$this->registry['request'];

        $module = $request->get('operation');
        switch ($module) {
            case 'documents':
                $add_link = sprintf('operation=add&type=%d&customer=%d&customer_create=1',
                                    $request->get('document_type'), $request->get('create_from_customer_id'));
                $this->redirect($module, 'add', $add_link);
                break;
            case 'tasks':
                $add_link = sprintf('type=%d&customer=%d&customer_create=1',
                                    $request->get('task_type'), $request->get('create_from_customer_id'));
                $this->redirect($module, 'add', $add_link);
                break;
            case 'events':
                $add_link = sprintf('type=%d&customer=%d&customer_create=1',
                                    $request->get('event_type'), $request->get('create_from_customer_id'));
                $this->redirect($module, 'add', $add_link);
                break;
            case 'projects':
                $add_link = sprintf('type=%d&customer=%d&customer_create=1',
                                    $request->get('project_type'), $request->get('create_from_customer_id'));
                $this->redirect($module, 'add', $add_link);
                break;
            case 'minitasks':
                $add_link = sprintf('communications=%d&communication_type=minitasks&operation=add#communications_container',
                                    $request->get('create_from_customer_id'));
                $this->redirect($this->module, 'communications', $add_link);
                break;
        }
        return true;
    }

    /**
     * Attaches files to the customer
     */
    private function _attachments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters['where'] = array('c.id = ' . $id);
        $filters['model_lang'] = $request->get('model_lang');
        $customer = Customers::searchOne($this->registry, $filters);

        $added_files = array(0 => array());

        if ($request->isPost()) {

            $old_customer = clone $customer;
            $old_customer->getAttachments();
            $this->old_model = $old_customer;

            $modified_files = array();
            $modified_genfiles = array();

            $erred_modified_files = array();
            $erred_modified_genfiles = array();
            $erred_added_files = array();
            $success_added_files = array();

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';

            //edit existing attachments
            $names        = $request->get('file_names');
            $descriptions = $request->get('file_descriptions');
            $permissions  = $request->get('file_permissions');
            $revisions    = $request->get('file_revisions');
            $filenames    = $request->get('file_filenames');
            $files        = !empty($_FILES['file_paths']) ? $_FILES['file_paths'] : array();
            $indices      = $request->get('file_indices');

            if (!empty($names)) {
                foreach ($files['name'] as $idx => $name) {
                    $index = $indices[$idx];

                    if ($files['tmp_name'][$idx]) {
                        $file = array(
                            'name'     => $files['name'][$idx],
                            'type'     => $files['type'][$idx],
                            'tmp_name' => $files['tmp_name'][$idx],
                            'error'    => $files['error'][$idx],
                            'size'     => $files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($names[$idx])) {
                        $names[$idx] = $files['name'][$idx];
                    }
                    $params = array(
                        'id'          => $idx,
                        'name'        => $names[$idx],
                        'filename'    => $filenames[$idx],
                        'description' => $descriptions[$idx],
                        'revision'    => $revisions[$idx],
                        'permission'  => $permissions[$idx]);

                    if (empty($params['name']) && !empty($file)) {
                        $params['name'] = $file['name'];
                    }

                    if (!Files::attachFile($this->registry, $file, $params, $customer->sanitize())) {
                        $erred_modified_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_edit') . ' ' . $index, 'edit_attachment_' . $idx);

                        //explain the failed upload with more details
                        foreach (FilesLib::$_errors as $err) {
                            $this->registry['messages']->setError($err);
                        }
                        FilesLib::$_errors = array();
                    }

                    $modified_files[$idx] = $params;
                }
            }

            //edit existing generated files
            $generated_names        = $request->get('g_file_names');
            $generated_descriptions = $request->get('g_file_descriptions');
            $generated_permissions  = $request->get('g_file_permissions');
            $generated_revisions    = $request->get('g_file_revisions');
            $generated_indices      = $request->get('g_file_indices');

            if (!empty($generated_names)) {
                foreach ($generated_names as $idx => $name) {
                    $index = $generated_indices[$idx];

                    $file = array();
                    $params = array(
                        'id'          => $idx,
                        'name'        => $generated_names[$idx],
                        'description' => $generated_descriptions[$idx],
                        'permission'  => $generated_permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $customer->sanitize())) {
                        $erred_modified_genfiles[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_gen_edit') . ' ' . $index, 'edit_gen_attachment_' . $idx);

                        FilesLib::$_errors = array();
                    }
                    $customer->unsanitize();
                    $modified_genfiles[$idx] = $params;
                }
            }

            //add new attachments
            $additional_names        = $request->get('a_file_names');
            $additional_descriptions = $request->get('a_file_descriptions');
            $additional_permissions  = $request->get('a_file_permissions');
            $additional_revisions    = $request->get('a_file_revisions');
            $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

            if (!empty($additional_files)) {
                foreach ($additional_files['name'] as $idx => $name) {
                    if ($additional_files['tmp_name'][$idx]) {
                        $file = array(
                            'name'     => $additional_files['name'][$idx],
                            'type'     => $additional_files['type'][$idx],
                            'tmp_name' => $additional_files['tmp_name'][$idx],
                            'error'    => $additional_files['error'][$idx],
                            'size'     => $additional_files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($additional_names[$idx])) {
                        $additional_names[$idx] = $additional_files['name'][$idx];
                    }
                    $params = array(
                        'name'        => $additional_names[$idx] ?? '',
                        'description' => $additional_descriptions[$idx] ?? '',
                        'revision'    => $additional_revisions[$idx] ?? '',
                        'permission'  => $additional_permissions[$idx] ?? 'all');

                    if (!empty($file) || $params['name']) {
                        if (!Files::attachFile($this->registry, $file, $params, $customer->sanitize())) {
                            $error_type = '';
                            if (empty($file)) {
                                $error_type = $error_type . $this->i18n('error_attachments_file');
                            }
                            if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                            if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                            $erred_added_files[] = $idx;
                            $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));

                            //explain the failed upload with more details
                            foreach (FilesLib::$_errors as $err) {
                                $this->registry['messages']->setError($err);
                            }
                            FilesLib::$_errors = array();
                        } else {
                            $success_added_files[] = $idx;
                        }
                    }

                    $added_files[$idx] = $params;
                }
            }


            if ($modified_files && empty($erred_modified_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'modified_attachments'));
            } elseif (!empty($modified_files)) {
                $this->registry['modified_files'] = $modified_files;
                $this->registry['erred_modified_files'] = $erred_modified_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($modified_genfiles && empty($erred_modified_genfiles)) {
                Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'modified_gen'));

                $this->registry['messages']->setMessage($this->i18n('message_attachments_gen_modified'));
                $this->registry['messages']->insertInSession($this->registry);
            } elseif (!empty($modified_genfiles)) {
                $this->registry['modified_genfiles'] = $modified_genfiles;
                $this->registry['erred_modified_genfiles'] = $erred_modified_genfiles;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($added_files && empty($erred_added_files) && !empty($success_added_files)) {
                $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                 'model_lang' => $customer->get('model_lang'));
                $customer_attached_files = Customers::searchOne($this->registry, $filters);
                $customer_attached_files->getAttachments();
                $customer_attached_files->sanitize();

                Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'add_attachments', 'new_model' => $customer_attached_files, 'old_model' => $old_customer));

                $this->registry['messages']->setMessage($this->i18n('message_attachments_added'));
                $this->registry['messages']->insertInSession($this->registry);
            } elseif ($added_files && !empty($erred_added_files)) {
                $this->registry['erred_added_files'] = $erred_added_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if (empty($erred_added_files) && empty($erred_modified_files) && empty($erred_modified_genfiles)) {
                $this->actionCompleted = true;
            }

        }

        $this->registry['added_files'] = $added_files;

        if (!empty($customer)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            $customer->getAttachments();
            $customer->getGeneratedFiles();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('customer')) {
                $this->registry->set('customer',  $customer->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Fetches generated customer file
     */
    private function _manageFile() {
        $request = &$this->registry['request'];

        //check if the 'generate' action is allowed
        $this->checkAccessModule(true, $this->module, 'attachments');

        //get the requested model ID
        $id = $request->get($this->action);
        // make a guess whether id is a model id or a temporary id for a model
        // that is not added yet
        if (General::guessIfRealModelId($this->registry, $id)) {
            $filters = array('where' => array('c.id = \'' . $id . '\''),
                             'model_lang' => $request->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
        } else {
            $customer = Customers::buildModel($this->registry);
            $customer->set('id', $id, true);
            $customer->set('added_by', $this->registry['currentUser']->get('id'), true);
        }

        if (!empty($customer)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            if (is_numeric($request->get('file'))) {
                $file_id = $request->get('file');
            } else {
                $file_id = General::decrypt($request->get('file'), '_' . $this->action . '_', 'xtea');
            }

            $filters['where'] = array('f.id = ' . $file_id);
            $filters['sanitize'] = true;
            $file = Files::searchOne($this->registry, $filters);

            if ($file && file_exists($file->get('path'))) {
                switch ($this->action) {
                case 'getfile':
                    $result = $file->sendFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $customer->get('id'));
                    }
                    break;
                case 'viewfile':
                    $result = $file->viewFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $customer->get('id'));
                    }
                    break;
                case 'delfile':
                    // get the files info needed for the audit
                    $old_customer = clone $customer;
                    $old_customer->getAttachments();

                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                         'model_lang' => $customer->get('model_lang'));
                        $customer_del_files = Customers::searchOne($this->registry, $filters);
                        $customer_del_files->getAttachments();
                        $customer_del_files->sanitize();

                        Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'del_attachments', 'new_model' => $customer_del_files, 'old_model' => $old_customer));

                        $this->registry['messages']->setMessage($this->i18n('message_customers_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_customers_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $customer->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $customer->get('id'));
                }
            } elseif ($file && $this->action == 'delfile') {
                switch ($this->action) {
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_customers_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_customers_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $customer->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $customer->get('id'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'attachments', 'attachments=' . $customer->get('id'));
            }

        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Get main variables for multi add/edit - default or from config
     *
     * @param bool $is_company - defines whether the companies or persons should be processed
     * @param Customer $model - the first model in the list
     * @return array - data for basic vars
     */
    private function getBasicVarsDefs($is_company, $model = null) {
        $type = $this->registry['custype'];

        //get the fields (columns) for the multi action
        $this->multiFields = array();
        if ($this->registry['config']->getParam('customers', $this->action . '_' . $type->get('id'))) {
            //get settings for this type
            $this->multiFields = explode(', ', $this->registry['config']->getParam('customers', $this->action . '_' . $type->get('id')));
        } elseif ($this->registry['config']->getParam('customers', $this->action)) {
            //get settings for all customers
            $this->multiFields = explode(', ', $this->registry['config']->getParam('customers', $this->action));
        } else {
            //hard-coded options
            $this->multiFields = array('name', 'lastname');
        }

        $layouts = array();
        if ($model) {
            $layouts = $model->getLayoutsDetails();
        }

        // required basic variables per type
        $required_fields = array_filter(
            $this->registry['config']->getParamAsArray($this->module, 'validate_' . $type->get('id')),
            function($a) { return $a != 'current_year' && strpos($a, 'unique_') !== 0; }
        );

        //prepare name
        if (in_array('name', $this->multiFields) || $this->action == 'multiadd') {
            $vars['name'] = array(
                'name' => 'name',
                'type' => 'text',
                'required' => 1,
                'label' => $this->i18n('customers_name')
            );

            if (!empty($layouts['name'])) {
                //get the label from the layout name
                $vars['name']['label'] = $layouts['name']['name'];
                if (!$layouts['name']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['name']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['name']['readonly'] = !$layouts['name']['edit'];
                }
            }
        }

        //prepare lastname
        if ((in_array('lastname', $this->multiFields) || $this->action == 'multiadd') && ! $is_company) {
            $vars['lastname'] = array(
                'name' => 'lastname',
                'type' => 'text',
                'required' => 1,
                'label' => $this->i18n('customers_lastname')
            );

            /*if (!empty($layouts['lastname'])) {
                //get the label from the layout name
                $vars['lastname']['label'] = $layouts['lastname']['name'];
                if (!$layouts['lastname']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['lastname']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['lastname']['readonly'] = !$layouts['lastname']['edit'];
                }
            }*/
        }

        //prepare code
        if (in_array('code', $this->multiFields)) {
            $vars['code'] = array(
                'name' => 'code',
                'type' => 'text',
                'label' => $this->i18n('customers_code')
            );

            if (!empty($layouts['code'])) {
                //get the label from the layout name
                $vars['code']['label'] = $layouts['code']['name'];
                if (!$layouts['code']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['code']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['code']['readonly'] = !$layouts['code']['edit'];
                }
            }
        }

        //prepare company_department
        if (in_array('company_department', $this->multiFields) && !$is_company) {
            $vars['company_department'] = array(
                'name' => 'company_department',
                'type' => 'text',
                'required' => in_array('company_department', $required_fields),
                'label' => $this->i18n('customers_company_department')
            );

            if (!empty($layouts['company_department'])) {
                //get the label from the layout name
                $vars['company_department']['label'] = $layouts['company_department']['name'];
                if (!$layouts['company_department']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['company_department']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['company_department']['readonly'] = !$layouts['company_department']['edit'];
                }
            }
        }

        //prepare position
        if (in_array('position', $this->multiFields) && !$is_company) {
            $vars['position'] = array(
                'name' => 'position',
                'type' => 'text',
                'required' => in_array('position', $required_fields),
                'label' => $this->i18n('customers_position')
            );

            if (!empty($layouts['position'])) {
                //get the label from the layout name
                $vars['position']['label'] = $layouts['position']['name'];
                if (!$layouts['position']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['position']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['position']['readonly'] = !$layouts['position']['edit'];
                }
            }
        }

        //prepare country
        if (in_array('country', $this->multiFields)) {
            $vars['country'] = array(
                'name' => 'country',
                'type' => 'dropdown',
                'required' => in_array('country', $required_fields),
                'label' => $this->i18n('customers_country'),
                'options'   => Dropdown::getCountries(array($this->registry)),
                'width' => '200'
            );

            if (!empty($layouts['country'])) {
                //get the label from the layout name
                $vars['country']['label'] = $layouts['country']['name'];
                if (!$layouts['country']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['country']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['country']['readonly'] = !$layouts['country']['edit'];
                }
            }
        }

        //prepare city
        if (in_array('city', $this->multiFields)) {
            $vars['city'] = array(
                'name' => 'city',
                'type' => 'text',
                'required' => in_array('city', $required_fields),
                'label' => $this->i18n('customers_city')
            );

            if (!empty($layouts['city'])) {
                //get the label from the layout name
                $vars['city']['label'] = $layouts['city']['name'];
                if (!$layouts['city']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['city']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['city']['readonly'] = !$layouts['city']['edit'];
                }
            }
        }

        //prepare postal_code
        if (in_array('postal_code', $this->multiFields)) {
            $vars['postal_code'] = array(
                'name' => 'postal_code',
                'type' => 'text',
                'required' => in_array('postal_code', $required_fields),
                'label' => $this->i18n('customers_postal_code')
            );

            if (!empty($layouts['postal_code'])) {
                //get the label from the layout name
                $vars['postal_code']['label'] = $layouts['postal_code']['name'];
                if (!$layouts['postal_code']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['postal_code']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['postal_code']['readonly'] = !$layouts['postal_code']['edit'];
                }
            }
        }

        //prepare address
        if (in_array('address', $this->multiFields)) {
            $vars['address'] = array(
                'name' => 'address',
                'type' => 'textarea',
                'required' => in_array('address', $required_fields),
                'label' => $this->i18n('customers_address'),
                'help' => $this->i18n('customers_address'));

            if (!empty($layouts['address'])) {
                //get the label from the layout name
                $vars['address']['label'] = $layouts['address']['name'];
                if (!$layouts['address']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['address']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['address']['readonly'] = !$layouts['address']['edit'];
                }
            }
        }

        //prepare notes
        if (in_array('notes', $this->multiFields)) {
            $vars['notes'] = array(
                'name' => 'notes',
                'type' => 'textarea',
                'required' => in_array('notes', $required_fields),
                'label' => $this->i18n('customers_notes'),
                'help' => $this->i18n('customers_notes'));

            if (!empty($layouts['notes'])) {
                //get the label from the layout name
                $vars['notes']['label'] = $layouts['notes']['name'];
                if (!$layouts['notes']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['notes']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['notes']['readonly'] = !$layouts['notes']['edit'];
                }
            }
        }

        //prepare phone
        if (in_array('phone', $this->multiFields)) {
            $vars['phone'] = array(
                'name' => 'phone',
                'type' => 'text',
                'required' => in_array('phone', $required_fields),
                'label' => $this->i18n('customers_phone')
            );

            if (!empty($layouts['contacts'])) {
                if (!$layouts['contacts']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['phone']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['phone']['readonly'] = !$layouts['contacts']['edit'];
                }
            }
        }

        //prepare fax
        if (in_array('fax', $this->multiFields)) {
            $vars['fax'] = array(
                'name' => 'fax',
                'type' => 'text',
                'required' => in_array('fax', $required_fields),
                'label' => $this->i18n('customers_fax')
            );

            if (!empty($layouts['contacts'])) {
                if (!$layouts['contacts']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['fax']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['fax']['readonly'] = !$layouts['contacts']['edit'];
                }
            }
        }

        //prepare gsm
        if (in_array('gsm', $this->multiFields)) {
            $vars['gsm'] = array(
                'name' => 'gsm',
                'type' => 'text',
                'required' => in_array('gsm', $required_fields),
                'label' => $this->i18n('customers_gsm')
            );

            if (!empty($layouts['contacts'])) {
                if (!$layouts['contacts']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['gsm']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['gsm']['readonly'] = !$layouts['contacts']['edit'];
                }
            }
        }

        //prepare email
        if (in_array('email', $this->multiFields)) {
            $vars['email'] = array(
                'name' => 'email',
                'type' => 'text',
                'required' => in_array('email', $required_fields),
                'label' => $this->i18n('customers_email')
            );

            if (!empty($layouts['contacts'])) {
                if (!$layouts['contacts']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['email']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['email']['readonly'] = !$layouts['contacts']['edit'];
                }
            }
        }

        //prepare web
        if (in_array('web', $this->multiFields)) {
            $vars['web'] = array(
                'name' => 'web',
                'type' => 'text',
                'required' => in_array('web', $required_fields),
                'label' => $this->i18n('customers_web')
            );

            if (!empty($layouts['contacts'])) {
                if (!$layouts['contacts']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['web']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['web']['readonly'] = !$layouts['contacts']['edit'];
                }
            }
        }

        //prepare skype
        if (in_array('skype', $this->multiFields)) {
            $vars['skype'] = array(
                'name' => 'skype',
                'type' => 'text',
                'required' => in_array('skype', $required_fields),
                'label' => $this->i18n('customers_skype')
            );

            if (!empty($layouts['contacts'])) {
                if (!$layouts['contacts']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['skype']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['skype']['readonly'] = !$layouts['contacts']['edit'];
                }
            }
        }

        //prepare othercontact
        if (in_array('othercontact', $this->multiFields)) {
            $vars['othercontact'] = array(
                'name' => 'othercontact',
                'type' => 'text',
                'required' => in_array('othercontact', $required_fields),
                'label' => $this->i18n('customers_othercontact')
            );

            if (!empty($layouts['contacts'])) {
                if (!$layouts['contacts']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['othercontact']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['othercontact']['readonly'] = !$layouts['contacts']['edit'];
                }
            }
        }

        //prepare company_name
        if (in_array('company_name', $this->multiFields) && $is_company) {
            $vars['company_name'] = array(
                'name' => 'company_name',
                'type' => 'text',
                'required' => in_array('company_name', $required_fields),
                'label' => $this->i18n('customers_company_name')
            );

            if (!empty($layouts['company_name'])) {
                //get the label from the layout name
                $vars['company_name']['label'] = $layouts['company_name']['name'];
                if (!$layouts['company_name']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['company_name']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['company_name']['readonly'] = !$layouts['company_name']['edit'];
                }
            }
        }

        //prepare in_dds
        if (in_array('in_dds', $this->multiFields)) {
            $vars['in_dds'] = array(
                'name' => 'in_dds',
                'type' => 'text',
                'required' => in_array('in_dds', $required_fields),
                'label' => $this->i18n('customers_in_dds')
            );

            if (!empty($layouts['in_dds'])) {
                //get the label from the layout name
                $vars['in_dds']['label'] = $layouts['in_dds']['name'];
                if (!$layouts['in_dds']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['in_dds']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['in_dds']['readonly'] = !$layouts['in_dds']['edit'];
                }
            }
        }

        //prepare eik
        if (in_array('eik', $this->multiFields) && $is_company) {
            $vars['eik'] = array(
                'name' => 'eik',
                'type' => 'text',
                'required' => in_array('eik', $required_fields),
                'label' => $this->i18n('customers_eik')
            );

            if (!empty($layouts['eik'])) {
                //get the label from the layout name
                $vars['eik']['label'] = $layouts['eik']['name'];
                if (!$layouts['eik']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['eik']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['eik']['readonly'] = !$layouts['eik']['edit'];
                }
            }
        }

        //prepare registration_file
        if (in_array('registration_file', $this->multiFields) && $is_company) {
            $vars['registration_file'] = array(
                'name' => 'registration_file',
                'type' => 'text',
                'required' => in_array('registration_file', $required_fields),
                'label' => $this->i18n('customers_registration_file')
            );

            if (!empty($layouts['registration_file'])) {
                //get the label from the layout name
                $vars['registration_file']['label'] = $layouts['registration_file']['name'];
                if (!$layouts['registration_file']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['registration_file']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['registration_file']['readonly'] = !$layouts['registration_file']['edit'];
                }
            }
        }

        //prepare iban
        if (in_array('iban', $this->multiFields)) {
            $vars['iban'] = array(
                'name' => 'iban',
                'type' => 'text',
                'required' => in_array('iban', $required_fields),
                'label' => $this->i18n('customers_iban')
            );

            if (!empty($layouts['iban'])) {
                //get the label from the layout name
                $vars['iban']['label'] = $layouts['iban']['name'];
                if (!$layouts['iban']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['iban']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['iban']['readonly'] = !$layouts['iban']['edit'];
                }
            }
        }

        //prepare bic
        if (in_array('bic', $this->multiFields)) {
            $vars['bic'] = array(
                'name' => 'bic',
                'type' => 'text',
                'required' => in_array('bic', $required_fields),
                'label' => $this->i18n('customers_bic')
            );

            if (!empty($layouts['bic'])) {
                //get the label from the layout name
                $vars['bic']['label'] = $layouts['bic']['name'];
                if (!$layouts['bic']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['bic']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['bic']['readonly'] = !$layouts['bic']['edit'];
                }
            }
        }

        //prepare registration_volume
        if (in_array('registration_volume', $this->multiFields) && $is_company) {
            $vars['registration_volume'] = array(
                'name' => 'registration_volume',
                'type' => 'text',
                'required' => in_array('registration_volume', $required_fields),
                'label' => $this->i18n('customers_registration_volume')
            );

            if (!empty($layouts['registration_volume'])) {
                //get the label from the layout name
                $vars['registration_volume']['label'] = $layouts['registration_volume']['name'];
                if (!$layouts['registration_volume']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['registration_volume']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['registration_volume']['readonly'] = !$layouts['registration_volume']['edit'];
                }
            }
        }

        //prepare registration_number
        if (in_array('registration_number', $this->multiFields) && $is_company) {
            $vars['registration_number'] = array(
                'name' => 'registration_number',
                'type' => 'text',
                'required' => in_array('registration_number', $required_fields),
                'label' => $this->i18n('customers_registration_number')
            );

            if (!empty($layouts['registration_number'])) {
                //get the label from the layout name
                $vars['registration_number']['label'] = $layouts['registration_number']['name'];
                if (!$layouts['registration_number']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['registration_number']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['registration_number']['readonly'] = !$layouts['registration_number']['edit'];
                }
            }
        }

        //prepare registration_address
        if (in_array('registration_address', $this->multiFields) && $is_company) {
            $vars['registration_address'] = array(
                'name' => 'registration_address',
                'type' => 'textarea',
                'required' => in_array('registration_address', $required_fields),
                'label' => $this->i18n('customers_registration_address'),
                'help' => $this->i18n('customers_registration_address'));

            if (!empty($layouts['registration_address'])) {
                //get the label from the layout name
                $vars['registration_address']['label'] = $layouts['registration_address']['name'];
                if (!$layouts['registration_address']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['registration_address']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['registration_address']['readonly'] = !$layouts['registration_address']['edit'];
                }
            }
        }

        //prepare mol
        if (in_array('mol', $this->multiFields) && $is_company) {
            $vars['mol'] = array(
                'name' => 'mol',
                'type' => 'text',
                'required' => in_array('mol', $required_fields),
                'label' => $this->i18n('customers_mol')
            );

            if (!empty($layouts['mol'])) {
                //get the label from the layout name
                $vars['mol']['label'] = $layouts['mol']['name'];
                if (!$layouts['mol']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['mol']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['mol']['readonly'] = !$layouts['mol']['edit'];
                }
            }
        }

        //prepare ucn
        if (in_array('ucn', $this->multiFields) && !$is_company) {
            $vars['ucn'] = array(
                'name' => 'ucn',
                'type' => 'text',
                'required' => in_array('ucn', $required_fields),
                'label' => $this->i18n('customers_ucn')
            );

            if (!empty($layouts['ucn'])) {
                //get the label from the layout name
                $vars['ucn']['label'] = $layouts['ucn']['name'];
                if (!$layouts['ucn']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['ucn']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['ucn']['readonly'] = !$layouts['ucn']['edit'];
                }
            }
        }

        //prepare identity_num
        if (in_array('identity_num', $this->multiFields) && !$is_company) {
            $vars['identity_num'] = array(
                'name' => 'identity_num',
                'type' => 'text',
                'required' => in_array('identity_num', $required_fields),
                'label' => $this->i18n('customers_identity_num')
            );

            if (!empty($layouts['identity_num'])) {
                //get the label from the layout name
                $vars['identity_num']['label'] = $layouts['identity_num']['name'];
                if (!$layouts['identity_num']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['identity_num']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['identity_num']['readonly'] = !$layouts['identity_num']['edit'];
                }
            }
        }

        //prepare identity_date
        if (in_array('identity_date', $this->multiFields) && !$is_company) {
            $vars['identity_date'] = array(
                'name'          => 'identity_date',
                'type'          => 'date',
                'required' => in_array('identity_date', $required_fields),
                'standalone'    => true,
                'label'         => $this->i18n('customers_identity_date')
            );

            if (!empty($layouts['identity_date'])) {
                //get the label from the layout name
                $vars['identity_date']['label'] = $layouts['identity_date']['name'];
                if (!$layouts['identity_date']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['identity_date']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['identity_date']['readonly'] = !$layouts['identity_date']['edit'];
                }
            }
        }

        //prepare identity_by
        if (in_array('identity_by', $this->multiFields) && !$is_company) {
            $vars['identity_by'] = array(
                'name' => 'identity_by',
                'type' => 'text',
                'required' => in_array('identity_by', $required_fields),
                'label' => $this->i18n('customers_identity_by')
            );

            if (!empty($layouts['identity_by'])) {
                //get the label from the layout name
                $vars['identity_by']['label'] = $layouts['identity_by']['name'];
                if (!$layouts['identity_by']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['identity_by']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['identity_by']['readonly'] = !$layouts['identity_by']['edit'];
                }
            }
        }

        //prepare identity_valid
        if (in_array('identity_valid', $this->multiFields) && !$is_company) {
            $vars['identity_valid'] = array(
                'name'          => 'identity_valid',
                'type'          => 'date',
                'required' => in_array('identity_valid', $required_fields),
                'standalone'    => true,
                'label'         => $this->i18n('customers_identity_valid')
            );

            if (!empty($layouts['identity_valid'])) {
                //get the label from the layout name
                $vars['identity_valid']['label'] = $layouts['identity_valid']['name'];
                if (!$layouts['identity_valid']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['identity_valid']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['identity_valid']['readonly'] = !$layouts['identity_valid']['edit'];
                }
            }
        }

        //prepare address_by_personal_id
        if (in_array('address_by_personal_id', $this->multiFields) && !$is_company) {
            $vars['address_by_personal_id'] = array(
                'name' => 'address_by_personal_id',
                'type' => 'textarea',
                'required' => in_array('address_by_personal_id', $required_fields),
                'label' => $this->i18n('customers_address_by_personal_id'),
                'help' => $this->i18n('customers_address_by_personal_id'));

            if (!empty($layouts['address_by_personal_id'])) {
                //get the label from the layout name
                $vars['address_by_personal_id']['label'] = $layouts['address_by_personal_id']['name'];
                if (!$layouts['address_by_personal_id']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['address_by_personal_id']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['address_by_personal_id']['readonly'] = !$layouts['address_by_personal_id']['edit'];
                }
            }
        }

        //prepare active
        if (in_array('active', $this->multiFields)) {
            $vars['active'] = array(
                'name' => 'active',
                'type' => 'checkbox',
                'label' => '',
                'help' => $this->i18n('activated') . '/' . $this->i18n('deactivated'),
                'label' => $this->i18n('customers_status_active'),
                'option_value' => '1',
                'val' => '1'
            );
        }

        //prepare group
        if (in_array('group', $this->multiFields)) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters_groups = array('sanitize' => true);
            $groups = Groups::getTree($this->registry, $filters_groups);

            $_options_groups = array();
            foreach ($groups as $group) {
                $label = str_repeat('-', $group->get('level'));
                $_options_groups[] = array(
                    'label' => $label . $group->get('name'),
                    'option_value' => $group->get('id'));
            }

            $vars['group'] = array(
                'name'      => 'group',
                'type'      => 'dropdown',
                'label'     => $this->i18n('group'),
                'options'   => $_options_groups,
                'val'       => $type->getDefaultGroup()
            );
        }

        //prepare is_portal
        if (in_array('is_portal', $this->multiFields)) {
            $vars['is_portal'] = array(
                'name' => 'is_portal',
                'type' => 'checkbox',
                'label' => $this->i18n('is_portal'),
                'option_value' => '1',
                'val' => '0');
        }

        // the prepared sorted array
        $sorted_vars_options = array();

        foreach ($this->multiFields as $opt) {
            if (isset($vars[$opt])) {
                $sorted_vars_options[] = $vars[$opt];
                unset ($vars[$opt]);
                if ($opt == 'name' && isset($vars['lastname'])) {
                    $sorted_vars_options[] = $vars['lastname'];
                    unset ($vars['lastname']);
                }
            }
        }

        foreach ($vars as $var) {
            $sorted_vars_options[] = $var;
        }

        return $sorted_vars_options;
    }

    /**
     * Merge basic and additional variables
     *
     * @param object $customer - the first model in the list
     * @param array $vars - basic vars
     * @param array $multivars - additional vars
     * @return array - merged data for basic and additional vars
     */
    private function mergeVars($customer, $vars, $multivars) {
        foreach ($multivars as $multivar) {
            $multivar['name'] = PH_ADDITIONAL_VAR_PREFIX . $multivar['name'];

            if ($multivar['type'] == 'autocompleter') {
                if (isset($multivar['autocomplete']['id_var']) && !empty($multivar['autocomplete']['id_var'])) {
                    $multivar['autocomplete']['id_var'] = PH_ADDITIONAL_VAR_PREFIX . $multivar['autocomplete']['id_var'];
                }
                if (!empty($multivar['autocomplete']['clear_fields'])) {
                    foreach ($multivar['autocomplete']['clear_fields'] as $key => $clear_field) {
                        $multivar['autocomplete']['clear_fields'][$key] = PH_ADDITIONAL_VAR_PREFIX . $clear_field;
                    }
                }
                if (!empty($multivar['autocomplete']['fill_options'])) {
                    foreach ($multivar['autocomplete']['fill_options'] as $key => $fill_option) {
                        if (preg_match('#^\$(.*) =#', $fill_option)) {
                            $complete_var = preg_replace('#^\$(.*) =\> *(.*)#', '$1', $fill_option);
                            $new_complete_var = PH_ADDITIONAL_VAR_PREFIX . $complete_var;
                            $new_value = preg_replace('#' . $complete_var . '#', $new_complete_var, $fill_option);
                            $multivar['autocomplete']['fill_options'][$key] = $new_value;
                        }
                    }
                }
            }

            $vars[] = $multivar;
        }

        //sort the multivars according to the settings
        usort($vars, array($this, 'sortMultiFields'));

        $vars_count = count($vars);
        for ($i = $vars_count-1; $i > 0; $i--) {
            if (empty($vars[$i]['hidden'])) {
                $vars[$i]['last_visible'] = true;
                break;
            }
        }

        return $vars;
    }

    /**
     * Sets custom actions definitions
     */
    public function sortMultiFields($a, $b) {
        $a_name = preg_replace('#^additional_#', '', $a['name']);
        $b_name = preg_replace('#^additional_#', '', $b['name']);
        $multiFields = array_flip($this->multiFields);

        if (!isset($multiFields[$a_name])) {
            //shift the hidden fields at the beginning
            return -1;
        } elseif (!isset($multiFields[$b_name])) {
            //shift the hidden fields at the beginning
            return 1;
        } elseif ($multiFields[$a_name] < $multiFields[$b_name]) {
            return -1;
        } else {
            return 1;
        }
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        if ($this->action == 'filter') {
            $this->actionDefinitions = array($this->action);
            $this->afterActionDefinitions = array();
        }

        $this->getModel();
        $actions = parent::getActions($action_defs);

        //get permissions of the currently logged user
        $this->getUserPermissions();
        if (!$this->model && in_array($this->registry->get('action'), ['list', 'search', 'getListActions'])) {
            $customize = '';
            $found = 0;

            $custom_filters = array();
            if ($this->registry['request']->get('type')) {
                $customize = 'ct.id="' . $this->registry['request']->get('type') . '"';
                $found++;
            } else if ($this->registry['request']->get('type_section')) {
                $customize = 'ct.type_section="' . $this->registry['request']->get('type_section') . '"';
                $found++;
            } else if ($this->registry['request']->isRequested('search_fields')) {
                $custom_filters['search_fields'] = $this->registry['request']->get('search_fields');
                $custom_filters['compare_options'] = $this->registry['request']->get('compare_options');
                $custom_filters['values'] = $this->registry['request']->get('values');
            } else if ($this->registry['session']->isRequested($this->action . '_customer')) {
                $custom_filters = $this->registry['session']->get($this->action . '_customer');
            }

            if (!empty($custom_filters)) {
                // shows if there is a type defined and if so doesn't add the type section filter
                $type_defined = false;
                if (isset($custom_filters['search_fields'])) {
                    foreach ($custom_filters['search_fields'] as $key => $where) {
                        if (preg_match('#c\.type#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            $customize = 'ct.id="' . $custom_filters['values'][$key] . '"';
                            if ($type_defined) {
                                $found++;
                            } else {
                                $type_defined = true;
                                $found = 1;
                            }
                        } else if (preg_match('#ct\.type_section#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            if (! $type_defined) {
                                $customize = 'ct.type_section="' . $custom_filters['values'][$key] . '"';
                                $found++;
                            }
                        }
                    }
                } elseif (isset($custom_filters['hidden_type'])) {
                    $customize = 'ct.id = ' . $custom_filters['hidden_type'];
                    $found++;
                } elseif (isset($custom_filters['hidden_type_section'])) {
                    $customize = 'ct.type_section = ' . $custom_filters['hidden_type_section'];
                    $found++;
                }
            }
        }

        //prepare add options company
        $_options_company = array(
            array('label' => $this->i18n('customers_add_new_company'), 'option_value' => 1),
            array('label' => $this->i18n('customers_add_new_person'), 'option_value' => 0)
        );

        //prepare add options types
        require_once($this->modelsDir . 'customers.types.factory.php');
        $filters = array('model_lang' => $this->getModelLang(),
                         'sanitize' => true,
                         'where' => array('ct.active=1'),
                         'sort' => array('ct.position ASC', 'cti18n.name ASC'));
        if (!empty($customize) && $found == 1) {
            $filters['where'][] = $customize;
        }
        // no need to get relations for autocompleters
        $this->registry->set('skipRelatedTypes', true, true);

        $customerTypes = Customers_Types::search($this->registry, $filters);

        $_options_types_add = array();
        $_options_types_multiadd = array();
        $allowed_types = array();

        $_options_types_transfer = array();
        // type of current model (variable is used for 'transfer' action)
        $custType = '';
        if (isset($actions['transfer']) && $this->model && $this->model->get('id')) {
            $filters = array('model_lang' => $this->getModelLang(),
                             'sanitize' => true,
                             'where' => array('ct.id=' . $this->model->get('type')));
            $custType = Customers_Types::searchOne($this->registry, $filters);

            if ($custType && !is_array($custType->get('transfer_types'))) {
                $custType->set('transfer_types', preg_split('#\s*,\s*#', $custType->get('transfer_types')), true);
            }
        }

        foreach ($customerTypes as $type) {
            $type_permition = $this->checkActionPermissions($this->module . $type->get('id'), 'add');
            $type_permition_multi = $this->checkActionPermissions($this->module . $type->get('id'), 'multiadd');

            if ($type_permition) {
                $allowed_types[] = $type->get('id');
                $_options_types_add[] = array(
                    'label' => $type->get('name'),
                    'option_value' => $type->get('id'));

                // prepare possible types for transfer
                if ($custType && $custType->get('id') != $type->get('id') &&
                in_array($type->get('id'), $custType->get('transfer_types')) &&
                $custType->get('counter') == $type->get('counter')) {
                    if (!($type->get('kind') == 'person' && $this->model->get('is_company') ||
                    $type->get('kind') == 'company' && !$this->model->get('is_company'))) {
                        $_options_types_transfer[] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }
            }
            if ($type_permition_multi) {
                $_options_types_multiadd[] = array(
                    'label' => $type->get('name'),
                    'option_value' => $type->get('id'));
            }
        }

        // remove flag from registry
        $this->registry->remove('skipRelatedTypes');

        $theme = $this->registry['theme'];
        $ThemeCanProvideIcons = is_callable([$theme, 'getIconForAction']);

        // list action
        if (isset($actions['list'])) {
            // extend the link for list to clear type sections and types
            if ($this->model && $this->model->get('id')) {
                $actions['list']['ajax_no'] = 1;
                $actions['list']['drop_menu'] = true;

                $actions['list']['options']['previous_list']['img'] = 'list';
                if ($ThemeCanProvideIcons) {
                    $actions['list']['options']['previous_list']['icon'] = $theme->getIconForAction('list');;
                }
                $actions['list']['options']['previous_list']['label'] = $this->i18n('previous_list');
                $actions['list']['options']['previous_list']['url'] = $actions['list']['url'];
                if (isset($actions['search'])) {
                    $actions['list']['options']['previous_search']['img'] = 'search';
                    if ($ThemeCanProvideIcons) {
                        $actions['list']['options']['previous_search']['icon'] = $theme->getIconForAction('search');
                    }
                    $actions['list']['options']['previous_search']['label'] = $this->i18n('previous_search');
                    $actions['list']['options']['previous_search']['url'] = $actions['search']['url'];
                }

                $actions['list']['options']['all_customers']['img'] = 'customers';
                if ($ThemeCanProvideIcons) {
                    $actions['list']['options']['all_customers']['icon'] = $theme->getIconForRecord('customers');
                }
                $actions['list']['options']['all_customers']['label'] = $this->i18n('customers_all_customers');
                $actions['list']['options']['all_customers']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';

                $actions['list']['url'] = $actions['list']['url'] . sprintf('&amp;type=%d&amp;type_section=', $this->model->get('type'));
            } else {
                $actions['list']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';
            }
        }

        if (isset($actions['add']) && !empty($_options_types_add)) {
            $_add_options = array(
                array (
                    'custom_id' => 'in_dds_',
                    'name' => 'in_dds',
                    'type' => 'text',
                    'required' => 0,
                    'label' => $this->i18n('customers_in_dds'),
                    'help' => $this->i18n('help_customers_in_dds'),
                    'value' => ''),
                );
            $actions['add']['options'] = $_add_options;
        } else {
            unset ($actions['add']);
        }

        if (isset($actions['transfer']) && !empty($_options_types_transfer)) {
            //prepare transfer options
            $options = array (
                array (
                    'custom_id' => 'to_type',
                    'name' => 'to_type',
                    'type' => 'dropdown',
                    'label' => $this->i18n('customers_transfer_type'),
                    'help' => $this->i18n('customers_transfer_type_legend'),
                    'options' => $_options_types_transfer,
                    'value' => ($this->registry['request']->get('to_type')) ?
                                $this->registry['request']->get('to_type') :
                                $_options_types_transfer[0]['option_value']
                )
            );

            if ($ThemeCanProvideIcons) {
                $options[0]['icon'] = $theme->getIconForAction('transfer');
            }

            $actions['transfer']['options'] = $options;
            $actions['transfer']['url'] = '';
            $actions['transfer']['ajax_no'] = 1;
        } else {
            unset($actions['transfer']);
        }

        if (isset($actions['multiadd']) && !empty($_options_types_multiadd)) {
            //prepare multiadd options
            $multiadd_options = array (
                array (
                    'custom_id' => 'is_company__',
                    'name' => 'is_company',
                    'type' => 'radio',
                    'label' => $this->i18n('customers_company_person'),
                    'help' => $this->i18n('customers_add_legend'),
                    'options' => $_options_company,
                    'value' => ($this->registry['request']->get('is_company')) ?
                                $this->registry['request']->get('is_company') : '0'),
                array (
                    'custom_id' => 'type__',
                    'name' => 'type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('customers_type'),
                    'help' => $this->i18n('customers_add_legend'),
                    'options' => $_options_types_multiadd,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : $_options_types_multiadd[0]['option_value']),
            );
            $actions['multiadd']['options'] = $multiadd_options;
        } else {
            unset ($actions['multiadd']);
        }

        //add action
        if (isset($actions['adds'])) {
            $actions['adds']['label'] = $this->i18n('add');
            $actions['adds']['ajax_no'] = 1;
            if($theme->isModern()) {
                $actions['adds']['template'] = PH_MODULES_DIR . 'customers/view/templates/_action_add.html';
            } else {
                $actions['adds']['template'] = PH_MODULES_DIR . 'customers/templates/_action_add.html';
            }
            if (isset($actions['add'])) {
                $actions['adds']['options']['add'] = $actions['add'];
                unset ($actions['add']);
            }
            if (isset($actions['multiadd'])) {
                $actions['adds']['options']['multiadd'] = $actions['multiadd'];
                unset ($actions['multiadd']);
            }
            if (empty($actions['adds']['options'])) {
                unset ($actions['adds']);
            } else {
                if (!isset($actions['adds']['options']['multiadd'])) {
                    // if only ADD action is available
                    if (!$this->model && in_array($this->registry->get('action'), ['list', 'search', 'getListActions'])) {
                        if (count($_options_types_add) == 1) {
                            $actions['adds']['url'] .= '&amp;operation=add&amp;type=' . $_options_types_add[0]['option_value'];
                        } elseif (!empty($allowed_types)) {
                            $actions['adds']['url'] .= '&amp;operation=add&amp;allowed_types=' . implode(',', $allowed_types);
                        }
                    } else {
                        $actions['adds']['url'] .= '&amp;operation=add' . (count($_options_types_add) == 1 ? ('&amp;type=' . $_options_types_add[0]['option_value']) : '');
                    }
                    $actions['adds']['options'] = '';
                    unset($actions['adds']['ajax_no']);
                    unset($actions['adds']['template']);
                } elseif (!$this->model && in_array($this->registry->get('action'), ['list', 'search', 'getListActions'])) {
                    $actions['adds']['allowed_types'] = implode(',', $allowed_types);
                }
            }
        }

        // check permissions for branches
        if (isset($actions['branches']) && !$this->checkAccessModule(false, 'customers', '_access_', 'branches')) {
            unset($actions['branches']);
        } elseif (isset($actions['branches']) && $this->model) {
            $actions['branches']['label'] = $this->model->getBranchLabels('branches');
        }

        // check permissions for contactpersons
        if (isset($actions['contactpersons']) && !$this->checkAccessModule(false, 'customers', '_access_', 'contactpersons')) {
            unset($actions['contactpersons']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['manage_outlooks'])) {
            $actions['manage_outlooks']['options'] = 1;
        } else {
            unset($actions['manage_outlooks']);
        }

        // communications action
        if (isset($actions['communications'])) {
            $actions['communications']['ajax_no'] = 1;
            $actions['communications']['drop_menu'] = true;
            $actions['communications']['hide_label'] = true;

            if (isset($actions['emails']) || isset($actions['comments']) || isset($actions['minitasks'])) {
                if (isset($actions['comments'])) {
                    $actions['communications']['options']['comments']['img'] = 'comments';
                    if ($ThemeCanProvideIcons) {
                        $actions['communications']['options']['comments']['icon'] = $theme->getIconForAction('comments');
                    }
                    $actions['communications']['options']['comments']['label'] = $actions['comments']['label'];
                    $actions['communications']['options']['comments']['url'] = $actions['communications']['url'] . '&amp;communication_type=comments';
                    unset($actions['comments']);
                }
                if (isset($actions['emails'])) {
                    $actions['communications']['options']['emails']['img'] = 'email';
                    if ($ThemeCanProvideIcons) {
                        $actions['communications']['options']['emails']['icon'] = $theme->getIconForAction('emails');
                    }
                    $actions['communications']['options']['emails']['label'] = $this->i18n('customers_action_email');
                    $actions['communications']['options']['emails']['url'] = $actions['communications']['url'] . '&amp;communication_type=emails';
                    unset($actions['emails']);
                }
                if (isset($actions['minitasks'])) {
                    $actions['communications']['options']['minitasks']['img'] = 'minitasks';
                    if ($ThemeCanProvideIcons) {
                        $actions['communications']['options']['minitasks']['icon'] = $theme->getIconForAction('minitasks');
                    }
                    $actions['communications']['options']['minitasks']['label'] = $actions['minitasks']['label'];
                    $actions['communications']['options']['minitasks']['url'] = $actions['communications']['url'] . '&amp;communication_type=minitasks';
                    // do not unset yet, action is used in "Create" as well
                    //unset($actions['minitasks']);
                }
            } else {
                unset($actions['communications']);
            }
        } else {
            if (isset($actions['emails'])) {
                unset($actions['emails']);
            }
            if (isset($actions['comments'])) {
                unset($actions['comments']);
            }
            if (isset($actions['minitasks'])) {
                unset($actions['minitasks']);
            }
        }

        if ($this->model && $this->model->get('id') && isset($actions['create'])) {
            $actions['create']['label'] = $this->i18n('create');
            $actions['create']['ajax_no'] = 1;
            $actions['create']['template'] = PH_MODULES_DIR . 'customers/templates/_action_create.html';

            if (isset($actions['documents']) && $this->checkActionPermissions('documents', 'add')) {

                $direction_labels[PH_DOCUMENTS_INCOMING] = $this->i18n('customers_documents_incoming');
                $direction_labels[PH_DOCUMENTS_OUTGOING] = $this->i18n('customers_documents_outgoing');
                $direction_labels[PH_DOCUMENTS_INTERNAL] = $this->i18n('customers_documents_internal');

                require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
                $filters = array('where' => array('dt.active = 1',
                                                  'dt.inheritance = 0'),
                                 'model_lang' => $this->getModelLang(),
                                 'sanitize' => true,
                                 'sort' => array('dti18n.name ASC'));
                $types_all = Documents_Types::search($this->registry, $filters);

                $_options = array();
                foreach ($types_all as $type) {
                    if ($this->checkActionPermissions('documents' . $type->get('id'), 'add')) {
                        $_options[$direction_labels[$type->get('direction')]][] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }

                if (!empty($_options)) {
                    $first_key = array_keys($_options);
                    $first_key = reset($first_key);
                    //prepare documents options
                    $actions['documents']['options'] = array (
                        array (
                            'custom_id'     => 'document_type__',
                            'name'          => 'document_type',
                            'type'          => 'dropdown',
                            'required'      => 1,
                            'label'         => $this->i18n('customers_document_type'),
                            'help'          => $this->i18n('customers_document_type'),
                            'optgroups'     => $_options,
                            'value'         => $this->registry['request']->get('document_type') ?: $_options[$first_key][0]['option_value']
                        )
                    );
                    $actions['documents']['label'] = $this->i18n('customers_document');
                }
            }

            if (!empty($actions['documents']['options'])) {
                $actions['create']['options']['documents'] = $actions['documents'];
            }
            unset ($actions['documents']);

            if (isset($actions['tasks']) && $this->checkActionPermissions('tasks', 'add')) {
                require_once PH_MODULES_DIR . 'tasks/models/tasks.types.factory.php';
                $filters = array('where' => array('tt.active = 1'),
                                 'model_lang' => $this->getModelLang(),
                                 'sanitize' => true,
                                 'sort' => array('tti18n.name ASC'));
                $types_all = Tasks_Types::search($this->registry, $filters);

                $_options = array();
                foreach ($types_all as $type) {
                    if ($this->checkActionPermissions('tasks' . $type->get('id'), 'add')) {
                        $_options[] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }

                if (!empty($_options)) {
                    //prepare task options
                    $actions['tasks']['options'] = array (
                        array (
                            'custom_id'     => 'task_type__',
                            'name'          => 'task_type',
                            'type'          => 'dropdown',
                            'required'      => 1,
                            'label'         => $this->i18n('customers_task_type'),
                            'help'          => $this->i18n('customers_task_type'),
                            'options'       => $_options,
                            'value'         => $this->registry['request']->get('task_type') ?: $_options[0]['option_value']
                        )
                    );
                    $actions['tasks']['label'] = $this->i18n('customers_task');
                }
            }

            if (!empty($actions['tasks']['options'])) {
                $actions['create']['options']['tasks'] = $actions['tasks'];
            }
            unset ($actions['tasks']);

            if (isset($actions['events']) && $this->checkActionPermissions('events', 'add')) {
                require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
                $filters = array('where' => array('et.keyword NOT IN (\'reminder\', \'plannedtime\')', 'et.active = 1'),
                                 'model_lang' => $this->getModelLang(),
                                 'sanitize' => true,
                                 'sort' => array('eti18n.name ASC'));
                $types_all = Events_Types::search($this->registry, $filters);

                $_options = array();
                foreach ($types_all as $type) {
                    if ($this->checkActionPermissions('events' . $type->get('id'), 'add')) {
                        $_options[] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }

                if (!empty($_options)) {
                    //prepare event options
                    $actions['events']['options'] = array (
                        array (
                            'custom_id'     => 'event_type__',
                            'name'          => 'event_type',
                            'type'          => 'dropdown',
                            'required'      => 1,
                            'label'         => $this->i18n('customers_event_type'),
                            'help'          => $this->i18n('customers_event_type'),
                            'options'       => $_options,
                            'value'         => $this->registry['request']->get('event_type') ?: $_options[0]['option_value']
                        )
                    );
                    $actions['events']['label'] = $this->i18n('customers_event');
                }
            }

            if (!empty($actions['events']['options'])) {
                $actions['create']['options']['events'] = $actions['events'];
            }
            unset ($actions['events']);

            if (isset($actions['projects']) && $this->checkActionPermissions('projects', 'add')) {
                require_once PH_MODULES_DIR . 'projects/models/projects.types.factory.php';
                $filters = array('where' => array('pt.active = 1'),
                                 'model_lang' => $this->getModelLang(),
                                 'sanitize' => true,
                                 'sort' => array('pti18n.name ASC'));
                $types_all = Projects_Types::search($this->registry, $filters);

                $_options = array();
                foreach ($types_all as $type) {
                    if ($this->checkActionPermissions('projects' . $type->get('id'), 'add')) {
                        $_options[] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }

                if (!empty($_options)) {
                    //prepare project options
                    $actions['projects']['options'] = array (
                        array (
                            'custom_id'     => 'project_type__',
                            'name'          => 'project_type',
                            'type'          => 'dropdown',
                            'required'      => 1,
                            'label'         => $this->i18n('customers_project_type'),
                            'help'          => $this->i18n('customers_project_type'),
                            'options'       => $_options,
                            'value'         => $this->registry['request']->get('project_type') ?: $_options[0]['option_value']
                        )
                    );
                    $actions['projects']['label'] = $this->i18n('customers_project');
                }
            }

            if (!empty($actions['projects']['options'])) {
                $actions['create']['options']['projects'] = $actions['projects'];
            }
            unset ($actions['projects']);

            if (isset($actions['minitasks'])) {
                if ($this->registry['currentUser']->checkRights('minitasks', 'add')) {
                    $actions['minitasks']['label'] = $this->i18n('customers_minitask');
                    $actions['create']['options']['minitasks'] = $actions['minitasks'];
                }
                unset ($actions['minitasks']);
            }

            if (!$actions['create']['options']) {
                unset ($actions['create']);
            } else {
                // GET CUSTOMER's ID
                $actions['create']['customer'] = $this->model->get('id');
            }
        } else {
            unset ($actions['create']);

            if (isset($actions['documents'])) {
                unset($actions['documents']);
            }
            if (isset($actions['tasks'])) {
                unset($actions['tasks']);
            }
            if (isset($actions['events'])) {
                unset($actions['events']);
            }
            if (isset($actions['projects'])) {
                unset($actions['projects']);
            }
            if (isset($actions['minitasks'])) {
                unset($actions['minitasks']);
            }
        }

        if (isset($actions['print'])) {
            $patterns_options = array();
            if ($this->model->get('type')) {
                $filters_type = array(
                    'where' => array(
                        'ct.id = ' . $this->model->get('type'),
                        'ct.active = 1'
                    ),
                    'model_lang' => $this->getModelLang(),
                    'sanitize' => true
                );
                $customer_type = Customers_Types::searchOne($this->registry, $filters_type);

                //get the id of the default document type print template
                $default_pattern_id = 0;
                if ($customer_type && $customer_type->get('default_pattern')) {
                    $default_pattern_id = $customer_type->get('default_pattern');
                }

                //get all generate/print patterns for this type
                require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
                $filters_patterns = array(
                    'where' => array(
                        'p.model = \'Customer\'',
                        'p.model_type = \'' . $this->model->get('type') . '\'',
                        'p.active = 1',
                        'p.list = 0'
                    ),
                    'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                    'model_lang' => $this->registry['lang'],
                    'sanitize' => true
                );
                $patterns = Patterns::search($this->registry, $filters_patterns);

                $available_patterns = array();
                foreach ($patterns as $pattern) {
                    $available_patterns[] = $pattern->get('id');
                    $patterns_options[] = array(
                        'id'        => $pattern->get('id'),
                        'label'     => $pattern->get('name'),
                        'img'       => $pattern->getIcon(),
                        'url'       => $actions['print']['url'] . '&amp;pattern=' . $pattern->get('id'),
                        'target'    => '_blank',
                    );
                }
            }

            if (empty($patterns_options)) {
                unset($actions['print']);
            } else {
                if ($default_pattern_id && in_array($default_pattern_id, $available_patterns)) {
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $default_pattern_id;
                } elseif (count($available_patterns) == 1) {
                    // sets the first pattern in the list as default and assigns a link to the direct print
                    list($first_pattern) = $patterns_options;
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $first_pattern['id'];
                } else {
                    $actions['print']['url'] = '#';
                }
                $actions['print']['drop_menu'] = true;
                $actions['print']['no_tab'] = true;
                $actions['print']['label'] = '';
                $actions['print']['target'] = '_blank';
                $actions['print']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'print';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['print']['img'] .= '_plus';
                }
                $actions['print']['options'] = $patterns_options;
            }
        }

        if (isset($actions['tag']) && $this->model && $this->model->get('id') && $this->model->getAvailableTags()) {

            $this->model->getTags();

            if ($this->model->checkPermissions('tags_view') && ($this->model->get('tags') && array_intersect($this->model->get('tags'), array_keys($this->model->get('available_tags'))) || $this->model->checkPermissions('tags_edit'))) {
                $actions['tag']['options'] = array('label' => $this->i18n('confirm_tags'));
                $actions['tag']['ajax_no'] = 1;
                $actions['tag']['template'] = '_action_tag.html';
                $actions['tag']['model_id'] = $this->model->get('id');
            } else {
                unset($actions['tag']);
            }
        } else {
            unset($actions['tag']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['printlist'])) {

            //get all print list patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->modelName . '\'',
                    'p.active = 1',
                    'p.list = 1'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );

            if ($found == 1 && $customize) {
                $ts_id = preg_replace('#.*=\s*(\'|\")?(\d+)(\'|\")?#', '$2', $customize);
                if (preg_match('#^ct\.id#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 0';
                } elseif (preg_match('#^ct\.type_section#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 1';
                }
            } else {
                $filters_patterns['where'][] = 'CONVERT(p.model_type, SIGNED INTEGER) = 0';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns_list = Patterns::search($this->registry, $filters_patterns);

            $additional_query_string = '&amp;session_param=' . $this->registry->get('action') . '_' . strtolower($this->modelName);

            $patterns_options = array();
            foreach ($patterns_list as $pattern) {
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['printlist']['url'] . '&amp;pattern=' . $pattern->get('id') . $additional_query_string,
                    'target'    => '_blank',
                    'onclick'   => 'return confirmPrintlist();'
                );
            }

            if (empty($patterns_options)) {
                unset($actions['printlist']);
            } else {
                if (count($patterns_options) == 1) {
                    // if there is only one pattern, its options are taken for the button
                    list($first_pattern) = $patterns_options;
                    $actions['printlist']['url'] = $first_pattern['url'];
                    $actions['printlist']['onclick'] = $first_pattern['onclick'];
                    $actions['printlist']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'printlist';
                } else {
                    $actions['printlist']['url'] = '#';
                    $actions['printlist']['img'] = 'printlist';
                }

                $actions['printlist']['drop_menu'] = true;
                $actions['printlist']['no_tab'] = true;
                $actions['printlist']['label'] = '';
                $actions['printlist']['target'] = '_blank';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['printlist']['img'] .= '_plus';
                }
                $actions['printlist']['options'] = $patterns_options;
            }
        } else {
            unset($actions['printlist']);
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        $_page_menu = [
            'general' => [],
            'context' => [],
            'quick' => [],
            'infriquent' => [],
        ];

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }

            foreach ($this->actionListPageMenu as $k2=>$v2) {
                if (in_array($key, $v2)) {
                    $_page_menu[$k2][$key] = $action;
                }
            }

            if ($flag_match) {
                unset($actions[$key]);
            }


        }

        if (!empty($_page_menu['infriquent'])) {
            $_page_menu['context']['infriquent'] = [
                'name' => 'infriquent_menu',
                'icon' => 'more_vert',
                'url' => '#',
                'drop_menu' => true,
                'options' => $_page_menu['infriquent'],
            ];
        }

        // check the current action and sets the alternative actions for view/edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } else if ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit or view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } else if (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && ! empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('data');
                    $_left_menu[$key]['img'] = 'custom_data';
                } elseif ($key == 'branches') {
                    if ($this->model->get('branch_icon_file')) {
                        $_left_menu[$key]['custom_img'] = PH_CUSTOMERS_TYPES_URL . $this->model->get('branch_icon_file');
                    }
                }
            }
        }

        if ($this->model && $this->model->get('id')) {
            if (isset($actions['search'])) {
                unset ($actions['search']);
            }
        }
        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_page_actions', $_page_menu, true);

        $modernizedActions = [
            'list',
            'add',
            'adds',
            'view',
            'edit',
            'assign',
            'relatives',
            'branches',
            'contactpersons',
            'attachments',
            'communications',
            'history',
        ];
        if (!$theme->isModern() || !in_array($this->action, $modernizedActions)) {
            // Not used in Evolution theme, made available for compatibility reasons in some actions
            $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);
        }

        return $actions;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActionOptions($action_name = '') {
        if ($this->action == 'filter') {
            $this->actionDefinitions = array($this->action);
            $this->afterActionDefinitions = array();
            return $this->actionDefinitions;
        }

        $actions = parent::getActions(array($action_name));

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['add'])) {
            $actions['add']['url'] .= '&amp;is_company=1';
        }

        return $actions;
    }

    /**
     * Sets custom actions definitions
     */
    public function getAfterActions($action_defs = array()) {
        //get default after actions
        $actions = parent::getAfterActions();

        //get permissions of the currently logged user
        $this->getUserPermissions();

        //prepare add options company
        $_options_company = array(
            array('label' => $this->i18n('customers_add_new_company'), 'option_value' => 1),
            array('label' => $this->i18n('customers_add_new_person'), 'option_value' => 0));

        //prepare add options types
        require_once($this->modelsDir . 'customers.types.factory.php');
        $filters = array('model_lang' => $this->getModelLang(),
                         'sanitize' => true,
                         'where' => array('ct.active=1'),
                         'sort' => array('ct.position ASC', 'cti18n.name ASC'));
        $customerTypes = Customers_Types::search($this->registry, $filters);

        $_options_types = array();
        foreach ($customerTypes as $type) {
            $_options_types[] = array(
                'label' => $type->get('name'),
                'option_value' => $type->get('id'));
        }

        if (isset($actions['add'])) {
            //prepare add options
            /*$add_options = array (
                array (
                    'custom_id' => 'aa1_is_company',
                    'name' => 'aa1_is_company',
                    'type' => 'radio',
                    'label' => $this->i18n('customers_company_person'),
                    'help' => $this->i18n('customers_add_legend'),
                    'options' => $_options_company,
                    'value' => ($this->registry['request']->get('is_company')) ?
                                $this->registry['request']->get('is_company') : 1),
                array (
                    'custom_id' => 'aa1_type',
                    'name' => 'aa1_type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('customers_type'),
                    'help' => $this->i18n('customers_add_legend'),
                    'options' => $_options_types,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : @$_options_types[0]['option_value'])
            );
            $actions['add']['options'] = $add_options;*/
        }

        //prepare multiadd options
        if (isset($actions['multiadd'])) {
            $multiadd_options = array (
                array (
                    'custom_id' => 'aa2_is_company',
                    'name' => 'aa2_is_company',
                    'type' => 'radio',
                    'label' => $this->i18n('customers_company_person'),
                    'help' => $this->i18n('customers_add_legend'),
                    'options' => $_options_company,
                    'value' => ($this->registry['request']->get('is_company')) ?
                                $this->registry['request']->get('is_company') : '0'),
                array (
                    'custom_id' => 'aa2_type',
                    'name' => 'aa2_type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('customers_type'),
                    'help' => $this->i18n('customers_add_legend'),
                    'options' => $_options_types,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : @$_options_types[0]['option_value'])
            );
            $actions['multiadd']['options'] = $multiadd_options;
        }

        return $actions;
    }

    /**
     * Selects certain items by specified parameter.
     * This method prints unordered list and exists
     */
    public function _select($autocomplete = array()) {
        //get the request
        $request = &$this->registry['request'];

        //name of the input field that request the autocompleter
        $source_field = $request->get('field');

        //get fields from other tables
        $get_fields = array('main_trademark');
        //ignore fields from other tables
        $ignore_fields = array('added_by_name', 'modified_by_name', 'deleted_by_name');
        //check for employees search
        $tmp_filters = $request->get('filters');
        if (!empty($tmp_filters)
                && count($tmp_filters) == 1
                && isset($tmp_filters['<type>'])
                && $tmp_filters['<type>'] == (string)PH_CUSTOMER_EMPLOYEE
            && preg_match('#^employee1?_autocomplete$#', $source_field)) {
            $get_employees = true;
            // update $get_fields variable when searching for employees
            $get_fields = array();
        } else {
            $get_employees = false;
        }
        // get/set fields to search by
        $search_fields = array();
        if (!$search_fields = $request->get('search')) {
            if ($get_employees) {
                //employees search
                $search_fields = array('<name>');
            } else {
                //check for autocomplete_search_fields setting
                $autocomplete_search_fields = $this->registry['config']->getParamAsArray('customers', 'autocomplete_search_fields');
                if (!empty($autocomplete_search_fields)) {
                    foreach ($autocomplete_search_fields as $tmp_field) {
                        $search_fields[] = '<' . $tmp_field . '>';
                    }
                } else {
                    $search_fields = array('<code>', '<name>');
                }
            }
        }
        if (!is_array($search_fields)) {
            $search_fields = array($search_fields);
        }
        $i18n_columns = array_keys($this->registry['db']->MetaColumns(DB_TABLE_CUSTOMERS_I18N, false));
        $main_alias = Customers::getAlias('customers', 'customers');
        foreach ($search_fields as $idx => $field) {
            $field = preg_replace('#<|>#', '', $field);
            if (preg_match('#^a__*#', $field)) {
                //search by additional variable
                $search_fields[$idx] = $field;
            } else {
                //search by main field
                $alias = $main_alias;
                if (in_array(strtoupper($field), $i18n_columns)) {
                    //search by main field in i18n table
                    $alias .= 'i18n';
                }
                $search_fields[$idx] = $alias . '.' . $field;
            }
        }

        //prepare sort if is requested
        $sort = array();
        if (!$r_sort = $request->get('sort')) {
            $r_sort = array('<name>');
        }

        ////////////////////////////////////
        // Order suggestions by relevance //
        ////////////////////////////////////
        // If the sort is by name (but not when search is by specified ids (from filter or refresh))
        if (in_array('<name>', $r_sort) && !(count($search_fields) == 1 && reset($search_fields) == 'c.id')) {
            // Get the search string
            if ($request->isRequested('autocomplete_filter')) {
                $search_string = $request->get('search_value');
            } else {
                $search_string = $request->get($source_field);
            }
            // If the search string is an array, then get its first value
            // This usually happens into the reports module, when using a multiple_*filter.html template for multiple autocomplete filter
            if (is_array($search_string)) {
                $search_string = array_shift($search_string);
            }
            // If the search string is not empty
            if (!empty($search_string)) {
                // Split the search string to words
                $search_pieces = preg_split('#\s+#u', trim($search_string));
                $order_case    = General::buildSQLOrderWeight('TRIM(CONCAT(ci18n.name, \' \', ci18n.lastname))', $search_pieces);
            }
        }
        ////////////////////////////////////////////
        // END OF: Order suggestions by relevance //
        ////////////////////////////////////////////

        foreach ($r_sort as $key => $field) {
            preg_match('#<([^>]*)>(\s+(ASC|DESC))?#i', $field, $sort_matches);
            if (!empty($sort_matches[1])) {
                //$order: ASC/DESC
                $order = (!empty($sort_matches[3]) ? $sort_matches[3] : 'ASC');
                if ($sort_matches[1] == 'name') {
                    //sort by customer name
                    if (!empty($order_case)) {
                        //IMPORTANT: use relevance sorting when searching by name
                        $sort[] = $order_case;
                    }
                    $sort[] = preg_replace('#<name>#', 'ci18n.name', $field);
                    $sort[] = preg_replace('#<name>#', 'ci18n.lastname', $field);
                } elseif (preg_match('#^a__*#', $sort_matches[1])) {
                    //sort by additional variable
                    $sort[] =  $sort_matches[1] . ' ' . $order;
                } else {
                    //sort by main field
                    $alias = $main_alias;
                    if (in_array(strtoupper($sort_matches[1]), $i18n_columns)) {
                        //sort by main field in i18n table
                        $alias .= 'i18n';
                    }
                    $sort[] = $alias . '.' . $sort_matches[1] . ' ' . $order;
                }
            }
        }

        $get_contactpersons = false;

        $additional_where = array();
        if ($req_filters = $request->get('filters')) {
            foreach ($req_filters as $filter => $value) {
                $alias = $main_alias . '.';
                $field = preg_replace('#<|>#', '', $filter);
                // escape value for the SQL search query
                $value = General::slashesEscape($value);

                switch ($filter) {
                    case '<tag>':
                        $alias = 'tags.';
                        $field = 'tag_id';
                        break;
                    // cases for contactpersons - start
                    case '<contactpersons>':
                        $additional_where['contactpersons'] = 1;
                        // update $get_fields variable when searching in contact persons
                        $get_fields = array();
                        $get_contactpersons = true;
                        continue 2;
                    case '<customer>':
                        $additional_where[] = 'pc.id = \'' . $value . '\' AND';
                        continue 2;
                    case '<branch>':
                        $additional_where[] = 'bn.id = \'' . $value . '\' AND';
                        continue 2;
                    // cases for contactpersons - end
                    default:
                        if (preg_match('#^a__*#', $field)) {
                            $alias = '';
                        } elseif (in_array(strtoupper($field), $i18n_columns)) {
                            //search by main field in i18n table
                            $alias = $main_alias . 'i18n.';
                        }
                        break;
                }

                if (preg_match('#^\s*(!?=)(.*)#', $value, $matches)) {
                    // search expression for a single value
                    $additional_where[] =
                        sprintf('%s%s %s \'%s\' AND',
                                $alias, $field, trim($matches[1]), trim($matches[2]));
                    continue;
                } elseif (preg_match('#^\s*((not\s+)?in\s*)\((.*)\)\s*#i', $value, $matches)) {
                    // search expression for multiple values
                    $negative_search = preg_match('#not#i', $matches[1]);
                    $compare_operator = $negative_search ? '!=' : '=';
                    $amatches = preg_split('#\s*,\s*#', trim($matches[3]));
                    $count_or_clauses = count($amatches);
                    foreach ($amatches as $idx => $amatch) {
                        $logical_operator = $negative_search || $idx == ($count_or_clauses - 1) ? 'AND' : 'OR';
                        $additional_where[] =
                            sprintf('%s%s %s \'%s\' %s',
                                    $alias, $field, $compare_operator, $amatch, $logical_operator);
                    }
                    continue;
                }

                $vals = preg_split('#\s*,\s*#', $value);
                if (count($vals) > 1) {
                    $count_or_clauses = count($vals);
                    foreach ($vals as $idx => $val) {
                        $clause = $alias . $field . ' = \'' . $val . '\'';
                        if ($idx < $count_or_clauses - 1) {
                            $clause .= ' OR';
                        } else {
                            $clause .= ' AND';
                        }
                        $additional_where[] = $clause;
                    }
                } else {
                    $additional_where[] = $alias . $field . ' = \'' . $vals[0] . '\' AND';
                }
            }
        }
        if ($request->get('add_contact_persons')) {
            $additional_where[] = "((c.subtype='normal' AND c.is_company='0') OR c.subtype='contact') AND";
        }

        //prepare suggestions format
        if (!$suggestions_format = $request->get('suggestions')) {
            if ($get_employees) {
                //employees search
                $suggestions_format = '<name> <lastname>';
            } elseif ($get_contactpersons) {
                //contact persons search
                $suggestions_format = '<name> <lastname> (<customer_name>)';
            } else {
                $suggestions_format = '[<code>] <name> <lastname>';
            }
        }

        //prepare fill option definitions
        $fill_options = $request->get('fill_options') ?: array();
        // If there aren't fill options or if it's set that the var type is basic
        if (!$fill_options || $request->get('var_type') == 'basic') {
            //we must be in the basic vars
            //so get the autocomplete field (i.e. build the default fill options)

            // check if fields from default fill options already exist
            $default_keys_exist = array(
                '$' . $source_field => false,
                '$' . preg_replace('#_autocomplete$#', '_oldvalue', $source_field) => false
            );
            if (preg_match('#_autocomplete$#', $source_field)) {
                $default_keys_exist['$' . preg_replace('#_autocomplete$#', '', $source_field)] = false;
            }
            foreach ($default_keys_exist as $opt_key => &$exists) {
                foreach ($fill_options as $opt) {
                    if (strpos($opt, $opt_key) === 0) {
                        $exists = true;
                        break;
                    }
                }
            }
            unset($exists);

            // specify the default fill format
            $fill_oldvalue = '[<code>] <name> <lastname>';
            if ($get_employees) {
                //employees search
                $fill_oldvalue = '<name> <lastname>';
            } elseif ($get_contactpersons) {
                // contact persons search
                $fill_oldvalue = '<name> <lastname> (<customer_name>)';
            }
            if ($fill_options) {
                foreach ($fill_options as $fill_option) {
                    if (preg_match('#^\$' . $source_field . '\s*=\>#', $fill_option)) {
                        @list($notimportant, $fill_oldvalue) = preg_split('#\s*=>\s*#', $fill_option);
                    }
                }
            }

            // add default fill options only if they don't exist
            foreach ($default_keys_exist as $opt_key => $exists) {
                if (!$exists) {
                    if (preg_match('#^\$' . preg_replace('#_autocomplete$#', '', $source_field) . '$#', $opt_key)) {
                        $fill_options[] = $opt_key . ' => <id>';
                    } else {
                        $fill_options[] = $opt_key . ' => ' . $fill_oldvalue;
                    }
                }
            }
        } else {
            $fill_oldvalue = '[<code>] <name> <lastname>';
            foreach ($fill_options as $fill_option) {
                if (preg_match('#^\$' . $source_field . '\s*=\>#', $fill_option)) {
                    @list($notimportant, $fill_oldvalue) = preg_split('#\s*=>\s*#', $fill_option);
                }
            }
            $fill_options[] = '$' . preg_replace('#_autocomplete$#', '', $source_field). '_oldvalue' . ' => ' . $fill_oldvalue;
        }

        $autocomplete = array('search'             => $search_fields,
                              'sort'               => $sort,
                              'suggestions_format' => $suggestions_format,
                              'fill_options'       => $fill_options,
                              'additional_where'   => $additional_where,
                              'type'               => strtolower($this->modelFactoryName),
                              'get_fields'         => $get_fields,
                              'ignore_fields'      => $ignore_fields);

        if ($request->get('autocomplete_filter')) {
            $filters = parent::_select($autocomplete);
            return $filters;
        }
        parent::_select($autocomplete);

        exit;
    }

    /**
     * Selects certain customers/branches/contact persons by specified email
     * This method prints ordered list
     */
    public function _emailSelect() {
        $request = &$this->registry['request'];

        // get/set fields to search by
        $search_fields = array();
        if (!$search_fields = $request->get('search')) {
            $search_fields = array('<name>', '<email>');
        }

        foreach ($search_fields as $idx => $field) {
            switch ($field) {
                case '<name>':
                    $search_fields[$idx] = 'CONCAT_WS(\' \', ci18n.name, ci18n.lastname)';
                    break;
                case '<email>':
                    $search_fields[$idx] = 'c.email';
                    break;
                default:
                    $search_fields[$idx] = 'c.' . preg_replace('#<|>#', '', $field);
                    break;
            }
        }

        //name of the input field that request the autocompleter
        $source_field = $request->get('field');

        //key is the search word
        $key = $request->get($source_field);

        //row is the group table row index (starting with 1 instead of 0)
        $row = $request->get('row');

        //the key might be an array derived from a group multi index table
        if (is_array($key)) {
            $key = $key[$row-1];
        }

        $filters = array(
            'where' => array(
                'c.subtype IS NOT NULL AND (c.email != "" OR c.subtype = "normal" AND c.is_company=1) '
            ),
            'sanitize' => true,
            'sort'     => array('c.subtype ASC'),
            //skip checking permissions by type as we search in branches and contact persons as well
            'skip_permissions_check' => true
        );

        if ((@!$fill_options = $request->get('fill_options')) && !$request->isRequested('autocomplete_filter')) {
            exit('<ul><li><span class="red">FILL ERROR</span></li></ul>');
        }

        if (!$request->get('ajax_filter')) {
            //check if $key match fill options for the field
            //and split the $key in to words
            foreach ($fill_options as $idx => $option) {
                // fill option must match the EXACT source field
                if (preg_match('#\$' . $source_field . '\s*=>#', $option)) {
                    $symbols = preg_replace('#(<[^<>]*>)|(\s+)|(\$' . $source_field . ')|(=>)#u', '', $option);
                    // fill options might include non-ASCII characters
                    $symbols = preg_replace_callback('#(.)#u', function($matches) {return preg_quote($matches[0], '#') . '|';}, $symbols);
                    $symbols = substr($symbols, 0, -1);
                    if (!empty($symbols)) {
                        $replaced_key = preg_replace('#' . $symbols . '#ui', ' ', $key);
                        // if there is at least one other character left
                        if (trim($replaced_key)) {
                            $key = $replaced_key;
                        }
                    }
                    $key = preg_split('#\s+#u', $key);
                    break;
                }
            }
        }

        if (!is_array($key)) {
            $key = array($key);
        }
        //clear white spaces
        foreach ($key as $idx => $k) {
            $key[$idx] = trim($k);
            if ($key[$idx] === '') {
                unset($key[$idx]);
            }
        }
        $key = array_values($key);
        //regular expression is prepared so that the keywords are later bolded
        $regex_key = '#(';
        foreach ($key as $idx => $k) {
            if ($idx == (count($key)-1)) {
                $regex_key .= preg_quote($k, '#');
            } else {
                $regex_key .= preg_quote($k, '#') . '|';
            }
        }
        $regex_key .= ')#iu';

        // escape entered values for the SQL search query
        $key = General::slashesEscape($key);

        $search_ids = array();
        if (!$request->isRequested('autocomplete_filter')) {
            //sets search values
            foreach ($key as $k) {
                $subfilters = array();
                foreach ($search_fields as $i => $field) {
                    if ($k != '' && !preg_match('#\.id(?!\w)#', $field)) {
                        $subfilters[] = $field . ' LIKE \'%' . $k . '%\'';
                        if (preg_match('#ci18n\.name#', $field)) {
                            $subfilters[] = 'ci18n.lastname LIKE \'%' . $k . '%\'';
                        } elseif (preg_match('#ui18n\.firstname#', $field)) {
                            $subfilters[] = 'ui18n.lastname LIKE \'%' . $k . '%\'';
                        }
                    } elseif ($k && preg_match('#\.id#', $field)) {
                        $search_ids = $k;
                        $subfilters[] = $field . ' IN (' . $k . ')';
                    }
                }
                // add search conditions to $filters['where'] as if they come from the "Search" form
                $num_sf = count($subfilters);
                for ($sf = 0; $sf < $num_sf; $sf++) {
                    $filters['where'][] = $subfilters[$sf] . ' ' . ($sf < $num_sf - 1 ? 'OR' : 'AND');
                }
            }
        } else {
            //when opening the filter popup window for search from autocompleter
            // (currently filter popup is not implemented)
        }

        $customers = Customers::search($this->registry, $filters);

        $results = array();
        $parent_ids = array();

        foreach ($customers as $key_cust => $customer) {
            if (! in_array($customer->get('parent_customer'), $parent_ids)) {
                $result_array = array(
                    'id'                => $customer->get('id'),
                    'is_company'        => $customer->get('is_company'),
                    'subtype'           => $customer->get('subtype'),
                    'parent_customer'   => $customer->get('parent_customer'),
                    'name'              => $customer->get('name') . ($customer->get('lastname') ? (' ' . $customer->get('lastname')) : ''),
                    'emails'            => $customer->get('email')
                );

                if ($customer->get('subtype') == 'normal' && $customer->get('is_company')) {
                    $branches = $customer->getBranches();
                    $customer->set('branches', $branches, true);
                    $contact_persons = $customer->getContactPersons(array(), true);
                    $customer->set('contact_persons', $contact_persons, true);

                    if ($customer->get('branches')) {
                        $customers_contact_persons = $customer->get('contact_persons');
                        foreach ($customer->get('branches') as $branch) {
                            if ($branch->get('id')) {
                                $result_array['parent_customer_of'][$branch->get('id')] = array(
                                    'id'                => $branch->get('id'),
                                    'is_company'        => $branch->get('is_company'),
                                    'subtype'           => $branch->get('subtype'),
                                    'parent_customer'   => $branch->get('parent_customer'),
                                    'name'              => $branch->get('name') . ($branch->get('lastname') ? (' ' . $branch->get('lastname')) : ''),
                                    'emails'            => $branch->get('email')
                                );
                                $parent_ids[] = $branch->get('parent_customer');
                            }
                            if (!empty($customers_contact_persons[$branch->get('id')])) {
                                foreach ($customers_contact_persons[$branch->get('id')] as $contact_person) {
                                    if ($contact_person->get('email')) {
                                        $result_array['parent_customer_of'][$branch->get('id')]['parent_customer_of'][$contact_person->get('id')] = array(
                                            'id'                => $contact_person->get('id'),
                                            'is_company'        => $contact_person->get('is_company'),
                                            'subtype'           => $contact_person->get('subtype'),
                                            'parent_customer'   => $contact_person->get('parent_customer'),
                                            'name'              => $contact_person->get('name') . ($contact_person->get('lastname') ? (' ' . $contact_person->get('lastname')) : ''),
                                            'emails'            => $contact_person->get('email')
                                        );
                                    }
                                }
                            }
                        }
                    }
                } else if ($customer->get('subtype') == 'branch') {
                    $contact_persons = $customer->getContactPersons($customer->get('id'));
                    foreach ($contact_persons[$customer->get('id')] as $contact_person) {
                        if ($contact_person->get('email')) {
                            $result_array['parent_customer_of'][$contact_person->get('id')] = array(
                                'id'                => $contact_person->get('id'),
                                'is_company'        => $contact_person->get('is_company'),
                                'subtype'           => $contact_person->get('subtype'),
                                'parent_customer'   => $contact_person->get('parent_customer'),
                                'name'              => $contact_person->get('name') . ($contact_person->get('lastname') ? (' ' . $contact_person->get('lastname')) : ''),
                                'emails'            => $contact_person->get('email')
                            );
                        }
                    }
                }
                $results[$customer->get('id')] = $result_array;
                $parent_ids[] = $customer->get('id');
            }
        }

        $results = $this->clearCustomersWithoutMails($results);

        $flat_mail_list = array();

        foreach ($results as $res) {
            if (!empty($res['emails'])) {
                foreach ($res['emails'] as $email) {
                    $flat_mail_list[] = array(
                        'name'      => $res['name'],
                        'email'     => $email,
                        'separator' => ''
                    );
                }
            } else {
                $flat_mail_list[] = array(
                    'name'      => $res['name'],
                    'email'     => '',
                    'separator' => ''
                );
            }

            if (!empty($res['parent_customer_of'])) {
                foreach ($res['parent_customer_of'] as $res_1) {
                    if ($res_1['emails']) {
                        foreach ($res_1['emails'] as $email) {
                            $flat_mail_list[] = array(
                                'name'      => $res_1['name'],
                                'email'     => $email,
                                'separator' => '   '
                            );
                        }
                    } else {
                        $flat_mail_list[] = array(
                            'name'      => $res_1['name'],
                            'email'     => '',
                            'separator' => '   '
                        );
                    }
                    if (!empty($res_1['parent_customer_of'])) {
                        foreach ($res_1['parent_customer_of'] as $res_2) {
                            foreach ($res_2['emails'] as $email) {
                                $flat_mail_list[] = array(
                                    'name'      => $res_2['name'],
                                    'email'     => $email,
                                    'separator' => '      '
                                );
                            }
                        }
                    }
                }
            }
        }

        printf('<ul>');
        if (!empty($flat_mail_list)) {
            $total_results = count($flat_mail_list);
            $too_much_results = false;

            if ($total_results > 20) {
                $flat_mail_list = array_slice($flat_mail_list, 0, 19);
                $too_much_results = true;
            }

            foreach ($flat_mail_list as $key_mail => $suggestions) {
                $data = array('row'          => $row,
                              'id'           => $key_mail,
                              'type'         => 'customers'
                );
                foreach ($fill_options as $option) {
                    @list($var_name, $var_value) = preg_split('#\s*=>\s*#', $option);
                    $var_name = trim($var_name);
                    $var_value = trim($var_value);
                    $option_value = $suggestions['separator'] . $suggestions['name'];
                    if ($suggestions['email']) {
                        $option_value .= ' <' . $suggestions['email'] . '>';
                    }
                    $data[$var_name] = str_replace("'", "&#39;", trim($option_value));
                }

                $marked_suggestions = preg_replace($regex_key, '<strong>$1</strong>', $option_value);
                $marked_suggestions = preg_replace(htmlspecialchars('#<(/?strong)>#'), '<$1>', htmlspecialchars($marked_suggestions));
                $marked_suggestions = preg_replace_callback('#^( +)#',
                                                            function() { return str_repeat('&nbsp;', strlen('$1')); },
                                                            $marked_suggestions);

                if ($suggestions['email']) {
                    printf('  <li id="%s_%d" title="%s">%s' . "\n",
                                'customers', $key_mail, htmlspecialchars(trim($option_value)), $marked_suggestions);
                    printf('    <input type="hidden" name="%s_%d_data" id="%s_%d_data" value=\'%s\' disabled="disabled" />' . "\n",
                                    'customers', $key_mail,
                                    'customers', $key_mail, json_encode($data));
                    printf('  </li>' . "\n");
                } else {
                    printf('<li>%s</li>', $marked_suggestions);
                }
            }
            if ($too_much_results) {
                printf('<li><i>%s</i></li>', sprintf($this->i18n('customers_mail_total_found_results'), $total_results));
            }
        } else {
            printf('  <li>%s</li>', $this->i18n('no_items_found'));
        }
        printf('</ul>');

        exit;
    }

    /**
     * get branches for the current customer
     */
    private function _getCustomerBranches() {
        $request = &$this->registry['request'];

        $customer_id = $request->get('customer_id');
        $records = array();
        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters  = array('sanitize' => true,
                              'model_lang' => $request->get('model_lang'),
                              'where' => array ('c.parent_customer = ' . $customer_id,
                                                'c.subtype = \'branch\'',
                                                'c.active = 1'),
                              'sort' => array('c.is_main DESC', 'ci18n.name ASC', 'c.id DESC'));
            $branches = Customers_Branches::search($this->registry, $filters);

            foreach ($branches as $key => $branch) {
                $records['branches'][$key]['option_value'] = $branch->get('id');
                $records['branches'][$key]['label'] = ($branch->get('name')) ? $branch->get('name') : '';
                if (empty($records['branch_label'])) {
                    $records['branch_label'] = $branch->getBranchLabels('branch');
                    $records['branch_help_text'] = $branch->getBranchLabels('help_branch');
                }
            }

            if (isset ($records['branches'][0])) {
                $branch_parent_id = $records['branches'][0]['option_value'];

                require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                $filters  = array('sanitize' => true,
                                  'model_lang' => $request->get('model_lang'),
                                  'where' => array ('c.parent_customer = ' . $branch_parent_id,
                                                    'c.subtype = \'contact\'',
                                                    'c.active = 1'),
                                  'sort' => array('c.is_main DESC', 'CONCAT_WS(\' \', ci18n.name, ci18n.lastname) ASC', 'c.id DESC'));
                $contact_persons = Customers_Contactpersons::search($this->registry, $filters);

                foreach ($contact_persons as $key => $contact_person) {
                    $records['contact_person'][$key]['option_value'] = $contact_person->get('id');
                    $records['contact_person'][$key]['label'] = $contact_person->get('name') . ($contact_person->get('lastname') ? (' ' . $contact_person->get('lastname')) : '');
                }
            }
        }

        print json_encode($records);
    }

    /**
     * get customer contact persons (all of them)
     */
    private function _getCustomerContactPersons() {
        $request = &$this->registry['request'];

        $customer_id = $request->get('customer_id');
        $records = array();
        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters  = array('sanitize' => true,
                              'model_lang' => $request->get('model_lang'),
                              'where' => array ('pc.id = ' . $customer_id,
                                                'c.subtype = \'contact\'',
                                                'c.active = 1'),
                              'sort' => array('c.is_main DESC', 'CONCAT_WS(\' \', ci18n.name, ci18n.lastname) ASC', 'c.id DESC'));
            $contact_persons = Customers_Contactpersons::search($this->registry, $filters);

            foreach ($contact_persons as $key => $contact_person) {
                $records['contact_persons'][$key]['option_value'] = $contact_person->get('id');
                $records['contact_persons'][$key]['label'] = $contact_person->get('name') . ($contact_person->get('lastname') ? (' ' . $contact_person->get('lastname')) : '');
            }
        }

        print json_encode($records);
    }

    /**
     * Gets similar names and prints/returns them as HTML (depending on $return param).
     * Method is no longer called from an AJAX request, just internally from other methods.
     *
     * @param boolean $return - if true return the content, otherwise print it
     * @return string|void - fetched content if $return is true, otherwise no value is returned
     */
    private function _checkName($return = false) {
        $request = &$this->registry['request'];

        $check_name = $request->get('name');
        $is_company = $request->get('is_company');
        if (!$is_company) {
            $check_name .= ' ' . $request->get('lastname');
        }

        //get similar names
        $records = Customers::checkName($this->registry, $check_name, $is_company);
        if ($records) {
            $viewer = new Viewer($this->registry);
            $viewer->setFrameset('frameset_blank.html');
            $viewer->setTemplate('_similar_names.html');
            $viewer->data['similar_names'] = $records;
            if ($return == false) {
                print $viewer->fetch();
            }
            else {
                return $viewer->fetch();
            }
        } else {
            //no similar names found
            if ($return == false) {
                 print '';
            } else {
                return false;
            }
        }

        exit;
    }

    /**
     * Transfer customers between different types
     */
    private function _transfer() {

        $registry = &$this->registry;
        $request = &$registry['request'];

        $id = $request->get($this->action);
        $new_type = $request->get('to_type');

        if (!$new_type || !$id) {
            $this->registry['messages']->setError($this->i18n('error_customers_transfer_failed'));
            if (!$id) {
                $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'list');
            }
            if (!$new_type) {
                $this->registry['messages']->setError($this->i18n('error_invalid_type'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', array('view' => $id));
            }
            return;
        }

        $filters = array('where' => array('c.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $customer = Customers::searchOne($this->registry, $filters);

        if ($customer) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);
        }

        require_once $this->modelsDir . 'customers.types.factory.php';
        $filters = array('where' => array('ct.id = ' . $new_type,
                                          'ct.active = 1'),
                         'sanitize' => true);
        $type = Customers_Types::searchOne($this->registry, $filters);

        $transfer_types = array();
        if ($customer) {
            $filters = array('where' => array('ct.id = ' . $customer->get('type'),
                                              'ct.deleted IS NOT NULL'),
                             'sanitize' => true);
            $old_type = Customers_Types::searchOne($this->registry, $filters);
            if ($old_type->get('transfer_types')) {
                $transfer_types = $old_type->get('transfer_types');
            }
        }

        if (empty($customer)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');

        } elseif ($new_type == $customer->get('type')) {
            $this->registry['messages']->setWarning($this->i18n('warning_customers_transfer_same_type'));
            $this->registry['messages']->insertInSession($this->registry);
            //redirect has to be done
            $this->redirect($this->module, 'view', array('view' => $customer->get('id')));

        } elseif (empty($type) || !in_array($new_type, $transfer_types) ||
        empty($old_type) || $type->get('counter') != $old_type->get('counter') ||
        ($type->get('kind') == 'person' && $customer->get('is_company') ||
        $type->get('kind') == 'company' && !$customer->get('is_company'))) {
            //invalid type, redirect to view
            $this->registry['messages']->setError($this->i18n('error_customers_transfer_failed'));
            $this->registry['messages']->setError($this->i18n('error_invalid_type'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'view', array('view' => $customer->get('id')));
        }

        $old_customer = clone $customer;
        $old_customer->sanitize();
        $this->old_model = $old_customer;

        if ($customer->transfer()) {
            $filters = array('where' => array('c.id = ' . $customer->get('id')),
                             'skip_permissions_check' => true,
                             'model_lang' => $request->get('model_lang'));
            $new_customer = Customers::searchOne($this->registry, $filters);
            $this->model = $new_customer;

            Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'transfer', 'new_model' => $new_customer, 'old_model' => $old_customer));

            $this->registry['messages']->setMessage($this->i18n('customers_transfer_success'));
            $this->registry['messages']->insertInSession($this->registry);

            if (isset($_SERVER['HTTP_REFERER'])) {
                $url = preg_replace('#\&customers=.*$#', '', $_SERVER['HTTP_REFERER']);
            } else {
                $url = sprintf('%s?%s=customers', $_SERVER['PHP_SELF'], $this->registry['module_param']);
            }
            $url .= sprintf('&customers=view&view=%d&model_lang=%s', $customer->get('id'), $customer->get('model_lang'));
            $this->registry->set('redirect_to_url', $url, true);
            $this->actionCompleted = true;
        }

        return;
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _print() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST ? $request->get('id') : $request->get('print'));
        $filters = array('where' => array('c.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $customer = Customers::searchOne($this->registry, $filters);

        if (!empty($customer)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($customer);

            if (!$request->get('pattern')) {
                //show error no such model
                $this->registry['messages']->setError($this->i18n('error_customers_print_no_default_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'list');
            }

            //get the pattern
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $request->get('pattern')),
                             'sanitize' => true,
                             'model_lang' => $request->get('model_lang'));
            $pattern = Patterns::searchOne($this->registry, $filters);

            if (!empty($pattern)) {
                $patterns_vars = $customer->getPatternsVars();
                $customer->extender = new Extender();
                $customer->extender->model_lang = $customer->get('model_lang');
                $customer->extender->module = $this->module;
                foreach ($patterns_vars as $key => $value) {
                    $customer->extender->add($key, $value);
                }

                if ($pattern->get('force_generate')) {
                    //generate and save file and get its id
                    $this->old_model = clone $customer;

                    if (preg_match('#^(xls|xlsx)$#', $pattern->get('format'))) {
                        $result = $customer->generateXLS();
                    } elseif (preg_match('#^(docx|docx2pdf)$#', $pattern->get('format'))) {
                        $result = $customer->generateDOCX();
                    } else {
                        $result = $customer->generatePDF();
                    }
                    if ($result) {
                        $customer->set('file_id', $result, true);
                        if (!$this->registry->isRegistered('customer')) {
                            $this->registry->set('customer', $customer->sanitize());
                        }

                        //save history
                        require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                        Customers_History::saveData($this->registry, array('model' => $customer,
                                                                           'action_type' => 'print',
                                                                           'pattern' => $pattern->get('id'),
                                                                           'generated_file' => $result));

                        // show the file
                        require_once PH_MODULES_DIR . 'files/models/files.factory.php';

                        $file_id = $request->get('file');
                        $filters = array('where'    => array('f.id = ' . $result),
                                         'sanitize' => true);
                        $file = Files::searchOne($this->registry, $filters);

                        $file->viewFile();
                        exit;
                    }
                } else {
                    //save history
                    require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                    Customers_History::saveData($this->registry, array('model' => $customer,
                                                                       'action_type' => 'print',
                                                                       'pattern' => $pattern->get('id'),
                                                                       'generated_file' => false));

                    if (preg_match('#^(xls|xlsx)$#', $pattern->get('format'))) {
                        //generate file to the browser window
                        $result = $customer->generateXLS('browser_mode');
                    } elseif (preg_match('#^(docx|docx2pdf)$#', $pattern->get('format'))) {
                        //generate file to the browser window
                        $result = $customer->generateDOCX('browser_mode');
                    } else {
                        //generate file to the browser window
                        $result = $customer->generatePDF('browser_mode');
                    }
                }
            } else {
                $result = false;
            }

            if ($result) {
                //the content is displayed in the browser window
                exit;
            } else {
                $this->registry['messages']->setError($this->i18n('error_customers_print_document'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'view', array('view' => $customer->get('id')));
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_customer'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates pseudo-merged file for multiple models using specified pattern, header and footer
     *
     * @param mixed $ids Array of ids or crypted string with array of ids
     *                  or crypted string containing from_id and to_id
     */
    private function _multiPrint($ids = '') {
        $request = &$this->registry['request'];

        if (isset($_SERVER['HTTP_REFERER'])) {
            preg_match('/&customers=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
        }
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'list';
        }

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        if (!is_array($ids)) {
            Customers::decryptIdsMultiprint($this->registry, $ids);
            //set to request as decrypted array
            $request->set('items', $ids, 'all', true);
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $customers = array();
        if ($ids) {
            $filters = array('where' => array('c.id IN (' . implode(', ', $ids) . ')'),
                             'model_lang' => $this->registry->get('lang'),
                             'sanitize' => true);
            $customers = Customers::search($this->registry, $filters);
        }

        //if no models
        if (empty($customers) || count($ids) != count($customers)) {
            $this->registry['messages']->setError($this->i18n('error_no_customers_or_deleted'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, $after_action);
        }

        $type = $customers[0]->get('type');
        $is_company = $customers[0]->get('is_company');
        foreach ($customers as $customer) {
            $type_i = $customer->get('type');
            $is_company_i = $customer->get('is_company');
            //different type or companies and persons
            if ($is_company != $is_company_i || $type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_different_types_or_companies'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, $after_action);
                return true;
            }
        }

        require_once $this->modelsDir . 'customers.types.factory.php';
        $filters = array('where' => array('ct.id = ' . $type),
                         'sanitize' => true);
        $cust_type = Customers_Types::searchOne($this->registry, $filters);
        $type_name_plural = $cust_type && $cust_type->get('name_plural') ? $cust_type->get('name_plural') : $this->i18n('customers');

        //get the specified pattern
        $pattern_id = $request->get('pattern');
        if (empty($pattern_id)) {
            $this->registry['messages']->setError($this->i18n('error_customers_multiprint_failed', array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_customers_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        //get the pattern
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('p.id = ' . $pattern_id,
                                          'p.active = 1',
                                          'p.format = "pdf"'),
                         'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
            $this->registry['messages']->setError($this->i18n('error_customers_multiprint_failed', array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_customers_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        $result = Customers::multiPrint($this->registry, $this);
        if ($result) {
            //the pdf content is displayed in the browser window
            exit;
        } else {
            $this->registry['messages']->setError($this->i18n('error_customers_multiprint_failed', array($type_name_plural)), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
        }

        $this->redirect($this->module, $after_action);
    }

    /**
     * Manage Frankenstein configurator (obsolete).
     * Also manage (load/save/delete) saved configurations for a configurator.
     */
    private function _franky() {
        $request = &$this->registry['request'];
        $id = $request->get('id');

        // make a guess whether id is a model id or a temporary id for a model
        // that is not added yet
        if ($id && General::guessIfRealModelId($this->registry, $id)) {
            $filters = array('where' => array('c.id = \'' . $id . '\''),
                             'model_lang' => $request->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
        }
        if (empty($customer)) {
            $customer = Customers::buildModel($this->registry);
            $customer->set('id', $request->get('id', 'get'), true);
            if ($request->get('type')) {
                $customer->set('type', $request->get('type'), true);
            } elseif ($request->get('edit_id')) {
                $type = Customers::getConfiguratorModelType($this->registry, $request->get('edit_id'));
                $customer->set('type', $type, true);
            }
        }

        $customer->getVarsForTemplate(false);
        $this->registry->set('customer', $customer->sanitize(), true);

        require_once PH_MODULES_DIR . 'configurators/models/configurators.factory.php';
        require_once PH_MODULES_DIR . 'configurators/viewers/franky.viewer.php';
        $this->viewer = new Franky_Viewer($this->registry);
        $this->viewer->model = $customer;

        $configurator = Configurators::buildModel($this->registry, '');
        if ($request->get('edit_id') > 0) {
            // load
            $this->registry['configData'] =
                $configurator->getConfigForTemplate(array('id' => $request->get('edit_id')));
        } else {
            // delete or save
            if ($request->get('del_id') > 0) {
                $configurator->config_delete($request->get('del_id'));
            } else {
                if ($request->get('id', 'get')) {
                    $configurator->saveFranky();
                } else {
                    $configurator->save();
                }
            }
            if (($request->get('del_id') || $request->get('config_id')) && !$request->get('id', 'get')) {
                $this->viewer->data['configPatterns'] =
                    $configurator->getConfigPatterns(
                        array(
                            'model' => $customer->modelName,
                            'model_type' => $customer->get('type'),
                            'model_id' => 0,
                            'config_num' => $request->get('config_num')
                        ));
            }

            if ($request->get('id', 'get')) {
                $customer->getVarsForTemplate(false);
                $this->registry->set('customer', $customer->sanitize(), true);
            }
        }

        return true;
    }

    /**
     * Manage bb (add/clone/edit/save/delete row)
     */
    private function _bb() {
        $request = &$this->registry['request'];
        $id = $request->get('id');              // id of model
        $meta_id = $request->get('meta_id');    // id of variable of type 'config', 'group' or 'gt2' that the bb row contains
        $bb_id = $request->get('bb_id');        // id of data row in `bb` table
        $bb_action = $request->get('bb_action');// current action performed with the bb row
        $bb_num = $request->get('bb_num');      // 'bb' field in `_fields_meta` of variables from bb (usually 1)
        // make a guess whether id is a model id or a temporary id for a model
        // that is not added yet
        if (General::guessIfRealModelId($this->registry, $id)) {
            $filters = array('where' => array('c.id = \'' . $id . '\''),
                             'model_lang' => $request->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
        }
        // build model from request
        if (empty($customer)) {
            $customer = Customers::buildModel($this->registry);
            if (!$id) {
                $id = $request->get('id', 'get');
            }
            $customer->set('id', $id, true);
            // only if variable is available in request and has value (other than 0)
            if ($bb_id) {
                $customer->set('type', Customers::getBBModelType($this->registry, $bb_id), true);
            }
        }
        $customer->getVarsForTemplate(false);
        $vars = $customer->get('vars');

        // get inner compound variable (prepared for display)
        $inner_var = array();
        if ($meta_id) {
            $meta_info = $customer->getMetaInfo($meta_id);
            foreach ($vars as $var) {
                if (isset($var['id']) && $var['id'] == $meta_id) {
                    $inner_var = $var;
                    break;
                }
            }
        }

        // the requested compound variable was not found, there is some error
        if (empty($inner_var)) {
            $bb_action = 'error';
        }

        /**
         * Check if bb element has file upload controls (in plain vars or in inner variable)
         *
         * @param array $vars - array of all additional variables of model
         * @param array $var - compound additional variable in a bb row
         * @return int - number of found file upload controls
         */
        $bbWithFileUpload = function(&$vars, &$var) {
            $bb_with_file_upload = 0;

            foreach ($vars as $var_bb) {
                if ($var_bb['type'] == 'bb') {
                    foreach ($var_bb['types'] as $var_bb_type) {
                        if ($var_bb_type == 'file_upload') {
                            $bb_with_file_upload++;
                        }
                    }
                }
            }

            if (isset($var['types'])) {
                foreach ($var['types'] as $var_type) {
                    if ($var_type == 'file_upload') {
                        $bb_with_file_upload++;
                    }
                }
            } else if ($var['type'] == 'gt2' && !empty($var['vars'])) {
                foreach ($var['vars'] as $var_info) {
                    if ($var_info['type'] == 'file_upload') {
                        $bb_with_file_upload++;
                    }
                }
            }

            return $bb_with_file_upload;
        };

        // perform selected action
        switch ($bb_action) {
            case 'edit':
                if ($bb_id) {
                    $bb_var = $customer->getBB(array('model_id' => $customer->get('id'), 'bb_id' => $bb_id));

                    if ($bb_var) {
                        $bb_var = reset($bb_var);
                        $inner_var['meta_id'] = $bb_var['meta_id'];
                        $inner_var['model_id'] = $bb_var['model_id'];
                        $inner_var['id'] = $bb_var['id'];
                        $customer->prepareBbVarValues($inner_var, $bb_var['params']);
                    } else {
                        $bb_action = 'error';
                    }
                } else {
                    $bb_action = 'error';
                }
                break;
            case 'add':
                $data = array();
                if ($meta_info['type'] == 'gt2') {
                    $data['values'] = $data['plain_values'] = $data['rows'] = array();
                }
                $params = array();
                $params['data'] = $data;
                $params['bb_id'] = $bb_id;
                $params['bb_num'] = $bb_num;
                $params['meta_id'] = $inner_var['id'];

                $bb_id = $customer->saveBB($params);
                if (!$bb_id) {
                    $bb_action = 'error';
                }
                break;
            case 'clone':
                $bb_id = $customer->cloneBBRow(array('bb_id' => $bb_id, 'var' => &$inner_var));
                if (!$bb_id) {
                    $bb_action = 'error';
                }
                break;
            case 'save':
                $customer->getVars();
                $all_vars = $customer->get('vars');
                if ($meta_info['type'] == 'gt2') {
                    $gt2_vals = array();
                    $gt2_rows = array();
                    $deleted = $request->get('deleted');
                    $delete_file_ids = array();
                    foreach ($inner_var['vars'] as $var_name => $v_var) {
                        if ($v_var['type'] == 'file_upload') {
                            if (!empty($_FILES) && isset($_FILES[$var_name])) {
                                $files_values = $_FILES[$var_name];
                            } else {
                                $files_values['tmp_name'] = array();
                            }

                            if ($request->get('dbid_' . $var_name)) {
                                $post_values = $request->get('dbid_' . $var_name);
                            } else {
                                $post_values = array();
                            }

                            $values_array = array();

                            // files which have to be deleted
                            if ($request->get('deleteid_' . $var_name)) {
                                foreach ($request->get('deleteid_' . $var_name) as $key_v => $file_id_encrypted) {
                                    if ($file_id_encrypted) {
                                        $file_id = General::decrypt($file_id_encrypted, '_delete_file_', 'xtea');
                                        $delete_file_ids[] = $file_id;
                                        $values_array[$key_v] = '';
                                    } else {
                                        $values_array[$key_v] = '';
                                    }
                                }
                            }

                            foreach ($files_values['tmp_name'] as $key_fv => $file_value) {
                                $values_array[$key_fv] = '';
                            }

                            foreach ($post_values as $pv_key => $post_value) {
                                $values_array[$pv_key] = $post_value;
                            }
                            // keys are negative so sort in reverse order
                            krsort($values_array);

                            foreach ($values_array as $r_idx => $r_val) {
                                if (isset($deleted[$r_idx]) && $deleted[$r_idx]) {
                                    unset($gt2_vals['values'][$r_idx]);
                                } else {
                                    $gt2_vals['values'][$r_idx][$var_name] = $r_val;
                                    if (!in_array($r_idx, $gt2_rows)) {
                                        $gt2_rows[] = $r_idx;
                                        $gt2_vals['values'][$r_idx]['id'] = $r_idx;
                                    }
                                }
                            }
                        } else {
                            $vals = $request->get($var_name);
                            if ($vals) {
                                foreach ($vals as $key => $val) {
                                    if (isset($deleted[$key]) && $deleted[$key]) {
                                        unset($gt2_vals['values'][$key]);
                                    } else {
                                        $gt2_vals['values'][$key][$var_name] = $val;
                                        if (!in_array($key, $gt2_rows)) {
                                            $gt2_rows[] = $key;
                                            $gt2_vals['values'][$key]['id'] = $key;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // delete old files
                    require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                    if (!empty($delete_file_ids)) {
                        $del_ids = $delete_file_ids;
                        if (!is_array($del_ids)) {
                            $del_ids = array($del_ids);
                        }
                        Files::delete($this->registry, $del_ids);
                    }

                    $gt2_vals['rows'] = $gt2_rows;
                    foreach ($inner_var['plain_vars'] as $var_name => $v_var) {
                        $gt2_vals['plain_values'][$var_name] = $request->get($var_name);
                    }

                    $gt2_validate = $gt2_vals;
                    $gt2_validate['vars'] = $inner_var['vars'];
                    $gt2_validate['calculated_price'] = $inner_var['calculated_price'];
                    $gt2_validate = $customer->validateGT2(array(), $gt2_validate);
                    // get GT2 after validation
                    unset($gt2_validate['vars']);
                    unset($gt2_validate['calculated_price']);
                    $gt2_vals = $gt2_validate;

                    // reindex 'rows' array and keys of 'values' array of GT2
                    // indexes will go -1, -2, -3 etc. without gaps
                    $reindexed_values = array();
                    // get keys from 'values' array because empty rows have been unset
                    $gt2_rows = array_keys($gt2_vals['values']);
                    foreach ($gt2_rows as $idx => $key) {
                        $gt2_rows[$idx] = -($idx+1);
                        $reindexed_values[$gt2_rows[$idx]] = $gt2_vals['values'][$key];
                    }
                    $gt2_vals['rows'] = $gt2_rows;
                    $gt2_vals['values'] = $reindexed_values;
                }
                $customer->set('vars', $all_vars, true);

                //validate values
                if (!$customer->validateVars(true) || !empty($gt2_validate['has_error'])) {
                    $bb_action = 'error';
                } else {
                    $data = array();
                    if ($meta_info['type'] == 'gt2') {
                        $data = $gt2_vals;
                    } else {
                        foreach ($inner_var['names'] as $key_name => $name) {
                            $default_value = $meta_info['type'] == 'group' || $inner_var['types'][$key_name] == 'checkbox_group' ? array() : '';
                            if ($inner_var['types'][$key_name] == 'file_upload') {
                                $data[$name] = $request->get('dbid_' . $name) ?: $default_value;
                            } else {
                                $data[$name] = $request->get($name) ?: $default_value;
                            }
                        }
                    }
                    $params = array();
                    $params['data'] = $data;
                    $params['bb_id'] = $bb_id;
                    $params['bb_num'] = $bb_num;
                    $params['meta_id'] = $inner_var['id'];
                    $params['var'] = &$inner_var;
                    if (!$customer->saveBB($params)) {
                        $bb_action = 'error';
                    }
                }
                break;
            case 'del':
                $params = array();
                $params['bb_id'] = $bb_id;
                $params['bb_num'] = $inner_var['bb'];
                $params['model_id'] = $id;
                if (!$customer->delBB($params)) {
                    $bb_action = 'error';
                }
                break;
            default:
                break;
        }

        // prepare response to be returned and displayed
        switch ($bb_action) {
            case 'error':
                // an error has occurred, return error messages
                if (!$this->registry['messages']->getErrors()) {
                    $this->registry['messages']->setError($this->i18n('error_bb_action'));
                }
                print $this->getMessagesForAJAX('error');
                break;
            case 'edit':
                // return selected bb row in edit mode
                $bbViewer = new Viewer($this->registry);
                $bbViewer->setFrameset('_bb_edit.html');
                $bbViewer->data['var'] = $inner_var;
                $bbViewer->data['model_id'] = $id;
                // model of the calc variable is other than model name
                $bbViewer->data['calc_meta_id'] = $customer->getMetaId($inner_var['name'] . '_calc');
                // check if the bb has file upload controls in it
                $bbViewer->data['bb_with_file_upload'] = $bbWithFileUpload($vars, $inner_var);

                // prepare saved configurations
                if ($inner_var['type'] == 'grouping') {
                    require_once PH_MODULES_DIR . 'configurators/models/configuratorsgroup.factory.php';
                    $configurator_group = ConfiguratorsGroups::buildModel($this->registry, '');
                    $bbViewer->data['configGroupPatterns'] =
                    $configurator_group->getConfigGroupPatterns(
                        array(
                            'model' => $customer->modelName,
                            'model_type' => $customer->get('type'),
                            'config_group_num' => $inner_var['grouping']
                        ));
                    unset($configurator_group);
                } elseif ($inner_var['type'] == 'config') {
                    require_once PH_MODULES_DIR . 'configurators/models/configurators.factory.php';
                    $configurator = Configurators::buildModel($this->registry, '');
                    $bbViewer->data['configPatterns'] =
                    $configurator->getConfigPatterns(
                        array(
                            'model' => $customer->modelName,
                            'model_type' => $customer->get('type'),
                            'model_id' => 0,
                            'config_num' => $inner_var['config']
                        ));
                    unset($configurator);
                }

                print $bbViewer->fetch();
                break;
            default:
                // return the whole bb table
                // remove inner bb vars from model vars
                $bb_elements = $bb_var = array();
                foreach ($vars as $key => $var) {
                    if (!empty($var['bb']) && in_array($var['type'], array('grouping', 'config', 'gt2'))) {
                        $bb_elements[$var['id']] = $var;
                        unset($vars[$key]);
                    } elseif ($var['type'] == 'bb') {
                        $bb_var = $var;
                    }
                }
                $customer->set('vars', $vars, true);

                // inner bb variables and data
                $bb_vars = $customer->getBB(array('model_id' => $customer->get('id')));
                foreach ($bb_vars as $index => $var) {
                    $bb_vars[$index] = $bb_elements[$var['meta_id']];
                    $bb_vars[$index]['id'] = $var['id'];
                    $bb_vars[$index]['meta_id'] = $var['meta_id'];
                    $bb_vars[$index]['model_id'] = $var['model_id'];
                    $customer->prepareBbVarValues($bb_vars[$index], $var['params']);
                }
                $customer->set('bb_vars', $bb_vars, true);

                // bb variables and data for caption rows
                $bb_fields_params = array('mode' => 'request');
                if (in_array($bb_action, array('add', 'clone'))) {
                    // specify that data for the row that was just added should be taken from db
                    $bb_fields_params['added_bb_id'] = $bb_id;
                }
                $customer->set('add_bb_vars', $customer->getBBFields($bb_fields_params), true);

                $bbViewer = new Viewer($this->registry);
                $bbViewer->setFrameset('_bb_list.html');
                $bbViewer->model = $customer;
                if ($bb_action != 'del') {
                    // highlight current row
                    $bbViewer->data['bb_id'] = $bb_id;
                }
                $bbViewer->data['model_id'] = $id;
                $bbViewer->data['var'] = $bb_var;

                // if current bb row should be displayed in edit mode
                if (in_array($bb_action, array('add'))) {
                    $bbViewer->data['edit_row'] = $bb_id;
                    // model of the calc variable is other than model name
                    $bbViewer->data['calc_meta_id'] = $customer->getMetaId($meta_info['name'] . '_calc');
                    // check if the bb has file upload controls in it
                    $bbViewer->data['bb_with_file_upload'] = $bbWithFileUpload($vars, $inner_var);

                    // prepare saved configurations
                    if ($meta_info['type'] == 'group') {
                        require_once PH_MODULES_DIR . 'configurators/models/configuratorsgroup.factory.php';
                        $configurator_group = ConfiguratorsGroups::buildModel($this->registry, '');
                        $bbViewer->data['configGroupPatterns'] =
                            $configurator_group->getConfigGroupPatterns(
                                array(
                                    'model' => $customer->modelName,
                                    'model_type' => $customer->get('type'),
                                    'config_group_num' => $meta_info['grouping']
                                ));
                        unset($configurator_group);
                    } elseif ($meta_info['type'] == 'config') {
                        require_once PH_MODULES_DIR . 'configurators/models/configurators.factory.php';
                        $configurator = Configurators::buildModel($this->registry, '');
                        $bbViewer->data['configPatterns'] =
                            $configurator->getConfigPatterns(
                                array(
                                    'model' => $customer->modelName,
                                    'model_type' => $customer->get('type'),
                                    'model_id' => 0,
                                    'config_num' => $meta_info['configurator']
                                ));
                        unset($configurator);
                    }
                }

                print $bbViewer->fetch();
                break;
        }

        exit;
    }

    /**
     * Manage (load/save/delete) saved configurations for a grouping table
     */
    private function _saveGroupVar() {
        $request = &$this->registry['request'];
        $id = $request->get('id');

        // make a guess whether id is a model id or a temporary id for a model
        // that is not added yet
        if ($id && General::guessIfRealModelId($this->registry, $id)) {
            $filters = array('where' => array('c.id = \'' . $id . '\''),
                             'model_lang' => $request->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
        }
        if (empty($customer)) {
            if ($request->get('type')) {
                $customer = new Customer($this->registry);
                $customer->set('id', $request->get('id', 'get'), true);
                $customer->set('type', $request->get('type'), true);
            } else {
                exit;
            }
        }

        $customer->getVarsForTemplate(false);
        $this->registry->set('customer', $customer->sanitize(), true);

        require_once PH_MODULES_DIR . 'configurators/models/configuratorsgroup.factory.php';
        require_once PH_MODULES_DIR . 'configurators/viewers/savegroupvar.viewer.php';
        $this->viewer = new SaveGroupVar_Viewer($this->registry);
        $this->viewer->model = $customer;

        $configurator_group = ConfiguratorsGroups::buildModel($this->registry, '');
        if ($request->get('edit_id') > 0) {
            // load
            $this->registry['configGroupData'] =
                $configurator_group->getConfigGroupForTemplate(array('id' => $request->get('edit_id')));
        } else {
            // delete or save
            if ($request->get('del_id') > 0) {
                $configurator_group->configGroupDelete($request->get('del_id'));
            } else {
                $configurator_group->save();
            }
            $this->viewer->data['configGroupPatterns'] =
                $configurator_group->getConfigGroupPatterns(
                    array(
                        'model' => $customer->modelName,
                        'model_type' => $customer->get('type'),
                        'config_group_num' => $request->get('group_num')
                    ));
        }

        return true;
    }

    /**
     * attach a file as additional field using ajax
     */
    private function _attachAdditionalFieldFile() {
        $request = &$this->registry['request'];
        $field_source = $request->get('field_source');

        $result = array();
        $errors = array();
        if ($_FILES && !empty($_FILES)) {
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            $id = $request->get('id');
            // make a guess whether id is a model id or a temporary id for a model
            // that is not added yet
            if (General::guessIfRealModelId($this->registry, $id)) {
                $filters = array('where' => array('c.id = \'' . $id . '\''),
                                 'model_lang' => $request->get('model_lang'));
                $customer = Customers::searchOne($this->registry, $filters);
            }
            // build model from request
            if (empty($customer)) {
                $customer = Customers::buildModel($this->registry);
                if (!$id) {
                    $id = $request->get('id', 'get');
                }
                $customer->set('id', $id, true);
                $customer->set('added', General::strftime($this->i18n('date_iso')), true);
            }

            $customer->getVarsForTemplate(false);
            $this->registry->set('customer', $customer, true);

            $doc_vars = $customer->get('vars');

            //get only data for config num table
            $data = array();

            foreach ($doc_vars as $key => $var) {
                if ($var['type'] == 'config' && ($var['id'] == $request->get('meta_id') || ($var['config'] == $request->get('config_num')))) {
                    foreach ($var['names'] as $idx => $var_name) {
                        if (isset($_FILES[$var_name]) && (!empty($_FILES[$var_name]['tmp_name']))) {
                            $current_file = $_FILES[$var_name];
                            $params = array(
                                'name'        => $current_file['name'],
                                'description' => '',
                                'revision'    => '',
                                'permission'  => 'all'
                            );

                            $restrictions = array();
                            if (!empty($var[$var_name]['source'])) {
                                $restrictions = General::parseSettings($var[$var_name]['source']);
                                if (!preg_match('#image#', $current_file['type'])) {
                                    $restrictions = array_diff_key($restrictions, array_flip(array('max_width', 'max_height', 'thumb_width', 'thumb_height', 'view_mode')));
                                }
                            }

                            $operation_result = Files::attachFile($this->registry, $current_file, $params, $customer, $restrictions);

                            if ($operation_result) {
                                // find the last uploaded id and sets it as a value
                                $result[$var_name] = $customer->getLatestAttachment();
                            } else {
                                $errors[$var_name] = $customer->raiseError('warning_attachment_not_uploaded', $var_name, null, array($current_file['name'], $var['labels'][$idx]));
                            }
                        }
                    }
                    break;
                } elseif ($var['type'] == 'grouping' && $var['id'] == $request->get('meta_id') && !empty($var['types'])) {
                    foreach ($var['types'] as $type_num => $var_type) {
                        $var_name = $var['names'][$type_num];
                        if ($var_type == 'file_upload') {
                            foreach ($var['values'] as $row_num => $row_values) {
                                if (isset($row_values[$type_num]) && is_array($row_values[$type_num])) {
                                    $current_file = $row_values[$type_num];

                                    $params = array(
                                        'name'        => $current_file['name'],
                                        'description' => '',
                                        'revision'    => '',
                                        'permission'  => 'all'
                                    );

                                    $restrictions = array();
                                    if (!empty($var[$var_name]['source'])) {
                                        $restrictions = General::parseSettings($var[$var_name]['source']);
                                        if (!preg_match('#image#', $current_file['type'])) {
                                            $restrictions = array_diff_key($restrictions, array_flip(array('max_width', 'max_height', 'thumb_width', 'thumb_height', 'view_mode')));
                                        }
                                    }

                                    $operation_result = Files::attachFile($this->registry, $current_file, $params, $customer, $restrictions);

                                    if ($operation_result) {
                                        // find the last uploaded id and sets it as a value
                                        $result[$var_name . '_' . $row_num] = $customer->getLatestAttachment();
                                    } else {
                                        $errors[$var_name . '_' . $row_num] = $customer->raiseError('warning_attachment_not_uploaded', $var_name . '_' . $row_num, null, array($current_file['name'], $var['labels'][$type_num]));
                                    }
                                }
                            }
                        }
                    }
                    break;
                } elseif ($var['type'] == 'bb') {
                    foreach ($var['types'] as $key_row => $var_type) {
                        if ($var_type == 'file_upload') {
                            $var_name = $var['names'][$key_row];
                            foreach ($var['values'] as $key_bb_val => $bb_val) {
                                if (isset($bb_val[$key_row]) && is_array($bb_val[$key_row])) {
                                    $current_file = $bb_val[$key_row];
                                    $params = array(
                                        'name'        => $current_file['name'],
                                        'description' => '',
                                        'revision'    => '',
                                        'permission'  => 'all'
                                    );

                                    $restrictions = array();

                                    $operation_result = Files::attachFile($this->registry, $current_file, $params, $customer, $restrictions);

                                    if ($operation_result) {
                                        // find the last uploaded id and sets it as a value
                                        $result[$var_name . '_' . ($key_bb_val+1)] = $customer->getLatestAttachment();
                                    } else {
                                        $errors[$var_name . '_' . ($key_bb_val+1)] = $customer->raiseError('warning_attachment_not_uploaded', $var_name . '_' . ($key_bb_val+1), null, array($current_file['name'], $var['labels'][$key_row]));
                                    }
                                }
                            }
                        }
                    }
                } elseif ($var['type'] == 'gt2' && ($var['id'] == $request->get('meta_id'))) {
                    foreach ($var['vars'] as $var_name => $var_info) {
                        if ($var_info['type'] == 'file_upload') {

                            foreach ($var['values'] as $row_num => $row_values) {
                                if (isset($row_values[$var_name]) && is_array($row_values[$var_name])) {

                                    // row is deleted
                                    if (!empty($row_values['deleted'])) {
                                        continue;
                                    }

                                    $current_file = $row_values[$var_name]['file_data'];
                                    $params = $row_values[$var_name]['params'];

                                    $restrictions = array();
                                    if (!empty($var['vars'][$var_name]['source'])) {
                                        $restrictions = General::parseSettings($var['vars'][$var_name]['source']);
                                        // checks the type of the file
                                        if (!preg_match('#image#', $current_file['type'])) {
                                            $restrictions = array_diff_key($restrictions, array_flip(array('max_width', 'max_height', 'thumb_width', 'thumb_height', 'view_mode')));
                                        }
                                    }

                                    $operation_result = Files::attachFile($this->registry, $current_file, $params, $customer, $restrictions);

                                    // IMPORTANT: GT2 rows in BB now have negative row indexes
                                    // because they are not real GT2 rows (saved in gt2_details).
                                    // Row could have old (positive: 0, 1, 2 etc.) or new (negative: -1, -2, -3)
                                    // indexation so try to define upload field suffix correctly
                                    $row_num = $row_num >= 0 ? $row_num + 1 : abs($row_num);
                                    if ($operation_result) {
                                        // find the last uploaded id and sets it as a value
                                        $result[$var_name . '_' . $row_num] = $customer->getLatestAttachment();
                                    } else {
                                        $errors[$var_name . '_' . $row_num] = $customer->raiseError('warning_attachment_not_uploaded', $var_name . '_' . $row_num, null, array($current_file['name'], $var['vars'][$var_name]['label']));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && empty($_POST)) {
            // $_FILES and $_POST are empty because file size exceeds max allowed size for application/server
            $errors[] = $this->registry['messages']->setError(
                $this->i18n(
                'error_file_greater_filesize',
                array(General::convertFileSize(General::convertBytes(ini_get('upload_max_filesize'))))
            ));
        }

        if (!empty(FilesLib::$_errors)) {
            $errors = array_merge($errors, FilesLib::$_errors);
            foreach(FilesLib::$_errors as $error) {
                $this->registry['messages']->setError($error);
            }
        }

        $data['module'] = $request->get($this->registry['module_param']);
        $data['files'] = $result;
        $data['errors'] = !empty($errors) ? $this->getMessagesForAJAX('error') : '';
        $data['form_id'] = $request->get('form_id');
        $data['div_id'] = $request->get('div_id');
        $data['model_id'] = $request->get('id') ?: $request->get('id', 'get');
        $data['field_source'] = $request->get('field_source');

        if ($field_source == 'franky') {
            $data['config_id'] = $request->get('config_id');
            $data['config_num'] = $request->get('config_num');
        } else if ($field_source == 'bb') {
            $data['bb_id'] = $request->get('bb_id');
            $data['bb_num'] = $request->get('bb_num');
            $data['meta_id'] = $request->get('meta_id');
        }

        $html_content = '<script type="text/javascript">' . "\n" .
                        '    data=' . json_encode($data) . ';' . "\n" .
                        '    window.parent.completeData(data);' . "\n" .
                        '</script>';

        print($html_content);
        exit;
    }

    /**
     * Gets all e-mails of customer + branches + contact persons and prints
     * them as a json-encoded array.
     */
    private function _getAllEmails() {

        $filters = array('sanitize' => true,
                         'where' => array('c.id = ' . $this->registry['request']->get($this->action),
                                          'c.deleted IS NOT NULL'));
        $customer = Customers::searchOne($this->registry, $filters);

        if ($customer) {
            // flags that modify behaviour of method
            foreach (array('for_campaign', 'financial_persons') as $flag) {
                if ($this->registry['request']->get($flag)) {
                    $customer->set($flag, $this->registry['request']->get($flag), true);
                }
            }

            $optgroups = $customer->getAllEmails();
            if (!empty($optgroups)) {
                echo json_encode($optgroups);
            } else {
                echo "''";
            }
        } else {
            echo "''";
        }
        die;
    }

    /**
     * Recursive function to clear customers without mails (used for special type of autocompleter)
     */
    private function clearCustomersWithoutMails($results = array()) {
        foreach ($results as $key => $res) {
            if (isset($res['parent_customer_of'])) {
                $results[$key]['parent_customer_of'] = $this->clearCustomersWithoutMails($res['parent_customer_of']);
            }
            if (empty($results[$key]['emails']) && empty($results[$key]['parent_customer_of'])) {
                unset($results[$key]);
            }
        }

        return $results;
    }
}

?>
