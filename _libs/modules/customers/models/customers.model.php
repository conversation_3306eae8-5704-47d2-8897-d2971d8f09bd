<?php

require_once 'customers.validator.php';
require_once 'customers.dropdown.php';

/**
 * Customers model class
 */
Class Customer extends Model {
    public $modelName = 'Customer';

    public $counter;

    /**
     * Contact parameters saved in the DB as arrays
     */
    public $contactParameters = array('phone', 'fax', 'gsm', 'email', 'web', 'skype', 'othercontact');

    /**
     * Contact parameters saved for companies reciprocally set when either company is saved, or its main branch
     */
    public $branchInheritedProperties = array('phone', 'fax', 'gsm', 'email', 'web', 'skype', 'othercontact',
                                              'address', 'city', 'postal_code', 'country');

    /**
     * Contact parameters to display in add mode
     */
    public $predefinedContactParameters = array('phone', 'phone', 'fax', 'gsm', 'email', 'web');

    /**
     * Placeholders used by the generate and print output filename
     */
    public $outputFileNamePlaceholders = array('name', 'added', 'modified', 'current_date', 'rev');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        if ($this->origin == 'database') {
            $this->getContacts();
            //IMPORTANT: remove some of the properties when the model is fetched from the DB
            //these properties are checked in validate method when trying to save the model built from the database
            if ($this->get('is_company')) {
                $this->unsetProperty('ucn');
            } else {
                $this->unsetProperty('eik');
            }
        } elseif ($this->origin == 'request') {
            $this->getPostContacts();
        }

        //get contact person name
        if ($registry->get('getContactPersonInfo')) {
            $this->getPersonInfo();
        }

    }

    /**
     * Checks permissions for certain action
     *
     * @param array $action - action name
     * @param array $module_check - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     */
    public function checkPermissions($action, $module_check = 'customers', $force = false) {

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        // checks if the current customer model is for company and if not
        //doesn't allow the users to open Branches and Contact Persons menu
        if (($action == 'branches' || $action == 'contactpersons') && !$this->get('is_company')) {
            return false;
        }

        $rights = $this->setPermissions(array(), $force, $module_check);

        if (!isset($rights[$action]) && is_array($rights)) {
            //the action is not defined within the rights array
            $action_defs = array_keys($rights);
            $action_defs[] = $action;

            //try to get permission definition for this action
            $rights = $this->setPermissions($action_defs, true, $module_check);
        }

        if ($this->get('type_rights')) {
            $type_rights = $this->get('type_rights');
        } elseif ($this->get('type')) {
            $sanitize_after = false;
            if (empty($this->registry)) {
                $this->unsanitize();
                $sanitize_after = true;
            }
            $user_permissions = $this->registry['currentUser']->getRights();
            $type_rights = $this->setPermissions(@array_keys($user_permissions[$module_check . $this->get('type')]), true, $module_check . $this->get('type'));
            if ($sanitize_after) {
                $this->sanitize();
            }
            $this->set('type_rights', $type_rights, true);
        }

        if (isset($rights[$action])) {
            if (isset($type_rights[$action])) {
                return $type_rights[$action];
            } else {
                return $rights[$action];
            }
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - action with the model
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        parent::validate($action);

        // Get some commonly used vars
        $id   = $this->get('id');
        $code = $this->get('code');

        if ((!$id || $this->isDefined('name')) && !$this->get('name')) {
            $this->raiseError('error_no_name', 'name', null,
                              array($this->get('is_company') ?
                                    $this->getLayoutName('name') :
                                    mb_strtolower($this->i18n('customers_name'),
                                                  mb_detect_encoding($this->i18n('customers_name')))));
        }

        if (!$this->get('is_company') && (!$id || $this->isDefined('lastname')) && !$this->get('lastname')) {
            $this->raiseError('error_no_lastname_specified', 'lastname');
        }

        if (!$id && !$this->get('type')) {
            $this->raiseError('error_no_type_specified', 'type');
        }

        //ToDo validate web, phone, fax ... if needed
        if ($this->get('web')) {
            foreach ($this->get('web') as $index => $web) {
                if (!Validator::validUrl($web)) {
                    $this->raiseError('error_invalid_url', 'web|' . $index);
                    $this->raiseError('error_invalid_contact_data', 'link', -1, array($this->getLayoutName('contacts')));
                }
            }
        }

        if ($this->get('email')) {
            foreach ($this->get('email') as $index => $email) {
                if (!Validator::validEmail($email)) {
                    $this->raiseError('error_invalid_email', 'email|' . $index);
                    $this->raiseError('error_invalid_contact_data', 'link', -1, array($this->getLayoutName('contacts')));
                }
            }
        }

        if (!$code) {
            if (!$id) {
                $code = $this->setCode();
            } elseif ($this->isDefined('code')) {
                $this->raiseError('error_no_code', 'code', null, array($this->getLayoutName('code')));
            }
        }
        if ($code) {
            // If there are any other customers with the same code
            $query = 'SELECT `id` FROM `' . DB_TABLE_CUSTOMERS .'` WHERE `code`= \'' . General::slashesEscape($code) . '\'';
            if ($id) {
                $query .= " AND `id` != {$id}";
            }
            $query .= ' LIMIT 1';
            $code_exists = $this->registry['db']->GetOne($query);
            if ($code_exists) {
                // Set an error
                $code_layout_name = $this->getLayoutName('code');
                $this->raiseError('error_code_not_unique', 'code', null, array($code_layout_name, $code_layout_name));
            }
        }

        if ($this->get('ucn')) {
            if ($valid_ucn = Validator::validUCN($this->get('ucn'), $this->get('country')) || $valid_pfn = Validator::validPFN($this->get('ucn'), $this->get('country'))) {
                //the number is valid UCN or PFN, try to guess the salutation and check if it is unique!!!
                if ($this->get('ucn') != '9999999999') {
                    // check the gender and salutation
                    if ($valid_ucn && $this->get('country') == 'BG') {
                        //the PFN numbers do not contain gender information
                        //guess the salutation only if it is valid UCN
                        $ucn = $this->get('ucn');

                        // get gender by the 9th digit of the UCN
                        // odds (1,3,5,7,9) are for femaile, even (0,2,4,6,8,) are for male
                        $gender_flag = $ucn[8]%2;

                        $salutation_layout = $this->getLayoutsDetails('salutation');
                        if ($gender_flag) {
                            // female (woman)
                            if ($this->get('salutation') == 'mr' && $salutation_layout['edit']) {
                                // raise an error only if the salutation dropdown is editable (as per Bug 3536, comment 22)
                                $this->raiseError('error_no_correct_salutation', 'salutation');
                            }
                            if (!$salutation_layout['edit'] || $this->registry['edit_all'] || !$this->get('salutation')) {
                                // set mistress (mrs) as salutation (Bug 3536, comment 22)
                                $this->set('salutation', 'mrs', true);
                            }
                        } else {
                            // male (man)
                            if ($salutation_layout['edit'] && $this->get('salutation') && $this->get('salutation') != 'mr') {
                                // raise an error only if the salutation dropdown is editable (as per Bug 3536, comment 22)
                                $this->raiseError('error_no_correct_salutation', 'salutation');
                            }
                            if (!$salutation_layout['edit'] || $this->registry['edit_all'] || !$this->get('salutation')) {
                                // set mister (mr) as salutation (Bug 3536, comment 22)
                                $this->set('salutation', 'mr', true);
                            }
                        }
                    }

                    $query_ucn = 'SELECT c.id, c.ucn, CONCAT(ci18n.name, " ", ci18n.lastname) as name, cti18n.name as type_name' . "\n" .
                                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 ' ON (ci18n.parent_id=c.id AND ci18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS cti18n' . "\n" .
                                 ' ON (cti18n.parent_id=c.type AND cti18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                                 'WHERE c.deleted_by=0 AND c.is_company=0 AND c.ucn="' . $this->get('ucn') . '"';
                    if ($id) {
                        $query_ucn .= ' AND id!=' . $id;
                    }
                    $records_ucn = $this->registry['db']->GetRow($query_ucn);

                    if ($records_ucn) {
                        $placeholders = array(
                            'customer_type' => mb_strtolower($records_ucn['type_name'], 'UTF-8'),
                            'customer_name' => $records_ucn['name'],
                            'customer_link' => $_SERVER['SCRIPT_NAME'] . '?' . $this->registry['module_param'] . '=customers&amp;customers=view&amp;view=' . $records_ucn['id'],
                        );
                        $this->raiseError('error_ucn_not_unique', 'ucn', 0, $placeholders);
                    }
                }
            } else {
                //the number is neither valid UCN nor valid PFN (personal foreigner number)
                //report the error
                $this->raiseError('error_invalid_ucn', 'ucn');
            }
        }

        $system_eiks = $this->registry['config']->getParamAsArray('customers', 'system_eik');
        if ($this->get('eik') && !in_array($this->get('eik'), $system_eiks)) {
            $country_code = '';
            if ($this->get('country')) {
                $country_code = $this->get('country');
            } elseif ($this->get('in_dds')) {
                $country_code = preg_replace('#^([A-Z]{2})[0-9]*$#', '$1', $this->get('in_dds'));
            }

            if (!Validator::validEIK($this->get('eik'), ($country_code ? $country_code : 'BG')) &&
                //farmers are registered with UCN instead of EIK
                !Validator::validUCN($this->get('eik'))) {
                $this->raiseError('error_invalid_eik', 'eik');
            } else {
                $query_eik = 'SELECT c.id, c.eik, CONCAT(ci18n.name, " ", ci18n.lastname) as name, cti18n.name as type_name' . "\n" .
                             'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             ' ON (ci18n.parent_id=c.id AND ci18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS cti18n' . "\n" .
                             ' ON (cti18n.parent_id=c.type AND cti18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                             'WHERE c.deleted_by=0 AND c.is_company=1 AND c.eik="' . $this->get('eik') . '"';
                if ($id) {
                    $query_eik .= ' AND id!=' . $id;
                }
                $records_eik = $this->registry['db']->GetRow($query_eik);

                if ($records_eik) {
                    $placeholders = array(
                        'customer_type' => mb_strtolower($records_eik['type_name'], 'UTF-8'),
                        'customer_name' => $records_eik['name'],
                        'customer_link' => $_SERVER['SCRIPT_NAME'] . '?' . $this->registry['module_param'] . '=customers&amp;customers=view&amp;view=' . $records_eik['id'],
                    );
                    $this->raiseError('error_eik_not_unique', 'eik', 0, $placeholders);
                }
            }
        }

        if ($this->get('in_dds') && !$this->registry->get('dont_check_vat_num')) {
            $system_in_dds = $this->registry['config']->getParamAsArray('customers', 'system_in_dds');
            if (!in_array($this->get('in_dds'), $system_in_dds)) {
                $validation_result = Validator::validInDDS($this->get('in_dds'));
                if (!$validation_result) {
                    //invalid VAT number
                    $this->raiseError('error_invalid_in_dds', 'in_dds');
                } elseif ($validation_result === -1) {
                    //no connection to the register or some other error occurred
                    $this->raiseWarning('warning_in_dds_not_checked', 'in_dds');
                } elseif ($validation_result === -2) {
                    //no european format
                    $this->raiseWarning('warning_in_dds_not_european', 'in_dds');
                }
            }
        }

        // validate additional vars
        if ($action !== 'translate') {
            $this->validateVars();
        }

        // validate required fields for financial documents
        $this->validateCustomerFinData();

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            if ($this->registry['action'] == 'translate') {
                $action = 'translate';
            } else {
                //edit mode
                $action = 'edit';
            }
        } else {
            $action = 'add';
        }
        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();
            if ($action == 'translate') {
                $action = 'edit';
            }
            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();
         //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        if ($this->get('custom_modified')) {
            //this is used to provide correct modification date for the record
            //when we sync with outlook
            $set['added'] = sprintf("added='%s'", $this->get('custom_modified'));
        } else {
            $set['added'] = sprintf("added=now()");
        }
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['type']          = sprintf("type=%d", $this->get('type'));

        if ($this->get('is_company') == 1) {
            $set['is_company'] = sprintf("is_company=%d", 1);
        }
        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_CUSTOMERS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new customer base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // adds the main branch for the customer
        if (isset ($set['is_company'])) {
            //save a default branch of the customer
            $branch_inherited_properties = $this->branchInheritedProperties;
            array_unshift($branch_inherited_properties, 'name');

            $params = array(
                '_origin'            => 'manual',
                'subtype'            => 'branch',
                'parent_customer_id' => $this->get('id'),
                'main_branch'        => 1,
                'edit_branch'        => true,
                'model_lang'         => $this->get('model_lang')
            );

            foreach ($branch_inherited_properties as $property) {
                if ($property != 'name') {
                    // Set the inherited parameters
                    $params[$property] = $this->get($property);
                    // Set the notes for these parameters
                    if (in_array($property, $this->contactParameters)) {
                        $params[$property . '_note'] = $this->get($property . '_note');
                    }
                } else {
                    $params[$property] = $this->getBranchLabels('customers_central_office');
                }
            }

            require_once 'customers.branches.model.php';
            require_once 'customers.branches.factory.php';
            $branch = new Customers_Branch($this->registry, $params);

            $old_branch = new Customers_Branch($this->registry);
            $old_branch->sanitize();

            $branch->slashesEscaped = true;
            if ($branch->save()) {
                $filters = array('where' => array('c.id = ' . $branch->get('id'),
                                                  'c.subtype = \'branch\''),
                                 'model_lang' => $this->get('model_lang'),
                                 'sanitize' => true);
                $new_branch = Customers_Branches::searchOne($this->registry, $filters);

                $parent_customer = clone $this;
                $parent_customer->sanitize();

                Customers_History::saveData($this->registry, array('model'        => $parent_customer,
                                                                   'new_model'    => $new_branch,
                                                                   'old_model'    => $old_branch,
                                                                   'action_type'  => 'add_branch',
                                                                   'branch_name'  => $new_branch->get('name')));
            }
        }

        // save additional variables
        if (!$db->HasFailedTrans() && $this->isDefined('vars')) {
            if ($this->replaceVars()) {
                // replace temporary model_id with real id of model
                $this->updateModelIdAfterAdd();
                $this->updateModelFiles();
            } else {
                $db->FailTrans();
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        // If the transaction has failed remove the id
        if ($dbTransError) {
            // Remove the id
            $this->set('id', '', true);
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // edit the data for the main branch
        // 'edit_customer' flag means that customer is updated from main branch
        if (($this->get('is_company') == 1) && (!$this->get('edit_customer'))) {
            //save a default branch of the customer
            $branch_inherited_properties = $this->branchInheritedProperties;

            $params = array(
                'id'                 => $this->get('main_branch_id'),
                '_origin'            => 'manual',
                'subtype'            => 'branch',
                'parent_customer_id' => $this->get('id'),
                'main_branch'        => 1,
                'edit_branch'        => true,
                'model_lang'         => $this->get('model_lang')
            );
            foreach ($branch_inherited_properties as $property) {
                // in multiedit some properties might not be set to model,
                // they will not be modified in customer therefore
                // they should not be updated with a blank value in branch
                if (!$this->isDefined($property)) {
                    continue;
                }
                // Set the inherited parameters
                $params[$property] = $this->get($property);
                // Set the notes for this parameters
                if (in_array($property, $this->contactParameters)) {
                    $params[$property . '_note'] = $this->get($property . '_note');
                }
            }
            if (!$this->get('main_branch_id') || $this->registry['action'] == 'translate') {
                $params['name'] = $this->getBranchLabels('customers_central_office', false, true, true);
            }

            require_once 'customers.branches.model.php';
            require_once 'customers.branches.factory.php';
            $branch = new Customers_Branch($this->registry, $params);

            // prepare old model
            if ($branch->get('id')) {
                $filters = array('where' => array('c.id = ' . $branch->get('id'),
                                                  'c.subtype = \'branch\''),
                                 'model_lang' => $this->get('model_lang'),
                                 'sanitize' => true);
                $old_branch = Customers_Branches::searchOne($this->registry, $filters);
            } else {
                $old_branch = new Customers_Branch($this->registry);
                $old_branch->sanitize();
            }

            $branch->slashesEscaped = true;
            if ($branch->save()) {
                $filters = array('where' => array('c.id = ' . $branch->get('id'),
                                                  'c.subtype = \'branch\''),
                                 'model_lang' => $this->get('model_lang'),
                                 'sanitize' => true);
                $new_branch = Customers_Branches::searchOne($this->registry, $filters);

                $parent_customer = clone $this;
                $parent_customer->sanitize();

                $action_type = (!$this->get('main_branch_id') ?
                               'add' :
                               ($this->registry['action'] == 'translate' ?
                                   'translate' :
                                   'edit')) .
                               '_branch';
                Customers_History::saveData($this->registry, array('model'        => $parent_customer,
                                                                   'new_model'    => $new_branch,
                                                                   'old_model'    => $old_branch,
                                                                   'action_type'  => $action_type,
                                                                   'branch_name'  => $new_branch->get('name')));
            }
        }

        // save additional variables
        if (!$db->HasFailedTrans() && $this->isDefined('vars')) {
            if ($this->replaceVars()) {
                $this->updateModelFiles();
            } else {
                $db->FailTrans();
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Transfers existing model to new type
     *
     * @return bool - result of the operation
     */
    public function transfer() {
        $db = $this->registry['db'];

        $new_type = $this->registry['request']->get('to_type');
        $old_type = $this->get('type');

        //start transaction
        $db->StartTrans();

        $this->set('type', $old_type, true);

        //get additional vars for current model type
        $vars = $this->getAssocVars(true);
        $this->unsetVars();

        //set the new type to the model
        $this->set('type', $new_type, true);

        //get vars for the new type
        $new_vars = $this->getAssocVars(true);

        // collect multilang vars that need to be saved in other languages
        $new_vars_multilang = array();
        // collect ids of variables of old type whose data needs to be deleted
        $delete_ids = $delete_cstm = array();

        foreach ($vars as $key => $var) {
            $delete_ids[] = $var['id'];
            if ($var['bb']) {
                $has_bb = true;
            }
            if (!empty($var['cstm_model'])) {
                $delete_cstm[] = $var['id'];
            }
        }

        $bb_update = $bb_cstm = $franky_vars = array();

        foreach ($new_vars as $key => $var) {
            if (!array_key_exists($key, $vars)) {
                continue;
            }
            if ($vars[$key]['bb'] && ($vars[$key]['grouping'] || $vars[$key]['configurator'])) {
                // bb to bb (grouping table or configurator, not the plain vars)
                if (!$var['bb'] || !in_array($var['type'], array('group', 'config')) || $vars[$key]['type'] != $var['type']) {
                    if ($var['bb'] && !empty($var['cstm_model'])) {
                        // collect the mapping of inner variables which hold model ids
                        $bb_cstm[$vars[$key]['id']] = $var['id'];
                    }
                    continue;
                } else {
                    // collect the mapping of config/group variables in bb
                    $bb_update[$vars[$key]['id']] = array('meta_id' => $var['id'], 'bb_num' => $var['bb']);
                }
            } elseif ($vars[$key]['grouping'] && !$var['grouping']) {
                //array to plain
                reset($vars[$key]['value']);
                if (current($vars[$key]['value'])) {
                    $new_vars[$key]['value'] = current($vars[$key]['value']);
                }
            } elseif (!$vars[$key]['grouping'] && $var['grouping']) {
                //plain to array
                $new_vars[$key]['value'] = array($vars[$key]['value']);
            } elseif ($vars[$key]['type'] == 'config' && $vars[$key]['configurator'] != 0 &&
                      !empty($vars[$key]['source']) && $var['type'] == 'config' && $var['configurator'] != 0 &&
                      !empty($var['source'])) {
                //franky to franky
                $franky_vars[$var['configurator']] = $vars[$key]['configurator'];
            } else {
                $new_vars[$key]['value'] = $vars[$key]['value'];
            }
            if ($var['multilang']) {
                $new_vars_multilang[$key] = $var;
            }
        }

        $this->unsetVars();
        $this->unsetProperty('layouts_view', true);
        $this->unsetProperty('layouts_edit', true);

        $this->set('vars', $new_vars, true);

        // save data only in current model lang (do not auto-translate), it is saved in other langs later
        $this->set('translations', array($this->get('model_lang')), true);
        $this->replaceVars();
        $this->unsetVars();
        $this->unsetProperty('translations', true);

        if ($new_vars_multilang) {
            $translations = $this->getTranslations();
            $current_lang = $this->get('model_lang');

            foreach ($translations as $trans) {
                if ($trans == $current_lang) {
                    continue;
                }
                $this->set('model_lang', $trans, true);
                $this->set('type', $old_type, true);

                //get vars for the translations
                $vars = $this->getAssocVars(true);

                foreach ($new_vars_multilang as $key => $var) {
                    if (!array_key_exists($key, $vars)) {
                        continue;
                    }
                    if ($vars[$key]['grouping'] && !$var['grouping']) {
                        //array to plain
                        reset($vars[$key]['value']);
                        if (current($vars[$key]['value'])) {
                            $new_vars_multilang[$key]['value'] = current($vars[$key]['value']);
                        }
                    } elseif (!$vars[$key]['grouping'] && $var['grouping']) {
                        //plain to array
                        $new_vars_multilang[$key]['value'] = array($vars[$key]['value']);
                    } else {
                        $new_vars_multilang[$key]['value'] = $vars[$key]['value'];
                    }
                }
                $this->set('vars', $new_vars_multilang, true);
                // flag to save vars as if action is translate (only multilang ones and only in current model lang)
                $this->set('translate_multilang', true, true);
                $this->replaceVars();
                $this->unsetVars();
                $this->unsetProperty('translate_multilang', true);
            }

            //set the new type to the model and the model lang
            $this->set('type', $new_type, true);
            $this->set('model_lang', $current_lang, true);
        }

        //query to update the main table
        $query = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                 'SET `type` = ' . $new_type . "\n" .
                 'WHERE id=' . $this->get('id');
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        if (!empty($delete_ids)) {
            $query = "DELETE FROM " . DB_TABLE_CUSTOMERS_CSTM . "\n" .
                     "WHERE model_id = " . $this->get('id') . "\n" .
                     "  AND var_id IN (" . implode(', ', $delete_ids) . ")";
            $db->Execute($query);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
            }
        }

        if (!empty($franky_vars) && count($franky_vars)) {
            foreach ($franky_vars as $new_num => $old_num) {
                $query = "UPDADE " . DB_TABLE_CONFIGURATOR . "\n" .
                         "SET model_type = " . $new_type . ", num='" . $new_num . "'" . "\n" .
                         "WHERE model='Customer' AND model_type=" . $old_type . "\n" .
                         "  AND model_id=" . $this->get('id') . " AND num='" . $old_num . "'";
                $db->Execute($query);

                if ($db->ErrorMsg()) {
                   $this->registry['messages']->setError($db->ErrorMsg());
                   break;
                }
            }
        }

        if (!empty($has_bb)) {
            // bb data to update if any
            foreach ($bb_update as $old_meta_id => $new_bb) {
                $query = 'UPDATE ' . DB_TABLE_BB . "\n" .
                         'SET model_type=\'' . $new_type . '\', bb_num=\'' . $new_bb['bb_num'] . '\', meta_id=\'' . $new_bb['meta_id'] . '\'' . "\n" .
                         'WHERE model=\'' . $this->modelName . '\'' . "\n" .
                         '  AND model_id=\'' . $this->get('id') . '\'' . "\n" .
                         '  AND meta_id=\'' . $old_meta_id . '\'';
                $db->Execute($query);
            }

            // delete remaining bb data if any
            $this->delBB(array(
                'model_id' => $this->get('id'),
                'model_type' => $old_type
            ));

            // update bb model relations for inner variables
            foreach ($bb_cstm as $old_meta_id => $new_meta_id) {
                $query = 'UPDATE ' . DB_TABLE_CSTM_RELATIVES . "\n" .
                         'SET var_id=\'' . $new_meta_id . '\'' . "\n" .
                         'WHERE model=\'' . $this->modelName . '\'' . "\n" .
                         '  AND model_id=\'' . $this->get('id') . '\'' . "\n" .
                         '  AND var_id=\'' . $old_meta_id . '\'';
                $db->Execute($query);
            }
        }

        // delete any model relations from additional variables of old type
        if ($delete_cstm) {
            $this->saveCstmRelatives(array_flip($delete_cstm));
        }

        //set the values of the
        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        if ($dbTransError) {
            $this->set('type', $old_type, true);
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Gets counter for this model
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            if ($sanitize_after = $this->isSanitized()) {
                $this->unsanitize();
            }

            require_once 'customers.counters.factory.php';
            $filters = array('where' => array('ct.id = \'' . $this->get('type') . '\'',
                                              'cc.deleted IS NOT NULL'),
                             'sanitize' => true);
            $this->counter = Customers_Counters::searchOne($this->registry, $filters);

            if ($sanitize_after) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Sets number to model
     *
     * @return int - result of the operation
     */
    public function setNumber() {
        $this->set('added', '', true);

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                  'SET num = ?' . "\n" .
                  'WHERE id = ' . $this->get('id');
        $result = $this->registry['db']->Execute($query1, array($this->getNum()));

        return $result;
    }

    /**
     * Check whether model should get a number
     *
     * @return bool - result of the operation
     */
    public function checkGetNum() {
        if (!$this->isActivated() || !$this->get('type') || $this->get('num') ||
        !$this->registry['db']->GetOne('SELECT counter FROM ' . DB_TABLE_CUSTOMERS_TYPES . ' WHERE id = \'' . $this->get('type') . '\'')) {
            return false;
        }

        if (!$this->get('id')) {
            return true;
        } else {
            return $this->registry['db']->GetOne('SELECT active FROM ' . DB_TABLE_CUSTOMERS . ' WHERE id = \'' . $this->get('id') . '\'') === '0';
        }
    }

    /**
     * Gets number for model
     *
     * @param bool $force - force getting a number even when model has one
     * @return string - number of model or empty string when type does not use counter
     */
    public function getNum($force = false) {
        if ($this->get('num') && !$force) {
            return $this->get('num');
        }

        if (!$this->isActivated()) {
            return '';
        }

        //get the counter assigned to the type of financial document
        $this->getCounter();

        if (!$this->counter) {
            return '';
        }

        //create extender to expand the formula components
        $extender = new Extender;

        //get next number from db and lock the counter for update to guarantee unique next number
        $extender->add('num', sprintf('%0' . $this->counter->get('leading_zeroes') . 'd', $this->counter->getNextNumber()));

        // increment the counter
        $this->counter->increment();

        if ($this->counter->get('prefix_used')) {
            $extender->add('prefix', $this->counter->get('prefix'));
        }

        if ($this->counter->get('user_code')) {
            $extender->add('user_code', $this->registry['currentUser']->get('code'));
        }

        if ($this->counter->get('assigned_code')) {
            $extender->add('assigned_code',
                $this->get('assigned') ?
                $this->registry['db']->GetOne('SELECT code FROM ' . DB_TABLE_USERS . ' WHERE id = \'' . $this->get('assigned') . '\'') :
                '');
        }

        if ($this->counter->get('added_used')) {
            $extender->add('added', General::strftime($this->counter->get('date_format'),
                ($this->get('added') ? strtotime($this->get('added')) : '')));
        }

        $num = $extender->expand($this->counter->get('formula'));

        $delimiter = $this->counter->get('delimiter');
        if ($delimiter) {
            //remove repeating delimiters
            $num = preg_replace('#' . preg_quote($delimiter . $delimiter) . '#', $delimiter, $num);
            $num = preg_replace('#' . preg_quote($delimiter) . '$#', '', $num);
            $num = preg_replace('#^' . preg_quote($delimiter) . '#', '', $num);
        }

        if ($this->slashesEscaped) {
            $num = General::slashesEscape($num);
        }

        $this->set('num', $num, true);

        return $this->get('num');
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        foreach ($this->contactParameters as $contact_param) {
            if ($this->registry['action'] == 'multiedit' && !$this->isDefined($contact_param)) {
                continue;
            }
            $contact_param_value = $this->get($contact_param);
            if (!empty($contact_param_value)) {
                $contact_data = array();
                $contact_notes = $this->get($contact_param . '_note');
                foreach ($this->get($contact_param) as $idx => $contact) {
                    $contact_data[] = sprintf("%s%s", $contact, (!empty($contact_notes[$idx])) ? '|' . $contact_notes[$idx] : '');
                }
                $set[$contact_param] = sprintf("%s='%s'", $contact_param, implode("\n", $contact_data));
            } else {
                $set[$contact_param] = sprintf("%s=''", $contact_param);
            }
        }
        if ($this->isDefined('country')) {
            $set['country']  = sprintf("country='%s'", $this->get('country'));
        }
        if ($this->isDefined('postal_code')) {
            $set['postal_code']     = sprintf("postal_code='%s'", $this->get('postal_code'));
        }
        if ($this->isDefined('gateway')) {
            $set['gateway']         = sprintf("gateway='%s'", $this->get('gateway'));
        }
        if ($this->isDefined('in_dds')) {
            $set['in_dds']          = sprintf("in_dds='%s'", $this->get('in_dds'));
        }
        if ($this->isDefined('eik')) {
            $set['eik']             = sprintf("eik='%s'", $this->get('eik'));
        }
        if ($this->isDefined('ucn')) {
            $set['ucn']             = sprintf("ucn='%s'", $this->get('ucn'));
        }
        if ($this->isDefined('identity_num')) {
            $set['identity_num']    = sprintf("identity_num='%s'", $this->get('identity_num'));
        }
        if ($this->isDefined('identity_date')) {
            $set['identity_date']   = sprintf("identity_date='%s'", $this->get('identity_date'));
        }
        if ($this->isDefined('identity_valid')) {
            $set['identity_valid']  = sprintf("identity_valid='%s'", $this->get('identity_valid'));
        }
        if ($this->isDefined('salutation')) {
            $set['salutation']      = sprintf("salutation='%s'", $this->get('salutation'));
        }
        if ($this->isDefined('iban')) {
            $set['iban']            = sprintf("iban='%s'", $this->get('iban'));
        }
        if ($this->isDefined('bic')) {
            $set['bic']             = sprintf("bic='%s'", $this->get('bic'));
        }
        if ($this->isDefined('code')) {
            $set['code']            = sprintf("code='%s'", $this->get('code'));
        }
        if ($this->checkGetNum()) {
            $set['num']             = sprintf("num='%s'", $this->getNum());
        }
        if ($this->isDefined('registration_file')) {
            $set['registration_file'] = sprintf("registration_file='%s'", $this->get('registration_file'));
        }
        if ($this->isDefined('registration_volume')) {
            $set['registration_volume'] = sprintf("registration_volume='%s'", $this->get('registration_volume'));
        }
        if ($this->isDefined('registration_number')) {
            $set['registration_number'] = sprintf("registration_number='%s'", $this->get('registration_number'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->registry['currentUser']->get('is_portal')) {
            $set['is_portal'] = "is_portal=1";
        } elseif ($this->isDefined('is_portal')) {
            $set['is_portal'] = sprintf("is_portal=%d", $this->get('is_portal'));
        }
        if ($this->isDefined('admit_VAT_credit')) {
            $set['admit_VAT_credit'] = sprintf("admit_VAT_credit=%d", $this->get('admit_VAT_credit'));
        } else {
            $set['admit_VAT_credit'] = sprintf("admit_VAT_credit=%d", 0);
        }
        if ($this->isDefined('department')) {
            $set['department'] = sprintf("`department`=%d", $this->get('department'));
        }
        if ($this->isDefined('assigned')) {
            $set['assigned'] = sprintf("`assigned`=%d", $this->get('assigned'));
        }

        if ($this->get('custom_modified')) {
            //this is used to provide correct modification date for the record
            //when we sync with outlook
            $set['modified'] = sprintf("modified='%s'", $this->get('custom_modified'));
        } else {
            $set['modified'] = sprintf("modified=now()");
        }
        $set['modified_by']     = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            $update['name'] = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('lastname')) {
            $update['lastname']  = sprintf("lastname='%s'", $this->get('lastname'));
        }
        if ($this->isDefined('city')) {
            $update['city']  = sprintf("city='%s'", $this->get('city'));
        }
        if ($this->isDefined('address')) {
            $update['address']  = sprintf("address='%s'", $this->get('address'));
        }
        if ($this->isDefined('company_department')) {
            $update['company_department']  = sprintf("company_department='%s'", $this->get('company_department'));
        }
        if ($this->isDefined('position')) {
            $update['position']  = sprintf("position='%s'", $this->get('position'));
        }
        if ($this->isDefined('mol')) {
            $update['mol'] = sprintf("mol='%s'", $this->get('mol'));
        }
        if ($this->isDefined('notes')) {
            $update['notes']  = sprintf("notes='%s'", $this->get('notes'));
        }
        if ($this->isDefined('contact_person')) {
            $update['contact_person']  = sprintf("contact_person='%s'", $this->get('contact_person'));
        }
        if ($this->isDefined('company_name')) {
            $update['company_name']  = sprintf("company_name='%s'", $this->get('company_name'));
        }
        if ($this->isDefined('address_by_personal_id')) {
            $update['address_by_personal_id']  = sprintf("address_by_personal_id='%s'", $this->get('address_by_personal_id'));
        }
        if ($this->isDefined('registration_address')) {
            $update['registration_address']  = sprintf("registration_address='%s'", $this->get('registration_address'));
        }
        if ($this->isDefined('identity_by')) {
            $update['identity_by']   = sprintf("identity_by='%s'", $this->get('identity_by'));
        }
        if ($this->isDefined('bank')) {
            $update['bank']   = sprintf("bank='%s'", $this->get('bank'));
        }


        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('editing customer i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }
    }

    /**
     * Update relatives table of the model
     *
     * @param bool $delete - whether to delete old relations first
     * @param array $relatives - relative types (major_associate-parent, minor_associate-parent, etc.), array should be assoc: <referer_id> => <relative_type>
     * @param array $referers - referer ids (numbered array)
     * @param array $notes -  notes, array should be assoc: <referer_id> => <relative_type>
     * @return bool - result of the operation
     */
    public function updateRelatives($delete = true, $relatives = null, $referers = null, $notes = null) {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        if ($delete) {
            // delete old relations
            $query3 = 'DELETE FROM ' . DB_TABLE_CUSTOMERS_RELATIVES . "\n" .
                      ' WHERE parent_id=' . $this->get('id') . ' OR child_id=' . $this->get('id');
            $db->Execute($query3);
        }

        // add new relations
        $new_relatives = array();
        if (!$relatives) {
            $relatives = $this->registry['request']->get('relative_type') ?: array();
        }
        if (!$referers) {
            $referers = $this->registry['request']->get('referers') ?: array();
        }
        if (!$notes) {
            $notes = $this->registry['request']->get('notes') ?: array();
        }

        foreach ($relatives as $key => $relative) {
            $relation_properties = array();
            if (in_array($key, $referers)) {
                $relation_properties = explode('-', $relative);
                $new_relatives[$key]['relation_type'] = $relation_properties[0];

                //if there's no relation side the relation is equivalent
                if (isset($relation_properties[1]) && $relation_properties[1] == 'child') {
                    $new_relatives[$key]['parent_id'] = $key;
                    $new_relatives[$key]['child_id'] = $this->get('id');
                } else {
                    $new_relatives[$key]['parent_id'] = $this->get('id');
                    $new_relatives[$key]['child_id'] = $key;
                }
            }
        }
        foreach ($notes as $key => $note) {
            if (in_array($key, $referers)) {
                $new_relatives[$key]['notes'] = $note;
            }
        }

        if (! empty ($new_relatives)) {
            $tmp = array();
            foreach ($new_relatives as $rels) {
                $tmp[] = '(' . $rels['parent_id'] . ', ' . $rels['child_id'] . ', "' . $rels['relation_type'] . '", ' . (isset($rels['notes']) ? '"' . General::slashesEscape($rels['notes']) . '"' : 'NULL') . ')';
            }
            $query4 = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_RELATIVES . ' (parent_id, child_id, relative_type, notes) VALUES ' . "\n" .
                      implode(', ' . "\n", $tmp);
        }

        if (!empty($query4)) {
            $db->Execute($query4);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update attached files
     *
     * @return bool - result of the operation
     */
    /*public function updateFiles() {
        $db = $this->registry['db'];

        $accepted_drives = Mapped_Drives::getDrives($this->registry);
        $files = $this->get('file_location');
        $notes = $this->get('file_notes');

        if (is_array($files) && count($files) > 0) {
            $inserts = array();
            $inserted_files = array();
            foreach ($files as $index=>$file) {
                if (trim($file)) {
                    foreach ($accepted_drives as $drive => $path) {
                        $regex = '#^' . $drive . '\:\\\\*#i';
                        if (preg_match($regex, $file)) {
                            $file = addslashes(preg_replace($regex, addslashes($path), stripslashes($file)));
                        }
                    }
                    if (substr($file,0,2)=='\\\\' && !in_array($file,$inserted_files)) {
                        $inserted_files[] = $file;
                        $note = $notes[$index];
                        $inserts[] = "(" . $this->get('id') . ", '$file', '$note')";
                    }
                }
            }
            if (count($inserts) > 0) {
                $query6 = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_FILES;
                $query6 .= " (parent_id, name, notes) VALUES ". implode(",\n", $inserts);
            }
        }

        if (!empty($query6)) {
            $db->Execute($query6);
            return !$db->HasFailedTrans();
        } else {
            return true;
        }
    }*/

    /**
     * Get parents and children referers from database
     * and sets them to model.
     *
     * @return bool - result of the operation
     */
    public function getParents() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT cr.parent_id as parent_id, cr.child_id as child_id, cr.relative_type, cr.notes, ' . "\n" .
                 '  c_p.is_company as parent_is_company, c_c.is_company as child_is_company, ' . "\n" .
                 'CONCAT(ci18n1.name, " ", ci18n1.lastname) as parent_name, ' . "\n" .
                 'CONCAT(ci18n2.name, " ", ci18n2.lastname) as child_name ' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS_RELATIVES . ' AS cr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c_p ' . "\n" .
                 '  ON (c_p.id=cr.parent_id) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c_c ' . "\n" .
                 '  ON (c_c.id=cr.child_id) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n1 ' . "\n" .
                 '  ON (cr.parent_id=ci18n1.parent_id AND ci18n1.lang="' . $lang . '") ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n2 ' . "\n" .
                 '  ON (cr.child_id=ci18n2.parent_id AND ci18n2.lang="' . $lang . '") ' . "\n" .
                 'WHERE cr.child_id="' . $this->get('id') . '" OR cr.parent_id="' . $this->get('id') . '"';
        $db->Execute($query);
        $records = $this->registry['db']->GetAll($query);

        $referers = array();

        foreach ($records as $key => $recs) {
            if ($recs['parent_id'] == $this->get('id')) {
                $referers[$recs['child_id']]['name'] = $recs['child_name'];
                $referers[$recs['child_id']]['relative_type'] = $recs['relative_type'];
                $referers[$recs['child_id']]['id'] = $recs['child_id'];
                $referers[$recs['child_id']]['is_company'] = $recs['child_is_company'];
                $referers[$recs['child_id']]['relative_side'] = 'parent';
                $referers[$recs['child_id']]['notes'] = $recs['notes'];
                unset($records[$key]);
            } else {
                $referers[$recs['parent_id']]['name'] = $recs['parent_name'];
                $referers[$recs['parent_id']]['relative_type'] = $recs['relative_type'];
                $referers[$recs['parent_id']]['id'] = $recs['parent_id'];
                $referers[$recs['parent_id']]['is_company'] = $recs['parent_is_company'];
                $referers[$recs['parent_id']]['relative_side'] = 'child';
                $referers[$recs['parent_id']]['notes'] = $recs['notes'];
                unset($records[$key]);
            }
        }

        return $this->set('referers', $referers);
    }

    /**
     * Get customers files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        $sql = array();
        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $ids_where .= Files::getAdditionalWhere($this->registry);

        //get the generated files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="generated"' . "\n" .
                  'GROUP BY pattern_id';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $generated = $this->registry['db']->GetAll($query);

            $files['generated'] = $generated;
        } else {
            $files['generated'] = array();
        }

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments) && empty($generated)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get customer attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array();
        $filters['where'] = array('f.model = \'Customer\'',
                                  'f.model_id = ' . $this->get('id'),
                                  'f.origin = \'attached\'',
                                  'f.deleted = 0');
        $filters['sanitize'] = true;
        $filters['model_lang'] = $this->get('model_lang');
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }
        $this->set('attachments', $files, true);
        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get generated files details
     *
     * @param array $params - filtering params
     * @return array - generated files and their revisions
     */
    public function getGeneratedFiles($params = array()) {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Customer\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'generated\'',
                                          'f.deleted =  0'),
                         'sanitize' => 1);

        if (isset($params['pattern_id'])) {
            $filters['where'][] = 'f.pattern_id = ' . $params['pattern_id'];
        }
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
            $icon_name = $file->getIconName($file->get('filename'));
            $file->set('icon_name', $icon_name);
            $files[$k] = $file;
        }
        $this->set('genfiles' ,$files, true);
        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get customers contacts
     *
     * @return bool - result of the operation
     */
    public function getContacts() {
        foreach ($this->contactParameters as $contact_param) {
            if ($this->get($contact_param)) {
                $contact_rows = explode("\n", $this->get($contact_param));
                $contacts = array();
                $contact_notes = array();
                foreach ($contact_rows as $idx => $contact_data) {
                    if (strpos($contact_data, '|') !== false) {
                        list($contact, $contact_note) = explode("|", $contact_data, 2);
                        $contacts[$idx] = $contact;
                        $contact_notes[$idx] = $contact_note;
                    } else {
                        $contacts[$idx] = $contact_data;
                        $contact_notes[$idx] = "";
                    }
                }
                $this->set($contact_param, $contacts, true);
                $this->set($contact_param . '_note', $contact_notes, true);
            }
        }
    }

    /**
     * Get customers main contacts person information
     *
     * @return bool - result of the operation
     */
    public function getPersonInfo() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $contact_person = '';
        if ($this->get('id')) {
            $query = 'SELECT CONCAT(ci18n.name, " ", ci18n.lastname) as contact_person ' . "\n" .
                'FROM ' . DB_TABLE_CUSTOMERS . ' as cp ' . "\n" .
                'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                ' ON c.id=cp.parent_customer' . "\n" .
                'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                ' ON (cp.id=ci18n.parent_id AND ci18n.lang="' . $lang . '")' . "\n" .
                ' WHERE cp.subtype="contact" AND c.subtype="branch" AND c.parent_customer=' .
                $this->get('id') . ' AND c.is_main=1 AND cp.is_main=1';
            $contact_person = $db->GetOne($query);
        }
        $this->set('contact_person', $contact_person, true);

        return true;
    }

    /**
     * Get customers contacts from POST
     *
     * @return bool - result of the operation
     */
    public function getPostContacts() {
        $links = $this->registry['request']['link'];
        if (empty($links)) {
            $links = array();
        }
        $link_notes = $this->registry['request']['link_notes'];
        if (empty($link_notes)) {
            $link_notes = array();
        }
        $link_types = $this->registry['request']['link_types'];

        $contacts = array();
        $contact_notes = array();
        $records = array();
        $inserted_links = array();
        foreach ($links as $index => $link) {
            if (! isset($inserted_links[$link_types[$index]])) {
                $inserted_links[$link_types[$index]] = array();
            }
            if (trim($link) && !in_array($link, $inserted_links[$link_types[$index]]) && !empty($link)) {
                $inserted_links[$link_types[$index]][] = $link;
                $contacts[$link_types[$index]][$index] = $link;
                $contact_notes[$link_types[$index]][$index] = $link_notes[$index];
            }
        }

        foreach ($contacts as $key => $recs) {
            $this->set($key , $recs, true);
        }
        foreach ($contact_notes as $key => $recs) {
            $this->set($key . '_note' , $recs, true);
        }

        //add properties that have no values but don't do it for multi actions
        if (!preg_match('#^multi#', $this->registry['action'])) {
            foreach ($this->contactParameters as $contact_param) {
                if (!$this->isDefined($contact_param)) {
                    $this->set($contact_param , array(), true);
                    $this->set($contact_param . '_note' , array(), true);
                }
            }
        }

        //special behaviour for multi actions for the contact data
        foreach ($this->contactParameters as $contact_param) {
            if ($this->isDefined($contact_param) && $this->registry['request']->isRequested($contact_param)) {
                $contact_data = preg_split('#\s*,\s*#', $this->get($contact_param));
                $contacts = array();
                $contact_notes = array();
                foreach ($contact_data as $idx => $value) {
                    if (trim($value)) {
                        if (strpos($value, '|') !== false) {
                            list($contact, $contact_note) = explode("|", $value, 2);
                            $contacts[$idx] = $contact;
                            $contact_notes[$idx] = $contact_note;
                        } else {
                            $contacts[$idx] = $value;
                            $contact_notes[$idx] = '';
                        }
                    }
                }
                $this->set($contact_param , $contacts, true);
                $this->set($contact_param . '_note' , $contact_notes, true);
            }
        }
    }

    /**
     * Gets predefined contact parameters from additional required fields
     * for type of model (if any) or from class property with default values.
     *
     * @return array - array with results
     */
    public function getPredefinedContactParameters() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $validate_settings = ($this->get('type') && $this->registry['config']->isSetParam($this->module, 'validate_' . $this->get('type'))) ?
                             $this->registry['config']->getParam($this->module, 'validate_' . $this->get('type')) :
                             '';
        if ($validate_settings) {
            $fields = preg_split('/\s*,\s*/', (string)$validate_settings);
            $required_contact_fields = array_intersect($this->contactParameters, $fields);
            if ($required_contact_fields) {
                $this->predefinedContactParameters = array_merge($required_contact_fields,
                                                                 array_diff($this->predefinedContactParameters,
                                                                            $required_contact_fields));
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->predefinedContactParameters;
    }

    /**
     * Translates some i18n fields
     *
     * @return bool - result of the operation
     */
    public function getDefaultTranslations() {
        $db = $this->registry['db'];
        $model_lang = $this->registry['lang'];

        $sql = array();
        //select clause
        $sql['select'] = 'SELECT c.*, ci18n.*, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  cti18n.name as type_name ' . "\n";
       //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to customer types
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS cti18n' . "\n" .
                       '  ON (c.type=cti18n.parent_id AND cti18n.lang="' . $model_lang . '")' . "\n";

        $sql['where'] = 'WHERE c.id=' . $this->get('id') . "\n";

        $query = implode("\n", $sql);
        $records = $db->GetRow($query);

        $this->set('default_translations', $records, true);

        return $records;
    }

    /**
     * Gets records related to the model
     *
     * @return array - array with related records
     */
    public function getRelatedRecords() {

        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $related = array();
        $registry = &$this->registry;
        $db = &$registry['db'];

        if ($this->checkPermissions('relatives')) {
            //get related customers
            $query = 'SELECT DISTINCT(IF(cr.parent_id = ' . $this->get('id') . ', cr.child_id,' . "\n" .
                     '       IF(cr.child_id = ' . $this->get('id') . ', cr.parent_id, NULL))) AS id' . "\n" .
                     'FROM ' . DB_TABLE_CUSTOMERS_RELATIVES . " AS `cr`\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS . " AS `c`\n" .
                     '  ON cr.parent_id=c.id OR cr.child_id=c.id' . "\n" .
                     'WHERE c.deleted_by = 0 AND (cr.parent_id = ' . $this->get('id') .
                     '   OR cr.child_id = ' . $this->get('id') . ")\n";

            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=customers&amp;';
            $link .= 'customers=relatives&amp;relatives=' . $this->get('id');
            $related['customers'] = array('name' => 'customers',
                                          'label' => $this->i18n('menu_customers'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
        }

        //gets which modules of optional related records should be displayed
        list($related_records_modules, $referent_model_types) = $this->getRelatedRecordsModules();

        // do not display modules that user has no access to
        $rights = $registry['currentUser']->get('rights');

        if (in_array('documents', $related_records_modules)) {
            //get related documents
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('d.customer=' . $this->get('id')),
                             'check_module_permissions' => 'documents');
            $result = Documents::getIds($registry, $filters);
            $related['documents'] = array('name' => 'documents',
                                          'label' => $this->i18n('menu_documents'),
                                          'link' => '#related_subpanel_customer' . $this->get('id'),
                                          'ids' => is_array($result) ? $result : array());
        }

        if (in_array('projects', $related_records_modules)) {
            //get related projects
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('p.customer=' . $this->get('id')),
                             'check_module_permissions' => 'projects');
            $result = Projects::getIds($registry, $filters);
            $related['projects'] = array('name' => 'projects',
                                         'label' => $this->i18n('menu_projects'),
                                         'link' => '#related_subpanel_customer' . $this->get('id'),
                                         'ids' => is_array($result) ? $result : array());
        }

        if (in_array('tasks', $related_records_modules)) {
            //get related tasks
            require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('t.customer=' . $this->get('id'),
                                              't.type != ' . PH_TASK_SYSTEM_TYPE),
                             'check_module_permissions' => 'tasks');
            $result = Tasks::getIds($registry, $filters);
            $related['tasks'] = array('name' => 'tasks',
                                      'label' => $this->i18n('menu_tasks'),
                                      'link' => '#related_subpanel_customer' . $this->get('id'),
                                      'ids' => is_array($result) ? $result : array());
        }

        if (in_array('events', $related_records_modules)) {
            //get related events
            require_once PH_MODULES_DIR . 'events/models/events.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('e.customer=' . $this->get('id')),
                             'check_module_permissions' => 'events');
            $result = Events::getIds($registry, $filters);
            $related['events'] = array('name' => 'events',
                                       'label' => $this->i18n('menu_events'),
                                       'link' => '#related_subpanel_customer' . $this->get('id'),
                                       'ids' => is_array($result) ? $result : array());
        }

        if (in_array('contracts', $related_records_modules)) {
            //get related contracts
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('co.customer=' . $this->get('id'),
                                              'co.subtype!="original"'),
                             'check_module_permissions' => 'contracts');
            $result = Contracts::getIds($registry, $filters);
            $related['contracts'] = array('name' => 'contracts',
                                          'label' => $this->i18n('menu_contracts'),
                                          'link' => '#related_subpanel_customer' . $this->get('id'),
                                          'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_incomes_reasons', $related_records_modules)) {
            //get related finance incomes reasons
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('fir.customer=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Incomes_Reasons::getIds($registry, $filters);
            $related['finance_incomes_reasons'] = array('name' => 'finance_incomes_reasons',
                                                        'label' => $this->i18n('menu_finance_incomes_reasons'),
                                                        'link' => '#related_subpanel_customer' . $this->get('id'),
                                                        'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_expenses_reasons', $related_records_modules)) {
            //get related finance expenses reasons
            require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('fer.customer=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Expenses_Reasons::getIds($registry, $filters);
            $related['finance_expenses_reasons'] = array('name' => 'finance_expenses_reasons',
                                                         'label' => $this->i18n('menu_finance_expenses_reasons'),
                                                         'link' => '#related_subpanel_customer' . $this->get('id'),
                                                         'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_payments', $related_records_modules)) {
            //get related finance payments
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('fp.customer=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Payments::getIds($registry, $filters);
            $related['finance_payments'] = array('name' => 'finance_payments',
                                                 'label' => $this->i18n('menu_finance_payments'),
                                                 'link' => '#related_subpanel_customer' . $this->get('id'),
                                                 'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_recurring_payments', $related_records_modules)) {
            //get related finance recurring payments
            require_once PH_MODULES_DIR . 'finance/models/finance.recurring_payments.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('frp.customer=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Recurring_Payments::getIds($registry, $filters);
            $related['finance_recurring_payments'] = array('name' => 'finance_recurring_payments',
                                                           'label' => $this->i18n('menu_finance_recurring_payments'),
                                                           'link' => '#related_subpanel_customer' . $this->get('id'),
                                                           'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_repayment_plans', $related_records_modules)) {
            //get related finance repayment plans
            require_once PH_MODULES_DIR . 'finance/models/finance.repayment_plans.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('frp.customer=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Repayment_Plans::getIds($registry, $filters);
            $related['finance_repayment_plans'] = array('name' => 'finance_repayment_plans',
                                                        'label' => $this->i18n('menu_finance_repayment_plans'),
                                                        'link' => '#related_subpanel_customer' . $this->get('id'),
                                                        'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_warehouses_documents', $related_records_modules)) {
            //get related finance warehouses documents
            require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('fwd.customer=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Warehouses_Documents::getIds($registry, $filters);
            $related['warehouse'] = array('name' => 'finance_warehouses_documents',
                                          'label' => $this->i18n('menu_finance_warehouses_documents'),
                                          'link' => '#related_subpanel_customer' . $this->get('id'),
                                          'ids' => is_array($result) ? $result : array());
        }

        if (in_array('referent_documents', $related_records_modules)) {

            //check if there are model types to get referent records for
            if (empty($referent_model_types['referent_documents'])) {
                //no model types - do not continue
                $related_records_modules = array_diff($related_records_modules, array('referent_documents'));
            } else {
                //get related referent documents
                $result = $this->getCstmRelatives('Document', $referent_model_types['referent_documents']);
                if (!empty($result)) {
                    // search once again to get only ids for models of active types that user has 'list' permissions for
                    require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                    $filters = array('sanitize' => true,
                                     'where' => array('d.id IN (' . implode(', ', $result) . ')'),
                                     'check_module_permissions' => 'documents');
                    $result = Documents::getIds($registry, $filters);
                }
                $related['referent_documents'] = array('name' => 'referent_documents',
                                                       'label' => $this->i18n('menu_referent_documents'),
                                                       'link' => '#related_subpanel_customer' . $this->get('id'),
                                                       'ids' => is_array($result) ? $result : array());
            }
        }

        if (in_array('referent_projects', $related_records_modules)) {

            //check if there are model types to get referent records for
            if (empty($referent_model_types['referent_projects'])) {
                //no model types - do not continue
                $related_records_modules = array_diff($related_records_modules, array('referent_projects'));
            } else {
                //get related referent projects
                $result = $this->getCstmRelatives('Project', $referent_model_types['referent_projects']);
                if (!empty($result)) {
                    // search once again to get only ids for models of active types that user has 'list' permissions for
                    require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
                    $filters = array('sanitize' => true,
                                     'where' => array('p.id IN (' . implode(', ', $result) . ')'),
                                     'check_module_permissions' => 'projects');
                    $result = Projects::getIds($registry, $filters);
                }
                $related['referent_projects'] = array('name' => 'referent_projects',
                                                       'label' => $this->i18n('menu_referent_projects'),
                                                       'link' => '#related_subpanel_customer' . $this->get('id'),
                                                       'ids' => is_array($result) ? $result : array());
            }
        }

        if (in_array('referent_contracts', $related_records_modules)) {

            //check if there are model types to get referent records for
            if (empty($referent_model_types['referent_contracts'])) {
                //no model types - do not continue
                $related_records_modules = array_diff($related_records_modules, array('referent_contracts'));
            } else {
                //get related referent contracts
                $result = $this->getCstmRelatives('Contract', $referent_model_types['referent_contracts']);
                if (!empty($result)) {
                    // search once again to get only ids for models that user has 'list' permissions for
                    require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                    $filters = array('sanitize' => true,
                                     'where' => array('co.id IN (' . implode(', ', $result) . ')',
                                                      'co.subtype!="original"'),
                                     'check_module_permissions' => 'contracts');
                    $result = Contracts::getIds($registry, $filters);
                }
                $related['referent_contracts'] = array('name' => 'referent_contracts',
                                                       'label' => $this->i18n('menu_referent_contracts'),
                                                       'link' => '#related_subpanel_customer' . $this->get('id'),
                                                       'ids' => is_array($result) ? $result : array());
            }
        }

        if (in_array('referent_customers', $related_records_modules)) {
            //check if there are model types to get referent records for
            if (empty($referent_model_types['referent_customers'])) {
                //no model types - do not continue
                $related_records_modules = array_diff($related_records_modules, array('referent_customers'));
            } else {
                //get related referent customers
                $result = $this->getCstmRelatives('Customer', $referent_model_types['referent_customers']);
                if (!empty($result)) {
                    // search once again to get only ids for models of active types that user has 'list' permissions for
                    $filters = array('sanitize' => true,
                                     'where' => array('c.id IN (' . implode(', ', $result) . ')'));
                    $result = Customers::getIds($registry, $filters);
                }
                $related['referent_customers'] = array('name' => 'referent_customers',
                                                       'label' => $this->i18n('menu_referent_customers'),
                                                       'link' => '#related_subpanel_customer' . $this->get('id'),
                                                       'ids' => is_array($result) ? $result : array());
            }
        }

        if (isset($rights[$this->module]['communications']) && $rights[$this->module]['communications'] != 'none' && $this->checkPermissions('communications')) {
            if (isset($rights[$this->module]['comments']) && $rights[$this->module]['comments'] != 'none' && $this->checkPermissions('comments')) {
                //get related comments
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_COMMENTS . "\n" .
                         'WHERE deleted_by = 0 AND model = \'Customer\' AND model_id = \'' . $this->get('id') . '\'' . ($registry['currentUser']->get('is_portal') ? ' AND is_portal = \'1\'' : '');

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=customers&amp;';
                $link .= 'customers=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=comments';
                $related['comments'] = array('name' => 'comments',
                                              'label' => $this->i18n('customers_comments'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }

            if (isset($rights[$this->module]['emails']) && $rights[$this->module]['emails'] != 'none' && $this->checkPermissions('emails')) {
                //get related e-mails
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_EMAILS_SENTBOX . "\n" .
                         'WHERE model = \'Customer\' AND model_id = ' . $this->get('id') . ' AND `system`=\'0\'' . "\n" .
                         'GROUP BY code';

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=customers&amp;';
                $link .= 'customers=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=emails';
                $related['emails'] = array('name' => 'email',
                                              'label' => $this->i18n('customers_emails'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }

            if ($this->checkPermissions('minitasks')) {
                //get related mini tasks
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_MINITASKS . ' AS m' . "\n" .
                         'WHERE model = \'\' AND model_id=0 AND customer=' . $this->get('id');

                $current_user_id = $registry['currentUser']->get('id');
                $rights = $registry['currentUser']->get('rights');
                $current_right = isset($rights['minitasks']['list']) ? $rights['minitasks']['list'] : '';
                unset($rights);
                if ($current_right == 'mine') {
                    $query .= " AND m.assigned_to=$current_user_id ";
                } elseif ($current_right == 'group') {
                    $query .= " AND (m.added_by=$current_user_id OR m.assigned_to=$current_user_id) ";
                } elseif ($current_right != 'all') {
                    $query .= ' AND 0';
                }

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=customers&amp;';
                $link .= 'customers=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=minitasks';
                $related['minitasks'] = array('name' => 'minitasks',
                                              'label' => $this->i18n('menu_minitasks'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }
        }

        if ($this->checkPermissions('attachments')) {
            //get related attachments
            $query = 'SELECT f.id' . "\n" .
                     'FROM ' . DB_TABLE_FILES . ' AS f ' . "\n" .
                     'WHERE f.deleted_by = 0 AND f.model = \'Customer\' AND f.model_id = ' . $this->get('id');
            //check access permissions of files
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            $query .= Files::getAdditionalWhere($registry);

            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=customers&amp;';
            $link .= 'customers=attachments&amp;attachments=' . $this->get('id');
            $related['files'] = array('name' => 'attachments',
                                      'label' => $this->i18n('attachments'),
                                      'link' => $link,
                                      'ids' => is_array($result) ? $result : array());
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        // reorder related records - if both records and referent records
        // of the same model are displayed, they should be in adjacent table cells
        $related_reordered = array();
        if (array_key_exists('customers', $related) && array_key_exists('referent_customers', $related)) {
            $related_reordered['customers'] = $related['customers'];
            $related_reordered['referent_customers'] = $related['referent_customers'];
            unset($related['customers']);
            unset($related['referent_customers']);
        }
        if (array_key_exists('documents', $related) && array_key_exists('referent_documents', $related)) {
            $related_reordered['documents'] = $related['documents'];
            $related_reordered['referent_documents'] = $related['referent_documents'];
            unset($related['documents']);
            unset($related['referent_documents']);
        }
        if (array_key_exists('projects', $related) && array_key_exists('referent_projects', $related)) {
            $related_reordered['projects'] = $related['projects'];
            $related_reordered['referent_projects'] = $related['referent_projects'];
            unset($related['projects']);
            unset($related['referent_projects']);
        }
        if (array_key_exists('contracts', $related) && array_key_exists('referent_contracts', $related)) {
            $related_reordered['contracts'] = $related['contracts'];
            $related_reordered['referent_contracts'] = $related['referent_contracts'];
            unset($related['contracts']);
            unset($related['referent_contracts']);
        }
        $related = array_merge($related_reordered, $related);

        return $related;
    }

    /**
     * Get patterns variables
     *
     * @param int $pattern_id - id of the pattern
     * @return array - variables
     */
    public function getPatternsVars($pattern_id = 0) {
        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array(
            'model_lang' => $this->get('model_lang'),
            'where' => array(
                'p.usage = \'patterns\'',
                'p.model IN ("Customer", "CurrentUser") OR p.type = "system"'
            )
        );
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //get the pattern
        $filters = array('where' => array('p.id = ' . $pattern_id),
                         'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        $pattern_format = '';
        if ($pattern) {
            $pattern_format = $pattern->get('format');
            $pattern_format = ($pattern_format == 'docx2pdf') ? 'docx' : $pattern_format;
        }

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('u.id = ' . $this->registry['currentUser']->get('id'), 'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $t_user = $t_customer = $t_a_vars = array();
        $user_translations = $user->getTranslations();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        $translations = $this->getTranslations();
        foreach ($translations as $t_lang) {
            if ($t_lang != $this->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('c.id = ' . $this->get('id')));
                $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
                $t_customer[$t_lang]->getVarsForTemplate();
                $t_a_vars[$t_lang] = $t_customer[$t_lang]->get('vars');
            }
        }

        //prepare basic/system variables
        $vars = array();

        foreach ($basic_placeholders as $placeholder) {
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'Customer') {
                //customer variables
                    if (!$placeholder->get('multilang')) {
                        if ($placeholder->get('source') == 'salutation') {
                            $salutation = '';
                            $vars[$placeholder->get('varname') . '_formal'] = '';

                            if ($this->get('salutation')) {
                                $salutation = $this->get('salutation');
                                $vars[$placeholder->get('varname') . '_formal'] =
                                    $salutation ? ($salutation == 'mr' ? $this->i18n('dear_m') : $this->i18n('dear_f')) : '';
                                $salutation = $this->i18n('salutation_vocative_' . $salutation) . ' ';
                            }
                            $salutation .= $this->get('lastname') ? $this->get('lastname') : $this->get('name');

                            $vars[$placeholder->get('varname')] = $salutation;
                        } else {
                            $vars[$placeholder->get('varname')] = $this->get($placeholder->get('source'));
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                $vars[ $t_customer[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')] =
                                (in_array($placeholder->get('source'),
                                    array('address', 'registration_address', 'address_by_personal_id'))) ?
                                nl2br($t_customer[$t_lang]->get($placeholder->get('source'))) :
                                $t_customer[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $this->get('model_lang') . '_' . $placeholder->get('varname')] =
                                (in_array($placeholder->get('source'),
                                    array('address', 'registration_address', 'address_by_personal_id'))) ?
                                nl2br($this->get($placeholder->get('source'))) :
                                $this->get($placeholder->get('source'));
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'CurrentUser') {
                //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$placeholder->get('varname')] = $user->get($placeholder->get('source'));
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[ $t_user[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                = $t_user[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $user->get('model_lang') . '_' . $placeholder->get('varname')] =
                                $user->get($placeholder->get('source'));
                            }
                        }
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
            //system variables
                if (strpos($placeholder->get('source'), '::')) {
                    list($method, $value) = preg_split('/\s*\::\s*/', $placeholder->get('source'));
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$placeholder->get('varname')] = $res;
                } else {
                    $vars[$placeholder->get('varname')] = $placeholder->get('source');
                }
            }
        }

        //prepare additional variables
        $this->getVarsForTemplate(false);

        //prepare BB variables
        $additional_vars = $this->get('vars');

        $bb_vars = $this->getBB(array('model_id' => $this->get('id')));

        $bb_elements = array();
        foreach ($additional_vars as $key => $var) {
            if (isset($var['bb']) && ($var['bb']) > 0 &&
                ($var['type'] == 'grouping' || $var['type'] == 'config' || $var['type'] == 'gt2')) {
                if ($var['type']!='gt2') {
                    $var['width'] = $var['width_print'];
                }
                $bb_elements[$var['id']] = $var;
                unset($additional_vars[$key]);
            }
        }

        // set additional vars back to model without the bb elements vars
        $this->set('vars', $additional_vars, true);

        $add_bb_vars = $this->getBBFields();
        foreach ($add_bb_vars as $bb_var_name => $bb_var_defs) {
            $add_bb_vars[$bb_var_name]['width'] = $bb_var_defs['width_print'];

            if ($bb_var_defs['type'] == 'file_upload') {
                if (!empty($bb_var_defs['value'])) {
                    foreach ($bb_var_defs['value'] as $bb_id => $file) {
                        // display thumbnail or file name
                        if (!empty($file) && is_object($file) && !$file->get('not_exist') && !$file->get('deleted_by')) {
                            $file = $this->getFileUploadForPrint($file, $bb_var_defs);
                        } else {
                            $file = '';
                        }
                        $add_bb_vars[$bb_var_name]['value'][$bb_id] = $file;
                    }
                }
            }
        }
        $this->set('add_bb_vars', $add_bb_vars, true);

        foreach ($bb_vars as $index => $var) {
            if (isset($bb_elements[$var['meta_id']])) {
                $bb_vars[$index] = $bb_elements[$var['meta_id']];
                $bb_vars[$index]['id'] = $var['id'];
                $bb_vars[$index]['meta_id'] = $var['meta_id'];
                $this->prepareBbVarValues($bb_vars[$index], $var['params'], true);

                // checks if source contains 'replace_value' params and
                // changes the name content with the required replacements
                if (isset($bb_elements[$var['meta_id']]['names'])) {
                    foreach ($bb_elements[$var['meta_id']]['names'] as $bb_sub_elements) {
                        if (isset($bb_elements[$var['meta_id']][$bb_sub_elements]) && !empty($bb_elements[$var['meta_id']][$bb_sub_elements]['source'])) {
                            // parse the params
                            $source_fields = General::parseSettings($bb_elements[$var['meta_id']][$bb_sub_elements]['source']);

                            // checks if 'replace_value' param is set
                            if (!empty($source_fields['replace_value'])) {
                                // find the replacement variable name
                                $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                                // check if there is a var with the required name and replace it
                                if (!empty($bb_vars[$index]['values'][$replaced_var])) {
                                    $bb_vars[$index]['values'][$bb_sub_elements] =
                                        str_replace('[a_' . $replaced_var . ']',
                                                    $bb_vars[$index]['values'][$bb_sub_elements],
                                                    $source_fields['replace_value']);

                                    // sets the option to overwrite a value and use it
                                    // but not searching for its corresponding label
                                    if ($bb_vars[$index][$bb_sub_elements]['type'] == 'dropdown' || $bb_vars[$index][$bb_sub_elements]['type'] == 'radio' || $bb_vars[$index][$bb_sub_elements]['type'] == 'checkbox') {
                                        $bb_vars[$index][$bb_sub_elements]['overwrite_value'] = true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        $this->set('bb_vars', $bb_vars, true);
        //end of preparation of BB variables

        $additional_vars = $this->get('vars');

        foreach ($additional_vars as $k => $a_var) {
            if (isset($a_var['type']) && !in_array($a_var['type'], array('bb', 'grouping', 'config', 'table', 'gt2'))) {
                if (!$a_var['multilang']) {
                    if (isset($a_var['value']) && $a_var['value'] !== '' && !is_array($a_var['value']) && isset($a_var['options'])) {
                        foreach ($a_var['options'] as $opt) {
                            if ($opt['option_value'] == $a_var['value']) {
                                $vars['a_' . $a_var['name']] =
                                    ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                break;
                            }
                        }
                    } elseif (isset($a_var['value']) && $a_var['value'] !== '' && is_array($a_var['value']) && isset($a_var['options'])) {
                        foreach ($a_var['value'] as $val) {
                            foreach ($a_var['options'] as $opt) {
                                if ($opt['option_value'] == $val) {
                                    $vars['a_' . $a_var['name']][] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        }
                    } elseif ($a_var['type'] == 'textarea') {
                        $vars['a_' . $a_var['name']] = nl2br($a_var['value']);
                    } elseif ($a_var['type'] == 'file_upload') {
                        if (!empty($a_var['value']) && is_object($a_var['value']) && !$a_var['value']->get('not_exist') && !$a_var['value']->get('deleted_by')) {
                            if (isset($a_var['view_mode']) && $a_var['view_mode'] == 'thumbnail' && $a_var['value']->isImage()) {
                                $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                    $_SERVER['SCRIPT_NAME'],
                                    $this->registry['module_param'],
                                    rawurlencode(General::encrypt($a_var['value']->get('id'), '_viewfile_', 'xtea')),
                                    (!empty($a_var['thumb_width']) ? ("&maxwidth=" . $a_var['thumb_width']) : ''),
                                    (!empty($a_var['thumb_height']) ? ("&maxheight=" . $a_var['thumb_height']) : '')
                                );
                            } else {
                                $value = $a_var['value']->get('name');
                            }
                        } else {
                            $value = '';
                        }

                        $vars['a_' . $a_var['name']] = $value;
                    } else {
                        $vars['a_' . $a_var['name']] = isset($a_var['value']) ? $a_var['value'] : '';
                    }
                } else {
                    foreach ($translations as $t_lang) {
                        if ($t_lang != $this->get('model_lang')) {
                            if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                $t_a_vars[$t_lang][$k]['value'] = '';
                            }
                            if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                            && isset($t_a_vars[$t_lang][$k]['options'])) {
                                foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                    if ($opt['option_value'] == $a_var['value']) {
                                        $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            } elseif ($a_var['type'] == 'textarea') {
                                //special behaviour for the textarea, they need new lines with breaks (<br />)
                                $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                                = nl2br($t_a_vars[$t_lang][$k]['value']);
                            } else {
                                $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                                = $t_a_vars[$t_lang][$k]['value'];
                            }
                        } else {
                            if ($a_var['value'] !== '' && !is_array($a_var['value']) && isset($a_var['options'])) {
                                foreach ($a_var['options'] as $opt) {
                                    if ($opt['option_value'] == $a_var['value']) {
                                        $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            } elseif ($a_var['type'] == 'textarea') {
                                //special behaviour for the textarea, they need new lines with breaks (<br />)
                                $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                nl2br($a_var['value']);
                            } else {
                                $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                $a_var['value'];
                            }
                        }
                    }
                }

                // checks if source contains 'replace_value' params and
                // changes the name content with the required replacements
                if (!empty($a_var['source'])) {
                    // parse the params
                    $source_fields = General::parseSettings($a_var['source']);

                    // check if there is a var with the required name and replace it
                    if (!empty($source_fields['replace_value'])) {
                        if (!$a_var['multilang']) {
                            $vars['a_' . $a_var['name']] =
                                str_replace('[a_' . $a_var['name'] . ']', $vars['a_' . $a_var['name']], $source_fields['replace_value']);
                        } else {
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                str_replace('[a_' . $a_var['name'] . ']', $vars[$this->get('model_lang') . '_a_' . $a_var['name']], $source_fields['replace_value']);
                        }
                    }
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'config' && isset($a_var['names']) && is_array($a_var['names'])) {
                //add containing variables to the list of replaceable variables
                $a_var['width'] = $a_var['width_print'];

                foreach ($a_var['names'] as $var_name) {
                    $ac_var = $a_var[$var_name];
                    $ac_var['value'] = isset($a_var['values'][$var_name]) ? $a_var['values'][$var_name] : '';

                    if (!$ac_var['multilang']) {
                        if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['options'] as $opt) {
                                if ($opt['option_value'] == $ac_var['value']) {
                                    $vars['a_'.$ac_var['name']] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        } elseif ($ac_var['value'] !== '' && is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['value'] as $val) {
                                foreach ($ac_var['options'] as $opt) {
                                    if ($opt['option_value'] == $val) {
                                        $vars['a_'.$ac_var['name']][] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            }
                            //special behaviour for checkboxes in a configurator (used as independent placeholders)
                            if ($ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$ac_var['name']]) && is_array($vars['a_'.$ac_var['name']])) {
                                if (!empty($ac_var['options_align']) && $ac_var['options_align'] == 'horizontal') {
                                    //separator is a space when options are aligned horizontally (options_align := horizontal)
                                    $vars['a_'.$ac_var['name']] = implode('&nbsp;', $vars['a_'.$ac_var['name']]);
                                } else {
                                    //separator is a break when options are aligned vertically (default)
                                    $vars['a_'.$ac_var['name']] = implode('<br />', $vars['a_'.$ac_var['name']]);
                                }
                            }
                        } elseif ($ac_var['type'] == 'textarea') {
                            //special behaviour for the textarea, they need new lines with breaks (<br />)
                            $vars['a_'.$ac_var['name']] = nl2br($ac_var['value']);
                        } elseif ($ac_var['type'] == 'file_upload') {
                            if (!empty($ac_var['value']) && is_object($ac_var['value']) && !$ac_var['value']->get('not_exist') && !$ac_var['value']->get('deleted_by')) {
                                if (isset($ac_var['view_mode']) && $ac_var['view_mode'] == 'thumbnail' && $ac_var['value']->isImage()) {
                                    $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                        $_SERVER['SCRIPT_NAME'],
                                        $this->registry['module_param'],
                                        rawurlencode(General::encrypt($ac_var['value']->get('id'), '_viewfile_', 'xtea')),
                                        (!empty($ac_var['thumb_width']) ? ("&maxwidth=" . $ac_var['thumb_width']) : ''),
                                        (!empty($ac_var['thumb_height']) ? ("&maxheight=" . $ac_var['thumb_height']) : '')
                                    );
                                } else {
                                    $value = $ac_var['value']->get('name');
                                }
                            } else {
                                $value = '';
                            }
                            $a_var['values'][$var_name] = $vars['a_'.$ac_var['name']] = $value;
                        } else {
                            $vars['a_'.$ac_var['name']] = $ac_var['value'];
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                    $t_a_vars[$t_lang][$k]['value'] = '';
                                }
                                if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                                && isset($t_a_vars[$t_lang][$k]['options'])) {
                                    foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = nl2br($t_a_vars[$t_lang][$k]['value']);
                                } else {
                                    $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = $t_a_vars[$t_lang][$k]['value'];
                                }
                            } else {
                                if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                                    foreach ($ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    nl2br($ac_var['value']);
                                } else {
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    $ac_var['value'];
                                }
                            }
                        }
                    }
                }

                $configViewer = new Viewer($this->registry);
                if (isset($a_var['frankenstein'])) {
                    $a_var['frankenstein']['columns'] = @$a_var['columns'];
                    $configViewer->data['var'] = $a_var['frankenstein'];
                    $configViewer->data['pattern_id'] = $pattern_id;

                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'franky_vars.html');
                    $vars['a_'.@$a_var['name']] = $configViewer->fetch();

                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'franky_configs.html');
                    $vars['a_'.@$a_var['name'].'_configs'] = $configViewer->fetch();
                } else {
                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'config_vars.html');
                    $configViewer->data['var'] = $a_var;
                    $configViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_'.@$a_var['name']] = $configViewer->fetch();
                }
                //ToDo - add multilang config variables

            } elseif (isset($a_var['type']) && $a_var['type'] == 'table' && isset($a_var['names']) && is_array($a_var['names'])) {
                // Skip this table if it's empty
                if (empty($a_var['values']) || count(array_filter($a_var['values'], function($a) {return !empty($a) && (is_object($a) || is_array($a) || !preg_match('#^0\.0+$#', $a));})) == 0) {
                    continue;
                }

                //add containing variables to the list of replaceable variables
                foreach ($a_var['names'] as $key => $var_name) {
                    $ac_var = $a_var[$var_name];
                    if ($ac_var['type'] == 'file_upload') {
                        if (!empty($a_var['values'][$key]) && is_object($a_var['values'][$key]) && !$a_var['values'][$key]->get('not_exist') && !$a_var['values'][$key]->get('deleted_by')) {
                            if (isset($ac_var['view_mode']) && $ac_var['view_mode'] == 'thumbnail' && $a_var['values'][$key]->isImage()) {
                                $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                    $_SERVER['SCRIPT_NAME'],
                                    $this->registry['module_param'],
                                    rawurlencode(General::encrypt($a_var['values'][$key]->get('id'), '_viewfile_', 'xtea')),
                                    (!empty($ac_var['thumb_width']) ? ("&maxwidth=" . $ac_var['thumb_width']) : ''),
                                    (!empty($ac_var['thumb_height']) ? ("&maxheight=" . $ac_var['thumb_height']) : '')
                                );
                                $a_var['values'][$key] = $value;
                            } else {
                                $a_var['values'][$key] = $a_var['values'][$key]->get('name');
                                $value = $a_var['values'][$key];
                            }
                        } else {
                            $value = '';
                        }
                    } else {
                        $value = isset($a_var['values'][$key]) ? $a_var['values'][$key] : '';
                    }
                    $ac_var['value'] = $value;
                    $a_var['values'][$key] = $value;

                    $a_var['width'] = $a_var['width_print'];

                    if (!$ac_var['multilang']) {
                        if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['options'] as $opt) {
                                if ($opt['option_value'] == $ac_var['value']) {
                                    $vars['a_'.$ac_var['name']] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        } elseif ($ac_var['value'] !== '' && is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['value'] as $val) {
                                foreach ($ac_var['options'] as $opt) {
                                    if ($opt['option_value'] == $val) {
                                        $vars['a_'.$ac_var['name']][] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            }
                            //special behaviour for checkboxes in a table (used as independent placeholders)
                            if ($ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$ac_var['name']]) && is_array($vars['a_'.$ac_var['name']])) {
                                if (!empty($ac_var['options_align']) && $ac_var['options_align'] == 'horizontal') {
                                    //separator is a space when options are aligned horizontally (options_align := horizontal)
                                    $vars['a_'.$ac_var['name']] = implode('&nbsp;', $vars['a_'.$ac_var['name']]);
                                } else {
                                    //separator is a break when options are aligned vertically (default)
                                    $vars['a_'.$ac_var['name']] = implode('<br />', $vars['a_'.$ac_var['name']]);
                                }
                            }
                        } elseif ($ac_var['type'] == 'textarea') {
                            //special behaviour for the textarea, they need new lines with breaks (<br />)
                            $vars['a_'.$ac_var['name']] = nl2br($ac_var['value']);
                        } else {
                            $vars['a_'.$ac_var['name']] = $ac_var['value'];
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                    $t_a_vars[$t_lang][$k]['value'] = '';
                                }
                                if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                                && isset($t_a_vars[$t_lang][$k]['options'])) {
                                    foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = nl2br($t_a_vars[$t_lang][$k]['value']);
                                } else {
                                    $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = $t_a_vars[$t_lang][$k]['value'];
                                }
                            } else {
                                if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                                    foreach ($ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    nl2br($ac_var['value']);
                                } else {
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    $ac_var['value'];
                                }
                            }
                        }
                    }

                    // checks if source contains 'replace_value' params and
                    // changes the name content with the required replacements
                    if (!empty($a_var['source'][$var_name])) {
                        // parse the params
                        $source_fields = General::parseSettings($a_var['source'][$var_name]);

                        // checks if 'replace_value' param is set
                        if (!empty($source_fields['replace_value'])) {
                            // find the replacement variable name
                            $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                            // check if there is a var with the required name and replace it
                            if ($replaced_var && in_array($replaced_var, $a_var['names'])) {
                                $additional_var_name = '[a_' . $replaced_var . ']';
                                $column_key_idx = array_search($replaced_var, $a_var['names']);
                                foreach ($a_var['values'] as $col_index => $col_value) {
                                    if ($col_index == $column_key_idx) {
                                        $new_value = str_replace($additional_var_name, $col_value, $source_fields['replace_value']);
                                        $a_var['values'][$col_index] = $new_value;
                                        $a_var[$replaced_var]['value'] = $new_value;
                                        if (!$ac_var['multilang']) {
                                            $vars['a_' . $replaced_var] = $new_value;
                                        } else {
                                            $vars[$this->get('model_lang') . '_a_' . $replaced_var] = $new_value;
                                        }

                                        // sets the option to overwrite a value and use it
                                        // but not searching for its corresponding label
                                        if ($ac_var['type'] == 'dropdown' || $ac_var['type'] == 'radio' || $ac_var['type'] == 'checkbox') {
                                            $a_var[$var_name]['overwrite_value'] = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                $template_file = 'table_vars' . ($pattern_format == 'docx' ? '_docx':'') . '.html';
                if (!empty($a_var['multilang'])) {
                    foreach ($translations as $t_lang) {
                        $tableViewer = new Viewer($this->registry);
                        $tableViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                        $tableViewer->data['pattern_id'] = $pattern_id;
                        $replacement = $tableViewer->fetch();
                        if ($pattern_format == 'docx') {
                            //remove whitespaces
                            $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                        }
                        if ($t_lang != $this->get('model_lang')) {
                            $tableViewer->data['var'] = $t_a_vars[$t_lang][$k];
                            $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] = $replacement;
                        } else {
                            $tableViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $replacement;
                        }
                    }
                } else {
                    $tableViewer = new Viewer($this->registry);
                    $tableViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                    $tableViewer->data['var'] = $a_var;
                    $tableViewer->data['pattern_id'] = $pattern_id;
                    $replacement = $tableViewer->fetch();
                    if ($pattern_format == 'docx') {
                        //remove whitespaces
                        $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                    }
                    $vars['a_'.$a_var['name']] = $replacement;
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'grouping') {
                // Skip this grouping table if it has no rows or it has only one row which is empty
                if (empty($a_var['values']) || count($a_var['values']) == 1 && count(array_filter(reset($a_var['values']), function($a) {return !empty($a) && (is_object($a) || is_array($a) || !preg_match('#^0\.0+$#', $a));})) == 0) {
                    continue;
                }

                $template_file = 'grouping_vars' . ($pattern_format == 'docx' ? '_docx':'') . '.html';
                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        $groupingViewer = new Viewer($this->registry);
                        $groupingViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                        $groupingViewer->data['pattern_id'] = $pattern_id;
                        if ($t_lang != $this->get('model_lang')) {
                            $groupingViewer->data['var'] = $t_a_vars[$t_lang][$k];
                            $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                            = $groupingViewer->fetch();
                        } else {
                            $groupingViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $groupingViewer->fetch();
                        }
                    }
                } else {
                    $groupingViewer = new Viewer($this->registry);
                    $groupingViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);

                    // checks if source contains 'replace_value' params and
                    // changes the name content with the required replacements
                    foreach ($a_var['names'] as $idx_var => $var_name) {
                        $a_var['width'] = $a_var['width_print'];

                        if (!empty($a_var['source'][$var_name])) {
                            // parse the params
                            $source_fields = General::parseSettings($a_var['source'][$var_name]);

                            // checks if 'replace_value' param is set
                            if (!empty($source_fields['replace_value'])) {
                                // find the replacement variable name
                                $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                                // check if there is a var with the required name and replace it
                                if ($replaced_var && in_array($replaced_var, $a_var['names'])) {
                                    $column_key_idx = array_search($replaced_var, $a_var['names']);
                                    foreach ($a_var['values'] as $row => $row_content) {
                                        $a_var['values'][$row][$column_key_idx] =
                                            str_replace('[a_' . $replaced_var . ']', $row_content[$column_key_idx], $source_fields['replace_value']);
                                    }

                                    // sets the option to overwrite a value and use it
                                    // but not searching for its corresponding label
                                    if (in_array($a_var['types'][$column_key_idx], array('dropdown', 'radio', 'checkbox'))) {
                                        $a_var[$var_name]['overwrite_value'] = true;
                                    }
                                }
                            }
                        }
                    }

                    //check if there are empty rows in the table and remove them
                    if (isset($a_var['values']) && is_array($a_var['values'])) {
                        $row_is_empty = true;
                        foreach ($a_var['values'] as $row_index => $row_content) {
                            foreach ($row_content as $cell_index => $cell_content) {
                                if ($cell_content || $cell_content === '0') $row_is_empty = false;
                            }
                            if ($row_is_empty) {
                                unset($a_var['values'][$row_index]);
                            } else {
                                $row_is_empty = true;
                            }
                        }
                    }

                    foreach ($a_var['types'] as $key_column => $var_type) {
                        if ($var_type == 'file_upload') {
                            $group_var_name = $a_var['names'][$key_column];
                            if (!empty($a_var['values'])) {
                                foreach ($a_var['values'] as $row => $row_values) {
                                    if (!empty($row_values[$key_column]) && is_object($row_values[$key_column]) && !$row_values[$key_column]->get('not_exist') && !$row_values[$key_column]->get('deleted_by')) {
                                        $file = $row_values[$key_column];
                                        if (isset($a_var[$group_var_name]['view_mode']) && $a_var[$group_var_name]['view_mode'] == 'thumbnail' && $row_values[$key_column]->isImage()) {
                                            $file_upload_properties = $a_var[$group_var_name];
                                            $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                                $_SERVER['SCRIPT_NAME'],
                                                $this->registry['module_param'],
                                                rawurlencode(General::encrypt($file->get('id'), '_viewfile_', 'xtea')),
                                                (!empty($file_upload_properties['thumb_width']) ? ("&maxwidth=" . $file_upload_properties['thumb_width']) : ''),
                                                (!empty($file_upload_properties['thumb_height']) ? ("&maxheight=" . $file_upload_properties['thumb_height']) : '')
                                            );
                                            $a_var['values'][$row][$key_column] = $value;
                                        } else {
                                            $a_var['values'][$row][$key_column] = $file->get('name');
                                        }
                                    } else {
                                        $a_var['values'][$row][$key_column] = '';
                                    }
                                }
                            }
                        }
                    }

                    $groupingViewer->data['var'] = $a_var;
                    $groupingViewer->data['pattern_id'] = $pattern_id;
                    $replacement = $groupingViewer->fetch();

                    if ($pattern_format == 'docx') {
                        //remove whitespaces
                        $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                    }
                    $vars['a_' . $a_var['name']] = $replacement;
                }
            /* ToDo: add GT2 support for customers
            } elseif (isset($a_var['type']) && $a_var['type'] == 'gt2') {
                //get print settings for the 2nd type grouping table
                $print_properties = $this->getGT2PrintSettings($pattern_id);

                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        //prepare additional variables
                        $groupingViewer = new Viewer($this->registry);
                        $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');

                        if ($t_lang != $this->get('model_lang')) {
                            $table = $t_a_vars[$t_lang][$k];
                        } else {
                            $table = $a_var;
                        }

                        //prepare files in GT2
                        if (in_array('file_upload', array_unique(array_column($table['vars'], 'type')))) {
                            foreach ($table['values'] as $ridx => $row) {
                                foreach ($row as $rkey => $rval) {
                                    if (!empty($rval) && is_object($rval)) {
                                        $file = $rval;
                                        if (!$file->get('not_exist') && !$file->get('deleted_by')) {
                                            $file = $this->getFileUploadForPrint($file, $table['vars'][$rkey]);
                                        } else {
                                            $file = '';
                                        }
                                        $table['values'][$ridx][$rkey] = $file;
                                    }
                                }
                            }
                        }

                        $table_ordered = $table;
                        $table_ordered['vars'] = array();
                        $styles_for_template = array();

                        foreach ($print_properties as $key => $property) {
                            // style properties
                            if (!empty($property['style'])) {
                                $styles_for_template[$key] = $property['style'];
                            }
                            // label for table caption
                            if ($key == 'var_' . $table['id']) {
                                if (isset($property['labels'][$t_lang])) {
                                    $table_ordered['label'] = $property['labels'][$t_lang];
                                }
                                continue;
                            }
                            foreach ($table['vars'] as $idx => $var) {
                                if ($key == 'var_' . $var['id']) {
                                    $table_ordered['vars'][$idx] = $var;
                                    // label for field
                                    if (isset($property['labels'][$t_lang])) {
                                        $table_ordered['vars'][$idx]['label'] = $property['labels'][$t_lang];
                                    }
                                    // aggregates
                                    if (isset($property['agregate'])) {
                                        if ($property['agregate'] != 'none') {
                                            $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                                        } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                            unset($table_ordered['vars'][$idx]['agregate']);
                                        }
                                    }
                                    continue 2;
                                }
                            }
                            foreach ($table['plain_vars'] as $idx => $var) {
                                if ($key == 'var_' . $var['id']) {
                                    // label for total field
                                    if (isset($property['labels'][$t_lang])) {
                                        $table_ordered['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                                    }
                                    continue 2;
                                }
                            }
                        }
                        // calculate aggregates in GT2 table
                        $table_ordered = $this->calculateGT2Agregates($table_ordered);

                        $groupingViewer->data['styles'] = $styles_for_template;
                        $groupingViewer->data['table'] = $table_ordered;
                        $groupingViewer->data['pattern_id'] = $pattern_id;
                        $vars[$t_lang . '_a_' . $a_var['name']] = $groupingViewer->fetch();
                    }
                } else {
                    //prepare additional variables
                    $groupingViewer = new Viewer($this->registry);
                    $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');

                    $table = $a_var;
                    $table_ordered = $table;
                    $table_ordered['vars'] = array();
                    $styles_for_template = array();

                    foreach ($print_properties as $key => $property) {
                        // style properties
                        if (!empty($property['style'])) {
                            $styles_for_template[$key] = $property['style'];
                        }
                        // label for table caption
                        if ($key == 'var_' . $table['id']) {
                            if (!empty($property['labels'])) {
                                $table_ordered['label'] = reset($property['labels']);
                            }
                            continue;
                        }
                        foreach ($table['vars'] as $idx => $var) {
                            if ($key == 'var_' . $var['id']) {
                                $table_ordered['vars'][$idx] = $var;
                                // label for field
                                if (!empty($property['labels'])) {
                                    $table_ordered['vars'][$idx]['label'] = reset($property['labels']);
                                }
                                // aggregates
                                if (isset($property['agregate'])) {
                                    if ($property['agregate'] != 'none') {
                                        $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                                    } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                        unset($table_ordered['vars'][$idx]['agregate']);
                                    }
                                }
                                continue 2;
                            }
                        }
                        foreach ($table['plain_vars'] as $idx => $var) {
                            if ($key == 'var_' . $var['id']) {
                                // label for total field
                                if (!empty($property['labels'])) {
                                    $table_ordered['plain_vars'][$idx]['label'] = reset($property['labels']);
                                }
                                continue 2;
                            }
                        }
                    }
                    // calculate aggregates in GT2 table
                    $table_ordered = $this->calculateGT2Agregates($table_ordered);

                    $groupingViewer->data['styles'] = $styles_for_template;
                    $groupingViewer->data['table'] = $table_ordered;
                    $groupingViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_' . $a_var['name']] = $groupingViewer->fetch();
                }

                //get the plain vars of the GT2
                $plain_values = $a_var['plain_values'];
                foreach ($plain_values as $plain_var => $plain_value) {
                    switch ($plain_var) {
                    case 'total_no_vat_reason_text':
                        if ($a_var['plain_vars'][$plain_var]['multilang']) {
                            foreach ($translations as $t_lang) {
                                $vars[$t_lang . '_a_' . $plain_var] =
                                    $a_var['multilang'] && $t_lang != $this->get('model_lang') ?
                                    $t_a_vars[$t_lang][$k]['plain_values'][$plain_var] :
                                    $plain_value;
                            }
                        } else {
                            $vars['a_' . $plain_var] = $plain_value;
                        }
                        break;
                    case 'total_vat_rate':
                        $vars['a_' . $plain_var] = $plain_value . ' %';
                        break;
                    default:
                        $vars['a_' . $plain_var] = $plain_value;
                    }
                }
            */
            } elseif (isset($a_var['type']) && $a_var['type'] == 'bb') {
                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        $bbViewer = new Viewer($this->registry);
                        $bbViewer->setFrameset($this->registry['theme']->templatesDir . 'bb_vars.html');

                        /* ToDo: add GT2 support for customers

                        //get print settings for the 2nd type grouping table
                        $print_properties = $this->getGT2PrintSettings($pattern_id);

                        $styles_for_template = array();

                        foreach ($print_properties as $key => $property) {
                            if (!empty($property['style'])) {
                                $styles_for_template[$key] = $property['style'];
                            }
                        }

                        $bbViewer->data['styles'] = $styles_for_template;

                        $bbViewer->data['customer'] = $this;

                        // complete the labels from the printing properties
                        $new_bb_vars = $this->get('bb_vars');
                        foreach ($new_bb_vars as $bb_idx => $bb_details) {
                            // if the variable is GT2
                            if ($bb_details['type'] == 'gt2') {
                                foreach ($bb_details['vars'] as $var_nm => $var_details) {
                                    $print_properties_key = 'var_' . $var_details['id'];
                                    if (array_key_exists($print_properties_key, $print_properties)) {
                                        $property = $print_properties[$print_properties_key];
                                        // label
                                        if (!empty($property['labels'][$t_lang])) {
                                            $new_bb_vars[$bb_idx]['vars'][$var_nm]['label'] = $property['labels'][$t_lang];
                                        }
                                        // aggregates
                                        if (isset($property['agregate'])) {
                                            if ($property['agregate'] != 'none') {
                                                $new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'] = $property['agregate'];
                                            } elseif (isset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'])) {
                                                unset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate']);
                                            }
                                        }
                                    }
                                }
                                foreach ($bb_details['plain_vars'] as $var_nm => $var_details) {
                                    $print_properties_key = 'var_' . $var_details['id'];
                                    // label
                                    if (array_key_exists($print_properties_key, $print_properties) && !empty($print_properties[$print_properties_key]['labels'][$t_lang])) {
                                        $new_bb_vars[$bb_idx]['plain_vars'][$var_nm]['label'] = $print_properties[$print_properties_key]['labels'][$t_lang];
                                    }
                                }
                                // calculate aggregates in GT2 table
                                $new_bb_vars[$bb_idx] = $this->calculateGT2Agregates($new_bb_vars[$bb_idx]);
                            }
                        }
                        $bbViewer->data['document']->set('bb_vars', $new_bb_vars, true); */

                        $bbViewer->data['pattern_id'] = $pattern_id;

                        if ($t_lang != $this->get('model_lang')) {
                            $bbViewer->data['var'] = @$t_a_vars[$t_lang][$k];
                            $vars[$t_customer[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] = $bbViewer->fetch();
                        } else {
                            $bbViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $bbViewer->fetch();
                        }
                    }
                } else {
                    $bbViewer = new Viewer($this->registry);
                    $bbViewer->setFrameset($this->registry['theme']->templatesDir . 'bb_vars.html');
                    $bbViewer->data['document'] = $this;
                    $bbViewer->data['var'] = $a_var;

                    /* ToDo: add GT2 support for customers

                    //get print settings for the 2nd type grouping table
                    $print_properties = $this->getGT2PrintSettings($pattern_id);

                    // complete the labels from the printing properties
                    $new_bb_vars = $this->get('bb_vars');
                    foreach ($new_bb_vars as $bb_idx => $bb_details) {
                        // if the variable is GT2
                        if ($bb_details['type'] == 'gt2') {
                            foreach ($bb_details['vars'] as $var_nm => $var_details) {
                                $print_properties_key = 'var_' . $var_details['id'];
                                if (array_key_exists($print_properties_key, $print_properties)) {
                                    $property = $print_properties[$print_properties_key];
                                    // label
                                    if (!empty($property['labels'])) {
                                        $new_bb_vars[$bb_idx]['vars'][$var_nm]['label'] = reset($property['labels']);
                                    }
                                    // aggregates
                                    if (isset($property['agregate'])) {
                                        if ($property['agregate'] != 'none') {
                                            $new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'] = $property['agregate'];
                                        } elseif (isset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'])) {
                                            unset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate']);
                                        }
                                    }
                                }
                            }
                            foreach ($bb_details['plain_vars'] as $var_nm => $var_details) {
                                $print_properties_key = 'var_' . $var_details['id'];
                                // label
                                if (array_key_exists($print_properties_key, $print_properties) && !empty($print_properties[$print_properties_key]['labels'])) {
                                    $new_bb_vars[$bb_idx]['plain_vars'][$var_nm]['label'] = reset($print_properties[$print_properties_key]['labels']);
                                }
                            }
                            // calculate aggregates in GT2 table
                            $new_bb_vars[$bb_idx] = $this->calculateGT2Agregates($new_bb_vars[$bb_idx]);
                        }
                    }
                    $bbViewer->data['customer']->set('bb_vars', $new_bb_vars, true);

                    $styles_for_template = array();

                    foreach ($print_properties as $key => $property) {
                        if (!empty($property['style'])) {
                            $styles_for_template[$key] = $property['style'];
                        }
                    }
                    $bbViewer->data['styles'] = $styles_for_template;
                    */

                    $bbViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_' . $a_var['name']] = $bbViewer->fetch();
                }
            }
        }

        return $vars;
    }

    /**
     * Gets branches of customer
     *
     * @return array - array with branches objects
     */
    public function getBranches() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        require_once 'customers.branches.factory.php';
        $filters = array('where' => array('c.subtype = \'branch\'',
                                          'c.parent_customer = ' . $this->get('id'),
                                          'c.active = 1'),
                         'sort' => array('c.is_main DESC', 'ci18n.name ASC', 'c.id DESC'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => true);
        $branches = Customers_Branches::search($this->registry, $filters);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $branches;
    }

    /**
     * Gets contact persons of customer for some or all branches
     *
     * @param mixed $branches - branches of customer where contact persons should belong to
     * @param bool $skip_permissions - optional; if true - access permissions for contact persons are not checked (used in Communications autocompleter)
     * @return array - array with results
     */
    public function getContactPersons($branches = array(), $skip_permissions = false) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        require_once 'customers.contactpersons.factory.php';
        $parents = array();
        if (empty($branches)) {
            $branches = $this->getBranches();
            foreach ($branches as $branch) {
                $parents[] = $branch->get('id');
            }
        } else {
            if (is_array($branches)) {
                $parents = $branches;
            } else {
                $parents[] = $branches;
            }
        }

        $contact_persons = array();
        foreach ($parents as $parent) {
            $filters = array('where' => array('c.subtype = \'contact\'',
                                              'c.parent_customer = ' . $parent,
                                              'c.active = 1'),
                             'sort' => array('c.is_main DESC', 'CONCAT_WS(\' \', ci18n.name, ci18n.lastname) ASC', 'c.id DESC'),
                             'model_lang' => $this->get('model_lang'),
                             'sanitize' => true,
                             'skip_permissions' => $skip_permissions);
            if ($this->get('financial_persons')) {
                $filters['where'][] = 'c.financial_person = 1';
            }
            $contact_persons[$parent] = Customers_Contactpersons::search($this->registry, $filters);
        }
        if ($sanitize_after) {
            $this->sanitize();
        }

        return $contact_persons;
    }

    /**
     * Gets trademarks of customer and sets them to model
     *
     * @return array - found trademarks
     */
    public function getTrademarks() {
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $query = 'SELECT n.id AS idx, ct.is_default, n.id, n.code, ni18n.name ' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS_TRADEMARKS . ' AS ct ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                 '  ON ct.trademark_id = n.id' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                 '  ON (n.id=ni18n.parent_id AND ni18n.lang = \'' . $this->get('model_lang') . '\')' . "\n" .
                 'WHERE ct.parent_id = \'' . $this->get('id') . '\'' . "\n" .
                 'ORDER BY ct.is_default DESC, ni18n.name ASC';
        $trademarks = $this->registry['db']->GetAssoc($query);
        if (!empty($sanitize_after)) {
            $this->sanitize();
        }
        $this->set('trademarks', $trademarks, true);
        return $trademarks;
    }

    /**
     * Saves trademarks of customer
     * IMPORTANT: values are taken from the session filters of trademarks subpanel
     *
     * @return bool - result of the operation
     */
    public function saveTrademarks() {
        $session_prefix = 'customer' . $this->get('id') . '_ajax_trademark_';
        $session_param = $session_prefix . 'nomenclature';

        if (!$this->registry['session']->get($session_param)) {
            return false;
        }

        // get default trademark from session search filters
        $default_trademark = $this->registry['session']->get('default_trademark', $session_param);

        $this->registry['request']->set('session_param', $session_param, '', true);

        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        $filters = Nomenclatures::saveSearchParams($this->registry, array(), $session_prefix);

        $this->registry['request']->remove('session_param');

        $this->registry['session']->set('default_trademark', $default_trademark, $session_param, true);

        $db = &$this->registry['db'];

        $db->StartTrans();

        $query = 'DELETE FROM ' . DB_TABLE_CUSTOMERS_TRADEMARKS . "\n" .
                 'WHERE parent_id = \'' . $this->get('id') . '\'';
        $db->Execute($query);

        $set = array();
        foreach (Nomenclatures::getIds($this->registry, $filters) as $trademark_id) {
            $set[] = sprintf('(%d, %d, %d)', $this->get('id'), $trademark_id, $trademark_id == $default_trademark);
        }
        if ($set) {
            $query = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_TRADEMARKS . ' (parent_id, trademark_id, is_default) VALUES ' . "\n" .
                     implode("\n,", $set);
            $db->Execute($query);
        }

        /* if (!$db->HasFailedTrans()) {
            $this->registry['session']->remove($session_param);
        } */

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Gets all e-mails of customer + branches + contact persons.
     * Function is used for emails targetlists.
     *
     * @param bool $detailed - if true, option value contains id and subtype of model
     * @return - array with results
     */
    public function getAllEmails($detailed = true) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $mail_options = array();
        if ($this->get('is_company')) {
            $branches = $this->get('financial_persons') ? array() : $this->getBranches();
            $contacts = $this->getContactPersons(array_map(function($a) { return $a->get('id'); }, $branches));

            foreach ($contacts as $branch) {
                foreach ($branch as $contact) {
                    $mails = $contact->get('email');
                    if (is_array($mails)) {
                        $og = $contact->get('name');
                        if ($contact->get('lastname')) {
                            $og .= ' ' . $contact->get('lastname');
                        }
                        if ($this->get('for_campaign')) {
                            Emails_Targetlists::filterUnsubscribed($this->registry, $mails);
                        }
                        foreach ($mails as $mail) {
                            if ($detailed) {
                                $mail_options[$og][] = array('label' => $mail,
                                                             'option_value' => $mail . '(' . 'contact_' . $contact->get('id') . ')',
                                                             'class_name' => ($contact->get('financial_person') ? 'financial_person' : '')
                                );
                            } else {
                                $mail_options[$og][] = array('label' => $mail,
                                                             'option_value' => $mail,
                                                             'class_name' => ($contact->get('financial_person') ? 'financial_person' : ''));
                            }
                        }
                    }
                }
            }
            foreach ($branches as $branch) {
                $mails = $branch->get('email');
                if (is_array($mails)) {
                    $og = $branch->get('name');
                    if ($branch->get('lastname')) {
                        $og .= ' ' . $branch->get('lastname');
                    }
                    if ($this->get('for_campaign')) {
                        Emails_Targetlists::filterUnsubscribed($this->registry, $mails);
                    }
                    foreach ($mails as $mail) {
                        if ($detailed) {
                            $mail_options[$og][] = array('label' => $mail,
                                                         'option_value' => $mail . '(' . 'branch_' . $branch->get('id') . ')');
                        } else {
                            $mail_options[$og][] = array('label' => $mail,
                                                         'option_value' => $mail);
                        }
                    }
                }
            }
        } elseif (!$this->get('financial_persons')) {
            $mails = $this->get('email');
            if (is_array($mails)) {
                $og = $this->get('name');
                if ($this->get('lastname')) {
                    $og .= ' ' . $this->get('lastname');
                }
                if ($this->get('for_campaign')) {
                    Emails_Targetlists::filterUnsubscribed($this->registry, $mails);
                }
                foreach ($mails as $mail) {
                    if ($detailed) {
                        $mail_options[$og][] = array('label' => $mail,
                                                     'option_value' => $mail . '(' . 'customer_' . $this->get('id') . ')');
                    } else {
                        $mail_options[$og][] = array('label' => $mail, 'option_value' => $mail);
                    }
                }
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $mail_options;
    }

    /**
     * Sends notification when the customer is assigned to user
     */
    public function sendAssignNotification() {
        //prepare mail to be sent to the assigned user
        $template = 'customer_assign';

        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        //get ids of users who ignore this type of email
        $not_users = Users::getUsersNoSend($this->registry, $template);

        if (!in_array($this->get('assigned'), $not_users)) {
            // fill the placeholders
            $mailer = new Mailer($this->registry, $template, $this);
            $mailer->placeholder->add('user_name', $this->get('assigned_to_name'));
            $mailer->placeholder->add('customer_name', $this->get('name') . (!$this->get('is_company') ? ' ' . $this->get('lastname') : ''));
            $mailer->placeholder->add('customer_code', $this->get('code'));
            $mailer->placeholder->add('customer_type', $this->getModelTypeName());
            $mailer->placeholder->add('customer_added_by', $this->get('added_by_name'));

            $customer_view_url = sprintf('%s/index.php?%s=customers&customers=view&view=%d',
                                        $this->registry['config']->getParam('crontab', 'base_host'),
                                        $this->registry['module_param'], $this->get('id'));
            $mailer->placeholder->add('customer_view_url', $customer_view_url);

            $add_comment_url = sprintf('%s/index.php?%s=customers&customers=communications&communications=%d&communication_type=comments#comments_add_form',
                                        $this->registry['config']->getParam('crontab', 'base_host'),
                                        $this->registry['module_param'], $this->get('id'));
            $mailer->placeholder->add('customer_add_comment_url', $add_comment_url);

            $mailer->placeholder->add('customer_id', $this->get('id'));
            $mailer->placeholder->add('user_lastname', $this->registry['currentUser']->get('lastname'));
            $mailer->placeholder->add('user_firstname', $this->registry['currentUser']->get('firstname'));

            $mailer->template['model_name'] = $this->modelName;
            $mailer->template['model_id'] = $this->get('id');

            // get the user email
            $query = 'SELECT email FROM ' . DB_TABLE_USERS . ' WHERE id=' . $this->get('assigned');
            $assignment_email = $this->registry['db']->GetOne($query);

            if ($assignment_email) {
                $mailer->placeholder->add('to_email', $assignment_email);
                $mailer->placeholder->add('user_name', $this->get('assigned_to_name'));

                //send email
                $result = $mailer->send();
                if (!@in_array($assignment_email, $result['erred'])) {
                    $notify_for = $this->i18n($template . '_notify', array($this->getModelTypeName()));
                    $this->registry['messages']->setMessage($this->i18n('names_users_notified', array($notify_for, $this->get('assigned_to_name'))));
                }
            }
        }
    }

    public function getExportBasicVarValue(string $field_name): string
    {
        switch($field_name) {
            case "name":
                $salutation = $this->get('salutation') ? $this->i18n('salutation_' . $this->get('salutation')) : '';
                return trim(sprintf('%s %s %s', $salutation, $this->get('name'), $this->get('lastname')));

            /* simple text fields */
            case "notes":
            case "description":
            case "full_num":
                return $this->get($field_name);

            case "name_full_num":
                return "[{$this->get('full_num')}] {$this->getExportBasicVarValue('name')}";

            /* XXX_name_code fields */
            case 'name_code':
                $code = $this->get('code');
                return $code ? "[{$code}] {$this->getExportBasicVarValue('name')}" : '';

            case 'company_name_code':
                $code = $this->get('code');
                return $code ? "[{$code}] {$this->getExportBasicVarValue('name')}" : '';

            case 'employee':
                $employee = $this->get('employee');
                var_dump($employee); exit();
                if ($employee && is_object($employee) ) {
                    return $employee->get('name');
                }
                return $employee;

            /* simple {field}_name types */
            case "type":
            case "main_trademark":
            case "company":
            case "department":
            case "added_by":
            case "modified_by":
            case "deleted_by":
                return $this->get("{$field_name}_name");

            case "employee":
                $employee = $this->get('employee_name');
                if (is_array($employee)) {
                    return $employee['value']??'';
                }
                return $employee;

            //case "emails": break;
            //case "email": break;
            //case "comments": break;
            //case "history_activity": break;

            // date fields handled in Model
            case 'added':
            case 'created':
            case 'modified':
            case 'deleted':
                $value = $this->get($field_name);
                if (preg_match('#^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$#', $value)) {
                    return General::strftime('%d.%m.%Y %H:%M', $value);
                }

                if (preg_match('#^\d{4}-\d{2}-\d{2}$#', $value)) {
                    return General::strftime('%d.%m.%Y', $value);
                }

                return '';
        }

        return parent::getExportBasicVarValue($field_name);
    }
}

?>
