<?php

namespace Nzoom\Export;

use Nzoom\Export\Adapter\ExportFormatAdapterInterface;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Factory\ExportFormatFactory;
use Nzoom\I18n\I18nAwareTrait;

/**
 * Service for handling grid exports
 */
class ExportService
{
    use I18nAwareTrait;
    /**
     * @var \Registry
     */
    protected $registry;

    /**
     * @var string
     */
    protected $module;

    /**
     * @var string
     */
    protected $controller;

    /**
     * @var string
     */
    protected $modelName;

    /**
     * @var string
     */
    protected $modelFactoryName;

    /**
     * @var array
     */
    protected $systemExportModules = [];

    /**
     * @var ExportFormatFactory
     */
    protected ExportFormatFactory $formatFactory;

    /**
     * @var string
     */
    private string $type;

    /**
     * @var ExportFormatAdapterInterface
     */
    private ExportFormatAdapterInterface $adapter;

    /**
     * @var array Reference column mapping by model class
     */
    private static array $referenceColumnMap = [
        'Document' => ['name' => 'full_num', 'label' => 'Document Number'],
        'Customer' => ['name' => 'code', 'label' => 'Customer Code'],
    ];

    /**
     * ExportService constructor.
     *
     * @param \Registry $registry
     * @param string $module
     * @param string $controller
     * @param string $type The export type/format (e.g., 'csv', 'excel', 'pdf')
     */
    public function __construct(\Registry $registry, string $module, string $controller, string $type)
    {
        $this->registry = $registry;
        $this->module = $module;
        $this->controller = $controller;
        $this->type = $type;
        $this->setTranslator($registry['translater']);
    }

    /**
     * Set the model name
     *
     * @param string $modelName
     * @return $this
     */
    public function setModelName(string $modelName)
    {
        $this->modelName = $modelName;
        return $this;
    }

    /**
     * Set the model factory name
     *
     * @param string $modelFactoryName
     * @return $this
     */
    public function setModelFactoryName(string $modelFactoryName)
    {
        $this->modelFactoryName = $modelFactoryName;
        return $this;
    }

    /**
     * Create export action
     *
     * @param string $module_check
     * @param array $types
     * @param array $typeSections
     * @return array|null
     */
    public function createExportAction(string $module_check, array $types, array $typeSections): ?array
    {
        // Create and configure the factory
        $factory = new ExportActionFactory(
            $this->registry,
            $this->module,
            $this->controller,
            $this->modelName,
            $this->modelFactoryName,
            $this->registry['translater']
        );

        // Use the factory to create the export action
        return $factory($module_check, $types, $typeSections);
    }


    public function createExportData(\Outlook $outlook, array $filters, string $modelClass, $pageSize = 200): ExportData
    {
        $dataFactory = new DataFactory($this->registry);
        return $dataFactory->createStreaming($modelClass, $filters, $outlook, $pageSize);
    }

    public function createExportDataWithTables(\Outlook $outlook, array $filters, string $factoryClass, $pageSize = 200): ExportData
    {
        $dataFactory = new DataFactory($this->registry);

        // Get reference column configuration for the model class
        $referenceColumn = $this->getReferenceColumnForModel($factoryClass::$modelName);
        $dataFactory->withModelTableProvider($referenceColumn['name'], $referenceColumn['label']);

        return $dataFactory->createStreaming($factoryClass, $filters, $outlook, $pageSize);
    }

    /**
     * Create a GeneratorFileStreamer instance
     *
     * @param callable $generatorFunction Function that returns a generator
     * @param string $filename The filename to present to the browser
     * @param string $mimeType The MIME type for the content
     * @param int|null $totalSize Total size in bytes if known
     * @return \Nzoom\Export\Streamer\GeneratorFileStreamer
     */
    public function createGeneratorFileStreamer(callable $generatorFunction, string $filename, string $mimeType = 'application/octet-stream', ?int $totalSize = null): \Nzoom\Export\Streamer\GeneratorFileStreamer
    {
        return new \Nzoom\Export\Streamer\GeneratorFileStreamer($generatorFunction, $filename, $mimeType, $totalSize);
    }

    /**
     * Export data using the configured type
     *
     * @param string $filename The filename to present to the browser
     * @param ExportData $data The export data to process
     * @param array $options Export options (e.g., sizing constraints)
     * @return void
     * @throws \Exception
     */
    public function export(string $filename, ExportData $data, array $options = []): void
    {
        // Use adapter pattern with temp stream + streaming approach
        try {
            // Generate export to temporary stream
            $tempStream = $this->createTempStream();

            $adapter = $this->getAdapter();
            $adapter->export($tempStream, $this->type, $data, $options);

            // Stream to browser
            $this->streamToBrowser($tempStream, $filename, $adapter->getMimeType($this->type));
        } catch (\InvalidArgumentException $e) {
            $this->handleExportError('Unsupported export format: ' . $this->type);
        } catch (\Exception $e) {
            $this->handleExportError('Export failed: ' . $e->getMessage());
        }
    }

    /**
     * Create a temporary stream for export generation
     *
     * @return resource A writable temporary stream
     * @throws \Exception If temp stream cannot be created
     */
    private function createTempStream()
    {
        $tempStream = fopen('php://temp', 'w+');

        if ($tempStream === false) {
            throw new \Exception('Failed to create temporary stream for export');
        }

        return $tempStream;
    }

    /**
     * Stream data to the browser using PointerFileStreamer
     *
     * @param resource $stream The stream containing the export data
     * @param string $downloadFilename Filename to present to the browser
     * @param string $mimeType MIME type for the file
     * @return void
     * @throws \Exception If stream cannot be sent
     */
    private function streamToBrowser($stream, string $downloadFilename, string $mimeType): void
    {
        if (!is_resource($stream)) {
            throw new \Exception('Invalid stream provided for streaming');
        }

        try {
            // Create and use PointerFileStreamer
            $streamer = new \Nzoom\Export\Streamer\PointerFileStreamer(
                $stream,
                $downloadFilename,
                $mimeType
            );

            // Stream the data (this will handle cleanup and exit)
            $streamer->stream();

        } finally {
            // Clean up stream
            if (is_resource($stream)) {
                fclose($stream);
            }
        }
    }

    /**
     * Get the export format factory
     * Creates the factory automatically if it doesn't exist
     *
     * @return ExportFormatFactory
     */
    public function getFormatFactory(): ExportFormatFactory
    {
        if (!isset($this->formatFactory)) {
            $this->formatFactory = new ExportFormatFactory($this->registry, $this->module, $this->controller);
        }

        return $this->formatFactory;
    }

    /**
     * Set the export format factory
     *
     * @param ExportFormatFactory $formatFactory
     * @return $this
     */
    public function setFormatFactory(ExportFormatFactory $formatFactory): self
    {
        $this->formatFactory = $formatFactory;
        return $this;
    }

    /**
     * Get the export adapter for the configured type
     * Creates the adapter automatically if it doesn't exist
     *
     * @return ExportFormatAdapterInterface
     */
    public function getAdapter(): ExportFormatAdapterInterface
    {
        if (!isset($this->adapter)) {
            $this->adapter = $this->getFormatFactory()->createAdapter($this->type, ['extension' => $this->type]);
        }

        return $this->adapter;
    }

    /**
     * Get supported export formats
     *
     * @return array
     */
    public function getSupportedFormats(): array
    {
        return $this->getFormatFactory()->getSupportedFormats();
    }

    /**
     * Check if a format is supported
     *
     * @param string $format
     * @return bool
     */
    public function isFormatSupported(string $format): bool
    {
        return $this->getFormatFactory()->isFormatSupported($format);
    }

    /**
     * Get reference column configuration for a model class
     *
     * @param string $modelClass The model class name (e.g., 'Document', 'Customer')
     * @return array Reference column configuration with 'name' and 'label' keys
     */
    private function getReferenceColumnForModel(string $modelClass): array
    {
        // Check if we have a mapping for this model class
        if (isset(self::$referenceColumnMap[$modelClass])) {
            return self::$referenceColumnMap[$modelClass];
        }

        // No mapping found - throw exception
        throw new \InvalidArgumentException("No reference column mapping found for model class: {$modelClass}");
    }

    /**
     * Get export filename
     *
     * @param string|array|null $prefix Filename prefix
     * @param string $extension File extension without dot
     * @return string Filename
     */
    protected function getExportFilename($prefix, string $extension)
    {
        // Get filename from request or generate one
        $filename = $prefix;

        // Handle array or null cases
        if (is_array($filename)) {
            $filename = $filename[0] ?? '';
        }

        if (empty($filename)) {
            $filename = strtolower($this->module . '_' . $this->controller . '_export_' . date('Y-m-d_H-i-s'));
        }

        // Add extension if not present
        if (substr($filename, -(strlen($extension) + 1)) !== '.' . $extension) {
            $filename .= '.' . $extension;
        }

        return $filename;
    }

    /**
     * Handle export error
     *
     * @param string $message Error message
     * @param int $statusCode HTTP status code to use (default: 400)
     * @return void
     * @throws \Exception
     */
    protected function handleExportError($message, $statusCode = 400)
    {
        // Log the error if logger is available
        if (isset($this->registry['logger'])) {
            $this->registry['logger']->error('Export error: ' . $message);
        }

        // Set error message in registry for AJAX response
        $this->registry->set('ajax_result', json_encode([
            'error' => $message,
            'status' => 'error',
            'timestamp' => date('Y-m-d H:i:s')
        ]), true);

        // If this is an AJAX request, send appropriate headers
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {

            http_response_code($statusCode);
            header('Content-Type: application/json');
            echo $this->registry->get('ajax_result');
            exit;
        }

        // For non-AJAX requests, we'll let the controller handle the response
    }
}
