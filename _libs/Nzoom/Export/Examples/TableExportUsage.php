<?php

namespace Nzoom\Export\Examples;

use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Entity\ExportTable;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Provider\ModelTableProvider;
use Nzoom\Export\Adapter\ExcelExportFormatAdapter;

/**
 * Class TableExportUsage
 *
 * Examples of how to use the table export functionality
 */
class TableExportUsage
{
    /**
     * Example 1: Basic table export setup (AUTO-DISCOVERY APPROACH)
     */
    public static function basicTableExport(\Registry $registry, array $models, \Outlook $outlook)
    {
        // Create DataFactory and enable auto-discovery of tables
        $dataFactory = new \Nzoom\Export\DataFactory($registry);

        // Configure table provider with options
        $dataFactory->withModelTableProvider('full_num', 'Document Number', [
            'include_empty_tables' => false,
            'date_format' => 'd.m.Y'
        ]);

        // Tables are automatically discovered from models with 'type' => 'grouping' variables
        $exportData = $dataFactory($models, $outlook);

        // Export to Excel with tables - all grouping variables are automatically included!
        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('customer_export_with_tables.xlsx', 'xlsx', $exportData);
    }

    /**
     * Example 1b: Alternative fluent approach
     */
    public static function fluentTableExport(\Registry $registry, array $models, \Outlook $outlook)
    {
        $dataFactory = new \Nzoom\Export\DataFactory($registry);

        // Configure table provider options first, then create export data
        $exportData = $dataFactory
            ->withModelTableProvider('customer_id', 'Customer ID', [
                'include_empty_tables' => true,
                'datetime_format' => 'd.m.Y H:i'
            ])
            ->__invoke($models, $outlook); // Or just call the factory

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('fluent_export.xlsx', 'xlsx', $exportData);
    }

    /**
     * Example 2: Manual table creation
     */
    public static function manualTableCreation(\Registry $registry)
    {
        // Create main export data
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('Customer Name', 'name', 'Customer Name'));
        $header->addColumn(new ExportColumn('Email', 'email', 'Email'));

        $exportData = new ExportData($header);

        // Create a main record
        $record = new ExportRecord();
        $record->addValue('name', 'John Doe', ExportValue::TYPE_STRING);
        $record->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);

        // Create purchase table manually
        $purchaseHeader = new ExportHeader();
        $purchaseHeader->addColumn(new ExportColumn('Item', 'item', 'Item'));
        $purchaseHeader->addColumn(new ExportColumn('Amount', 'amount', 'Amount'));
        $purchaseHeader->addColumn(new ExportColumn('Date', 'date', 'Date'));

        $purchaseTable = new ExportTable('purchases', 'Purchase History', $purchaseHeader, 'customer_1');

        // Add purchase records
        $purchase1 = new ExportRecord();
        $purchase1->addValue('item', 'Laptop', ExportValue::TYPE_STRING);
        $purchase1->addValue('amount', 1299.99, ExportValue::TYPE_FLOAT, '2');
        $purchase1->addValue('date', new \DateTime('2024-01-15'), ExportValue::TYPE_DATE);
        $purchaseTable->addRecord($purchase1);

        $purchase2 = new ExportRecord();
        $purchase2->addValue('item', 'Mouse', ExportValue::TYPE_STRING);
        $purchase2->addValue('amount', 29.99, ExportValue::TYPE_FLOAT, '2');
        $purchase2->addValue('date', new \DateTime('2024-01-20'), ExportValue::TYPE_DATE);
        $purchaseTable->addRecord($purchase2);

        // Create table collection and add to record
        $tableCollection = new ExportTableCollection();
        $tableCollection->addTable($purchaseTable);
        $record->setTableCollection($tableCollection);

        // Add record to export data
        $exportData->addRecord($record);

        // Enable tables and export
        $exportData->enableTables();

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('manual_table_export.xlsx', 'xlsx', $exportData);
    }

    /**
     * Example 3: Streaming export with tables
     */
    public static function streamingTableExport(\Registry $registry, string $factoryClass, array $filters, \Outlook $outlook)
    {
        $dataFactory = new \Nzoom\Export\DataFactory($registry);

        // Configure table provider for auto-discovery
        $dataFactory->withModelTableProvider('order_ref', 'Order Reference', [
            'include_empty_tables' => false,
            'date_format' => 'd/m/Y'
        ]);

        // Create streaming export data with tables
        $exportData = $dataFactory->createStreaming($factoryClass, $filters, $outlook, 500);

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('streaming_with_tables.xlsx', 'xlsx', $exportData);
    }

    /**
     * Example 4: Conditional table inclusion (UPDATED)
     */
    public static function conditionalTableExport(\Registry $registry, array $models, \Outlook $outlook)
    {
        $dataFactory = new \Nzoom\Export\DataFactory($registry);

        // Enable tables only if requested
        $includeDetailedData = $_GET['include_details'] ?? false;

        if ($includeDetailedData) {
            // Configure table provider for auto-discovery
            $dataFactory->withModelTableProvider('full_num', 'Document Number', [
                'include_empty_tables' => false,
                'date_format' => 'd.m.Y'
            ]);
        }

        // Create export data (tables included if configured above)
        $exportData = $dataFactory($models, $outlook);

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $filename = $includeDetailedData ? 'detailed_export.xlsx' : 'basic_export.xlsx';
        $adapter->export($filename, 'xlsx', $exportData);
    }

    /**
     * Example 5: Table validation and error handling (UPDATED)
     */
    public static function tableValidationExample(\Registry $registry, array $models, \Outlook $outlook)
    {
        $dataFactory = new \Nzoom\Export\DataFactory($registry);

        // Configure table provider for validation
        $tableConfig = [
            'table_types' => [
                'orders' => [
                    'name' => 'Order History',
                    'relation_method' => 'getOrders',
                    'columns' => [
                        ['var_name' => 'order_id', 'label' => 'Order ID', 'field' => 'id', 'type' => 'integer']
                    ]
                ]
            ]
        ];

        $tableProvider = new ModelTableProvider($registry, $tableConfig);
        $validModels = [];
        $invalidModels = [];

        // Validate models before processing
        foreach ($models as $model) {
            if ($tableProvider->validateRecord($model, ['orders'])) {
                $validModels[] = $model;
            } else {
                $invalidModels[] = $model;
            }
        }

        if (!empty($invalidModels)) {
            if (isset($registry['logger'])) {
                $registry['logger']->warn(
                    'Some models cannot provide required table data: ' . count($invalidModels) . ' models excluded'
                );
            }
        }

        // Process only valid models
        if (!empty($validModels)) {
            // Configure table provider for auto-discovery
            $dataFactory->withModelTableProvider('full_num', 'Document Number', ['include_empty_tables' => false]);
            $exportData = $dataFactory($validModels, $outlook);

            $adapter = new ExcelExportFormatAdapter($registry, 'orders', 'export');
            $adapter->export('validated_export.xlsx', 'xlsx', $exportData);
        }
    }

    /**
     * Example 6: Configuration-based approach for different export types
     */
    public static function configBasedExport(\Registry $registry, array $models, \Outlook $outlook, string $exportType)
    {
        $dataFactory = new \Nzoom\Export\DataFactory($registry);

        // Define different table configurations for different export types
        $tableConfigs = [
            'basic' => [], // No tables
            'detailed' => [
                'table_types' => [
                    'purchases' => [
                        'name' => 'Purchase History',
                        'relation_method' => 'getPurchases',
                        'columns' => [
                            ['var_name' => 'item', 'label' => 'Item', 'field' => 'item_name'],
                            ['var_name' => 'amount', 'label' => 'Amount', 'field' => 'amount', 'type' => 'float']
                        ]
                    ]
                ]
            ],
            'comprehensive' => [
                'table_types' => [
                    'purchases' => [
                        'name' => 'Purchase History',
                        'relation_method' => 'getPurchases',
                        'columns' => [
                            ['var_name' => 'item', 'label' => 'Item', 'field' => 'item_name'],
                            ['var_name' => 'amount', 'label' => 'Amount', 'field' => 'amount', 'type' => 'float']
                        ]
                    ],
                    'contacts' => [
                        'name' => 'Contact Information',
                        'relation_method' => 'getContacts',
                        'columns' => [
                            ['var_name' => 'type', 'label' => 'Type', 'field' => 'contact_type'],
                            ['var_name' => 'value', 'label' => 'Value', 'field' => 'contact_value']
                        ]
                    ],
                    'notes' => [
                        'name' => 'Notes',
                        'relation_method' => 'getNotes',
                        'columns' => [
                            ['var_name' => 'note', 'label' => 'Note', 'field' => 'note_text'],
                            ['var_name' => 'date', 'label' => 'Date', 'field' => 'created_at', 'type' => 'date']
                        ]
                    ]
                ],
                'max_records_per_table' => 200
            ]
        ];

        $tableConfig = $tableConfigs[$exportType] ?? [];

        if (!empty($tableConfig)) {
            $dataFactory->withModelTableProvider('customer_id', 'Customer ID', $tableConfig);
        }

        $exportData = $dataFactory($models, $outlook);

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export("export_{$exportType}.xlsx", 'xlsx', $exportData);
    }
}
