<?php

require_once __DIR__ . '/../../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * Verification script for hidden ID column functionality
 */
class VerifyHiddenIdColumn
{
    public static function run()
    {
        echo "Verifying hidden ID column implementation...\n";
        echo "==========================================\n\n";

        $files = [
            '/tmp/test_hidden_id_no_enum.xlsx' => 'Without enumeration',
            '/tmp/test_hidden_id_with_enum.xlsx' => 'With enumeration'
        ];

        foreach ($files as $filename => $description) {
            echo "Checking: $description ($filename)\n";
            echo str_repeat("-", 50) . "\n";

            if (!file_exists($filename)) {
                echo "❌ File not found: $filename\n\n";
                continue;
            }

            try {
                $spreadsheet = IOFactory::load($filename);
                $worksheet = $spreadsheet->getActiveSheet();

                // Check if column A is hidden
                $columnA = $worksheet->getColumnDimension('A');
                $isHidden = !$columnA->getVisible();
                echo "✓ Column A hidden: " . ($isHidden ? "YES" : "NO") . "\n";

                // Check column A header
                $headerA = $worksheet->getCell('A1')->getValue();
                echo "✓ Column A header: '$headerA'\n";

                // Check first few data rows in column A
                echo "✓ Column A data:\n";
                for ($row = 2; $row <= 6; $row++) {
                    $cellValue = $worksheet->getCell("A$row")->getValue();
                    echo "   Row $row: '$cellValue'\n";
                }

                // Check named ranges
                $namedRanges = $spreadsheet->getNamedRanges();
                $hasRecordIdRange = false;
                foreach ($namedRanges as $namedRange) {
                    if ($namedRange->getName() === 'record_id') {
                        $hasRecordIdRange = true;
                        $rangeValue = $namedRange->getRange();
                        echo "✓ Named range 'record_id': $rangeValue\n";
                        break;
                    }
                }
                if (!$hasRecordIdRange) {
                    echo "❌ Named range 'record_id' not found\n";
                }

                // Check data column positions
                $headerB = $worksheet->getCell('B1')->getValue();
                echo "✓ Column B header: '$headerB'\n";

                if (strpos($description, 'enumeration') !== false) {
                    $headerC = $worksheet->getCell('C1')->getValue();
                    echo "✓ Column C header: '$headerC'\n";
                }

                echo "✅ Verification completed for $description\n\n";

            } catch (Exception $e) {
                echo "❌ Error reading file: " . $e->getMessage() . "\n\n";
            }
        }

        echo "Verification complete!\n";
    }
}

// Run the verification if this file is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    VerifyHiddenIdColumn::run();
}
