<?php

require_once __DIR__ . '/../../../vendor/autoload.php';
require_once __DIR__ . '/../../../inc/common/registry.class.php';

use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Adapter\ExcelExportFormatAdapter;

/**
 * Test script for hidden ID column functionality in Excel export
 */
class TestHiddenIdColumn
{
    public static function run()
    {
        echo "Testing hidden ID column in Excel export...\n";

        try {
            // Create a proper Registry instance
            $registry = new Registry();
            $registry->set('logger', null);

            // Create Excel adapter
            $adapter = new ExcelExportFormatAdapter($registry, 'test', 'test');

            // Create test data with ID metadata
            $exportData = self::createTestData();

            // Test without enumeration
            echo "Testing without enumeration column...\n";
            $filename1 = '/tmp/test_hidden_id_no_enum.xlsx';
            $adapter->export($filename1, 'xlsx', $exportData, [
                'include_enumeration' => false
            ]);
            echo "Created: $filename1\n";

            // Test with enumeration
            echo "Testing with enumeration column...\n";
            $filename2 = '/tmp/test_hidden_id_with_enum.xlsx';
            $adapter->export($filename2, 'xlsx', $exportData, [
                'include_enumeration' => true
            ]);
            echo "Created: $filename2\n";

            echo "Test completed successfully!\n";
            echo "Check the generated files to verify:\n";
            echo "1. Column A contains record IDs and is hidden\n";
            echo "2. Named range 'record_id' points to A1\n";
            echo "3. Data columns start from B (no enum) or C (with enum)\n";

        } catch (Exception $e) {
            echo "Test failed: " . $e->getMessage() . "\n";
            echo $e->getTraceAsString() . "\n";
        }
    }

    private static function createTestData(): ExportData
    {
        // Create header
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('email', 'Email', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('age', 'Age', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('created_at', 'Created At', ExportValue::TYPE_DATETIME));

        // Create export data
        $exportData = new ExportData('TestModel', $header);

        // Create test records with different ID metadata patterns
        $testRecords = [
            ['id' => 101, 'name' => 'John Doe', 'email' => '<EMAIL>', 'age' => 30, 'created_at' => '2024-01-15 10:30:00'],
            ['record_id' => 102, 'name' => 'Jane Smith', 'email' => '<EMAIL>', 'age' => 25, 'created_at' => '2024-01-16 11:45:00'],
            ['row_id' => 103, 'name' => 'Bob Johnson', 'email' => '<EMAIL>', 'age' => 35, 'created_at' => '2024-01-17 09:15:00'],
            ['primary_id' => 104, 'name' => 'Alice Brown', 'email' => '<EMAIL>', 'age' => 28, 'created_at' => '2024-01-18 14:20:00'],
            ['other_field' => 'no_id', 'name' => 'Charlie Wilson', 'email' => '<EMAIL>', 'age' => 42, 'created_at' => '2024-01-19 16:30:00']
        ];

        foreach ($testRecords as $recordData) {
            // Extract ID fields for metadata
            $metadata = [];
            foreach (['id', 'record_id', 'row_id', 'primary_id'] as $idField) {
                if (isset($recordData[$idField])) {
                    $metadata[$idField] = $recordData[$idField];
                }
            }
            if (isset($recordData['other_field'])) {
                $metadata['other_field'] = $recordData['other_field'];
            }

            // Create record with metadata
            $record = new ExportRecord($metadata);
            $record->addValue('name', $recordData['name'], ExportValue::TYPE_STRING);
            $record->addValue('email', $recordData['email'], ExportValue::TYPE_STRING);
            $record->addValue('age', $recordData['age'], ExportValue::TYPE_INTEGER);
            $record->addValue('created_at', $recordData['created_at'], ExportValue::TYPE_DATETIME);

            $exportData->addRecord($record);
        }

        return $exportData;
    }
}

// Run the test if this file is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    TestHiddenIdColumn::run();
}
